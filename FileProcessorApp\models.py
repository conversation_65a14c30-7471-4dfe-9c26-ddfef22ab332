from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
import os


def upload_to_path(instance, filename):
    """Generate upload path for files"""
    return f'uploads/{instance.upload_date.strftime("%Y/%m/%d")}/{filename}'


class UploadedFile(models.Model):
    """Model to store uploaded files and their metadata"""
    
    FILE_TYPES = [
        ('pdf', 'PDF Document'),
        ('csv', 'CSV File'),
        ('xlsx', 'Excel File'),
        ('xls', 'Excel File (Legacy)'),
        ('jpg', 'JPEG Image'),
        ('jpeg', 'JPEG Image'),
        ('png', 'PNG Image'),
        ('tiff', 'TIFF Image'),
        ('txt', 'Text File'),
        ('docx', 'Word Document'),
        ('doc', 'Word Document (Legacy)'),
    ]
    
    PROCESSING_STATUS = [
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
    ]
    
    # Basic file information
    file = models.FileField(upload_to=upload_to_path, max_length=500)
    original_filename = models.CharField(max_length=255)
    file_type = models.CharField(max_length=10, choices=FILE_TYPES)
    file_size = models.BigIntegerField(help_text="File size in bytes")
    
    # Upload metadata
    upload_date = models.DateTimeField(default=timezone.now)
    uploaded_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='uploaded_files')
    
    # Processing information
    processing_status = models.CharField(max_length=20, choices=PROCESSING_STATUS, default='pending')
    processing_started_at = models.DateTimeField(null=True, blank=True)
    processing_completed_at = models.DateTimeField(null=True, blank=True)
    processing_error = models.TextField(null=True, blank=True)
    
    # Extracted content
    extracted_text = models.TextField(null=True, blank=True, help_text="Raw text extracted from file")
    ai_analysis = models.TextField(null=True, blank=True, help_text="AI analysis of the content")
    
    # GTFS generation
    gtfs_generated = models.BooleanField(default=False)
    gtfs_generation_date = models.DateTimeField(null=True, blank=True)
    gtfs_files_path = models.CharField(max_length=500, null=True, blank=True, help_text="Path to generated GTFS files")
    
    class Meta:
        ordering = ['-upload_date']
        verbose_name = "Uploaded File"
        verbose_name_plural = "Uploaded Files"
    
    def __str__(self):
        return f"{self.original_filename} - {self.get_processing_status_display()}"
    
    @property
    def file_extension(self):
        """Get file extension from filename"""
        return os.path.splitext(self.original_filename)[1].lower().lstrip('.')
    
    def get_processing_duration(self):
        """Calculate processing duration if completed"""
        if self.processing_started_at and self.processing_completed_at:
            return self.processing_completed_at - self.processing_started_at
        return None


class GTFSDataset(models.Model):
    """Model to store generated GTFS datasets"""

    # Link to source file
    source_file = models.ForeignKey(UploadedFile, on_delete=models.CASCADE, related_name='gtfs_datasets')

    # Dataset metadata
    dataset_name = models.CharField(max_length=255)
    description = models.TextField(null=True, blank=True)
    creation_date = models.DateTimeField(default=timezone.now)

    # GTFS files paths
    gtfs_zip_file = models.FileField(upload_to='gtfs_datasets/', null=True, blank=True)
    agency_file = models.FileField(upload_to='gtfs_files/', null=True, blank=True)
    routes_file = models.FileField(upload_to='gtfs_files/', null=True, blank=True)
    trips_file = models.FileField(upload_to='gtfs_files/', null=True, blank=True)
    stops_file = models.FileField(upload_to='gtfs_files/', null=True, blank=True)
    stop_times_file = models.FileField(upload_to='gtfs_files/', null=True, blank=True)
    calendar_file = models.FileField(upload_to='gtfs_files/', null=True, blank=True)
    calendar_dates_file = models.FileField(upload_to='gtfs_files/', null=True, blank=True)

    # Optional GTFS files
    fare_attributes_file = models.FileField(upload_to='gtfs_files/', null=True, blank=True)
    fare_rules_file = models.FileField(upload_to='gtfs_files/', null=True, blank=True)
    shapes_file = models.FileField(upload_to='gtfs_files/', null=True, blank=True)
    frequencies_file = models.FileField(upload_to='gtfs_files/', null=True, blank=True)
    transfers_file = models.FileField(upload_to='gtfs_files/', null=True, blank=True)

    # Validation status
    is_valid = models.BooleanField(default=False)
    validation_report = models.TextField(null=True, blank=True)
    validation_date = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ['-creation_date']
        verbose_name = "GTFS Dataset"
        verbose_name_plural = "GTFS Datasets"

    def __str__(self):
        return f"{self.dataset_name} - {self.creation_date.strftime('%Y-%m-%d')}"


class ExtractedTransitData(models.Model):
    """Model to store structured transit data extracted from files"""

    DATA_TYPES = [
        ('agency', 'Agency Information'),
        ('route', 'Route Information'),
        ('stop', 'Stop Information'),
        ('trip', 'Trip Information'),
        ('schedule', 'Schedule Information'),
        ('fare', 'Fare Information'),
        ('other', 'Other Transit Data'),
    ]

    # Link to source file
    source_file = models.ForeignKey(UploadedFile, on_delete=models.CASCADE, related_name='extracted_data')

    # Data classification
    data_type = models.CharField(max_length=20, choices=DATA_TYPES)
    confidence_score = models.FloatField(default=0.0, help_text="AI confidence in data extraction (0-1)")

    # Extracted data (stored as JSON)
    raw_data = models.JSONField(help_text="Raw extracted data in JSON format")
    structured_data = models.JSONField(help_text="Structured data ready for GTFS conversion")

    # Processing metadata
    extraction_date = models.DateTimeField(default=timezone.now)
    extraction_method = models.CharField(max_length=50, default='ai_analysis')

    # Validation
    is_validated = models.BooleanField(default=False)
    validation_notes = models.TextField(null=True, blank=True)

    class Meta:
        ordering = ['-extraction_date']
        verbose_name = "Extracted Transit Data"
        verbose_name_plural = "Extracted Transit Data"

    def __str__(self):
        return f"{self.get_data_type_display()} from {self.source_file.original_filename}"
