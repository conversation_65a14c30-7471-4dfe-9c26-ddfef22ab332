# Generated by Django 4.2.3 on 2023-09-22 06:01

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('BusApp', '0004_busfare'),
    ]

    operations = [
        migrations.CreateModel(
            name='BusFare2',
            fields=[
                ('fare_chart_id', models.AutoField(primary_key=True, serialize=False)),
                ('service_type_id', models.IntegerField()),
                ('passenger_type_id', models.IntegerField()),
                ('schedule_type_id', models.IntegerField()),
                ('start_point_id', models.IntegerField()),
                ('end_point_id', models.IntegerField()),
                ('number_of_kms', models.IntegerField()),
                ('fare_amount', models.FloatField(blank=True, null=True)),
                ('toll_fee', models.FloatField(blank=True, null=True)),
            ],
            options={
                'verbose_name_plural': 'Bus Fare-C',
                'db_table': 'bus_fare2',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='FareChartMaster',
            fields=[
                ('farechart_master_id', models.AutoField(primary_key=True, serialize=False)),
                ('route_id', models.IntegerField()),
                ('service_type_id', models.IntegerField()),
                ('passenger_type_id', models.IntegerField()),
                ('rate_master_id', models.IntegerField()),
                ('route_fare_map_id', models.IntegerField()),
                ('farechart_name', models.CharField(blank=True, max_length=50)),
                ('schedule_service', models.IntegerField()),
                ('percentage', models.IntegerField()),
                ('deleted_status', models.IntegerField()),
                ('ceiling_fare', models.IntegerField()),
                ('nignt_service', models.CharField(blank=True, max_length=5)),
                ('flexi_fare', models.CharField(blank=True, max_length=5)),
            ],
            options={
                'verbose_name_plural': 'Bus Fare Chart Master',
                'db_table': 'farechart_master',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='RateMasterDetails',
            fields=[
                ('rate_master_details_id', models.AutoField(primary_key=True, serialize=False)),
                ('rate_master_id', models.IntegerField()),
                ('service_type_id', models.IntegerField()),
                ('stage_no', models.IntegerField()),
                ('adult', models.IntegerField()),
                ('children', models.IntegerField()),
                ('senior_citizen', models.IntegerField()),
                ('luggage', models.IntegerField()),
                ('happy_hour1', models.IntegerField()),
                ('happy_hour2', models.IntegerField()),
                ('deleted_status', models.IntegerField()),
                ('created_by', models.IntegerField()),
                ('created_date', models.CharField(blank=True, max_length=50)),
                ('updated_by', models.IntegerField()),
                ('updated_date', models.CharField(blank=True, max_length=50)),
                ('sync_updated_date', models.CharField(blank=True, max_length=5)),
            ],
            options={
                'verbose_name_plural': 'Rate Master Details',
                'db_table': 'rate_master_details',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='RateMasterTable',
            fields=[
                ('rate_master_id', models.AutoField(primary_key=True, serialize=False)),
                ('parent_rate_master', models.IntegerField()),
                ('version_number', models.CharField(blank=True, max_length=50)),
                ('version_number_service_stype', models.CharField(blank=True, max_length=50)),
                ('service_type_id', models.IntegerField()),
                ('effective_start_date', models.CharField(blank=True, max_length=5)),
                ('effective_end_date', models.CharField(blank=True, max_length=5, null=True)),
                ('status', models.CharField(blank=True, max_length=50)),
                ('deleted_status', models.IntegerField()),
                ('created_by', models.IntegerField()),
                ('created_date', models.CharField(blank=True, max_length=50)),
                ('updated_by', models.IntegerField()),
                ('updated_date', models.CharField(blank=True, max_length=5)),
                ('sync_updated_date', models.CharField(blank=True, max_length=5)),
            ],
            options={
                'verbose_name_plural': 'Rate Master Table',
                'db_table': 'rate_master',
                'managed': False,
            },
        ),
    ]
