import os
import shutil
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional
from django.conf import settings
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
from django.utils import timezone

from ..models import UploadedFile, GTFSDataset

logger = logging.getLogger(__name__)


class FileManagerService:
    """Service for managing uploaded files and GTFS datasets"""
    
    def __init__(self):
        self.media_root = getattr(settings, 'MEDIA_ROOT', 'media')
        self.upload_path = getattr(settings, 'FILEPROCESSOR_UPLOAD_PATH', 'uploads/')
        self.gtfs_path = getattr(settings, 'FILEPROCESSOR_GTFS_PATH', 'gtfs_datasets/')
        
        # Ensure directories exist
        self._ensure_directories()
    
    def _ensure_directories(self):
        """Ensure required directories exist"""
        directories = [
            os.path.join(self.media_root, self.upload_path),
            os.path.join(self.media_root, self.gtfs_path),
            os.path.join(self.media_root, 'gtfs_files'),
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
            logger.info(f"Ensured directory exists: {directory}")
    
    def get_file_info(self, file_path: str) -> Dict:
        """Get information about a file"""
        try:
            if os.path.exists(file_path):
                stat = os.stat(file_path)
                return {
                    'exists': True,
                    'size': stat.st_size,
                    'modified': datetime.fromtimestamp(stat.st_mtime),
                    'created': datetime.fromtimestamp(stat.st_ctime),
                    'path': file_path
                }
            else:
                return {'exists': False, 'path': file_path}
        except Exception as e:
            logger.error(f"Error getting file info for {file_path}: {e}")
            return {'exists': False, 'error': str(e), 'path': file_path}
    
    def cleanup_old_files(self, days_old: int = 30) -> Dict:
        """Clean up old uploaded files and GTFS datasets"""
        cutoff_date = timezone.now() - timedelta(days=days_old)
        cleanup_stats = {
            'files_deleted': 0,
            'datasets_deleted': 0,
            'space_freed': 0,
            'errors': []
        }
        
        try:
            # Find old uploaded files
            old_files = UploadedFile.objects.filter(
                upload_date__lt=cutoff_date,
                processing_status__in=['completed', 'failed']
            )
            
            for uploaded_file in old_files:
                try:
                    file_size = uploaded_file.file_size
                    
                    # Delete associated GTFS datasets first
                    gtfs_datasets = GTFSDataset.objects.filter(source_file=uploaded_file)
                    for dataset in gtfs_datasets:
                        self._delete_gtfs_dataset_files(dataset)
                        dataset.delete()
                        cleanup_stats['datasets_deleted'] += 1
                    
                    # Delete the uploaded file
                    if uploaded_file.file and default_storage.exists(uploaded_file.file.name):
                        default_storage.delete(uploaded_file.file.name)
                        cleanup_stats['space_freed'] += file_size
                    
                    uploaded_file.delete()
                    cleanup_stats['files_deleted'] += 1
                    
                except Exception as e:
                    error_msg = f"Error deleting file {uploaded_file.id}: {e}"
                    logger.error(error_msg)
                    cleanup_stats['errors'].append(error_msg)
            
            logger.info(f"Cleanup completed: {cleanup_stats}")
            return cleanup_stats
            
        except Exception as e:
            error_msg = f"Error during cleanup: {e}"
            logger.error(error_msg)
            cleanup_stats['errors'].append(error_msg)
            return cleanup_stats
    
    def _delete_gtfs_dataset_files(self, dataset: GTFSDataset):
        """Delete all files associated with a GTFS dataset"""
        file_fields = [
            'gtfs_zip_file', 'agency_file', 'routes_file', 'trips_file',
            'stops_file', 'stop_times_file', 'calendar_file', 'calendar_dates_file',
            'fare_attributes_file', 'fare_rules_file', 'shapes_file',
            'frequencies_file', 'transfers_file'
        ]
        
        for field_name in file_fields:
            file_field = getattr(dataset, field_name, None)
            if file_field and default_storage.exists(file_field.name):
                try:
                    default_storage.delete(file_field.name)
                    logger.info(f"Deleted {field_name} for dataset {dataset.id}")
                except Exception as e:
                    logger.error(f"Error deleting {field_name} for dataset {dataset.id}: {e}")
    
    def get_storage_statistics(self) -> Dict:
        """Get storage usage statistics"""
        stats = {
            'total_uploaded_files': 0,
            'total_gtfs_datasets': 0,
            'total_storage_used': 0,
            'storage_by_type': {},
            'processing_status_counts': {},
            'recent_activity': []
        }
        
        try:
            # Count uploaded files
            uploaded_files = UploadedFile.objects.all()
            stats['total_uploaded_files'] = uploaded_files.count()
            
            # Storage by file type
            for file_type, _ in UploadedFile.FILE_TYPES:
                count = uploaded_files.filter(file_type=file_type).count()
                if count > 0:
                    stats['storage_by_type'][file_type] = count
            
            # Processing status counts
            for status, _ in UploadedFile.PROCESSING_STATUS:
                count = uploaded_files.filter(processing_status=status).count()
                if count > 0:
                    stats['processing_status_counts'][status] = count
            
            # Calculate total storage used
            total_size = sum(f.file_size for f in uploaded_files if f.file_size)
            stats['total_storage_used'] = total_size
            
            # Count GTFS datasets
            stats['total_gtfs_datasets'] = GTFSDataset.objects.count()
            
            # Recent activity (last 7 days)
            recent_date = timezone.now() - timedelta(days=7)
            recent_files = uploaded_files.filter(upload_date__gte=recent_date).order_by('-upload_date')[:10]
            
            stats['recent_activity'] = [
                {
                    'filename': f.original_filename,
                    'upload_date': f.upload_date.isoformat(),
                    'status': f.processing_status,
                    'file_type': f.file_type
                }
                for f in recent_files
            ]
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting storage statistics: {e}")
            return stats
    
    def validate_file_integrity(self, uploaded_file: UploadedFile) -> Dict:
        """Validate the integrity of an uploaded file"""
        validation_result = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'file_info': {}
        }
        
        try:
            if not uploaded_file.file:
                validation_result['is_valid'] = False
                validation_result['errors'].append("No file associated with record")
                return validation_result
            
            file_path = uploaded_file.file.path
            file_info = self.get_file_info(file_path)
            validation_result['file_info'] = file_info
            
            if not file_info['exists']:
                validation_result['is_valid'] = False
                validation_result['errors'].append("File does not exist on disk")
                return validation_result
            
            # Check file size consistency
            if file_info['size'] != uploaded_file.file_size:
                validation_result['warnings'].append(
                    f"File size mismatch: DB={uploaded_file.file_size}, Disk={file_info['size']}"
                )
            
            # Check file extension
            expected_ext = uploaded_file.file_type
            actual_ext = os.path.splitext(file_path)[1].lower().lstrip('.')
            
            if actual_ext != expected_ext:
                validation_result['warnings'].append(
                    f"File extension mismatch: Expected={expected_ext}, Actual={actual_ext}"
                )
            
            return validation_result
            
        except Exception as e:
            validation_result['is_valid'] = False
            validation_result['errors'].append(f"Validation error: {e}")
            return validation_result
    
    def backup_file(self, uploaded_file: UploadedFile, backup_location: str) -> bool:
        """Create a backup of an uploaded file"""
        try:
            if not uploaded_file.file or not default_storage.exists(uploaded_file.file.name):
                logger.error(f"Source file not found for backup: {uploaded_file.id}")
                return False
            
            # Create backup directory
            backup_dir = os.path.join(backup_location, 'file_backups')
            os.makedirs(backup_dir, exist_ok=True)
            
            # Create backup filename with timestamp
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_filename = f"{uploaded_file.id}_{timestamp}_{uploaded_file.original_filename}"
            backup_path = os.path.join(backup_dir, backup_filename)
            
            # Copy file
            with default_storage.open(uploaded_file.file.name, 'rb') as source:
                with open(backup_path, 'wb') as backup:
                    shutil.copyfileobj(source, backup)
            
            logger.info(f"File backed up: {uploaded_file.original_filename} -> {backup_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error backing up file {uploaded_file.id}: {e}")
            return False
    
    def get_disk_usage(self) -> Dict:
        """Get disk usage information for the media directory"""
        try:
            usage = shutil.disk_usage(self.media_root)
            return {
                'total': usage.total,
                'used': usage.used,
                'free': usage.free,
                'percent_used': (usage.used / usage.total) * 100
            }
        except Exception as e:
            logger.error(f"Error getting disk usage: {e}")
            return {'error': str(e)}
