@echo off
echo 🚀 Quick Ollama Setup for FileProcessorApp
echo ==========================================

echo.
echo 📥 Step 1: Download Ollama manually
echo Please visit: https://ollama.ai/download
echo Download and install Ollama for Windows
echo.
pause

echo.
echo 🔄 Step 2: Starting Ollama service...
start /B ollama serve
timeout /t 5 /nobreak >nul

echo.
echo 📦 Step 3: Downloading lightweight AI model...
echo This will download the 'phi' model (~1.6GB)
ollama pull phi

echo.
echo 🧪 Step 4: Testing setup...
echo Testing AI model with a simple prompt...
ollama run phi "Hello, can you analyze transit data?"

echo.
echo ✅ Setup complete!
echo.
echo 📋 Summary:
echo - Ollama service is running
echo - 'phi' AI model is downloaded
echo - FileProcessorApp can now use AI for data extraction
echo.
echo 🌐 Access FileProcessorApp at:
echo   http://localhost:8000/fileprocessor/
echo.
echo 💡 Tip: Keep this window open to keep Ollama running
echo      Or run 'ollama serve' in a new terminal
echo.
pause
