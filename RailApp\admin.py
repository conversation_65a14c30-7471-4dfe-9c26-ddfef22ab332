from django.contrib import admin
import RailApp.models
from django_admin_listfilter_dropdown.filters import DropdownFilter, ChoiceDropdownFilter, RelatedDropdownFilter
from django.urls import reverse, reverse_lazy
from django.utils.html import format_html
from django import forms
from django.contrib import messages
from django.http import HttpResponseRedirect
from django.urls import path
from CityApp.models import Cities
from django import conf
from django.shortcuts import render, redirect

# Register your models here.



class RailRouteAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'line', 'status', 'service', 'direction', 'is_main_route', 'description')
    readonly_fields = ('id',)
    search_fields = ('name', 'line', 'status', 'service', 'direction', 'is_main_route', 'description')
    ordering = ('id',)
    list_per_page = 10
    list_filter = (
        ('name', DropdownFilter),
        ('line', DropdownFilter),
        ('direction', DropdownFilter),
        ('is_main_route', DropdownFilter),
        ('status', DropdownFilter),
    )

    def add_view(self, request, extra_context=None):
        extra_context = {'title': 'Rail Route'}
        try:
            return super(RailRouteAdmin, self).add_view(request, extra_context=extra_context)
        except Exception as e:
            print(e)
            messages.warning(request, 'Invalid Data')
            previous_url = request.META.get('HTTP_REFERER', reverse('admin:index'))
            return HttpResponseRedirect(previous_url) 

    def changelist_view(self, request, extra_context=None):
        extra_context = {'title': 'Rail Route' }
        try:
            return super(RailRouteAdmin, self).changelist_view(request, extra_context=extra_context)
        except Exception as e:
            message = e
            messages.add_message(request, messages.INFO,message)
            previous_url = request.META.get('HTTP_REFERER', reverse('admin:index'))
            return HttpResponseRedirect(previous_url)

    def selectCity(self, request, id: int):
        city = Cities.objects.filter(id=id).first()
        print(city.name)
        db_name = city.db_name
        city_name = city.name
        request.session["db_name"] = db_name
        request.session["city_name"] = city_name

        conf.settings.DATABASES['master']['NAME'] = db_name
        return redirect ('/admin/')


    def select_city(self, obj):
        return format_html('<option value="{{ city.id }}">',
            reverse_lazy("admin:selectCity", args=[obj.id])
            )
    
    def get_urls(self):
        my_urls = [
            path('selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),
            path('add/selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),
            path('change/selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),


        ]
        return my_urls + super().get_urls()
        
admin.site.register(RailApp.models.RailRoute,RailRouteAdmin)

class RailScheduleAdmin(admin.ModelAdmin):
    list_display = ('id', 'get_route_id', 'start_time', 'end_time', 'train_no', 'day_type', 'train_type', 'status', 'service')
    # readonly_fields = ('id','route_id', 'train_no')
    readonly_fields = ('id',)
    search_fields = ('route_id__name', 'start_time', 'train_no', 'day_type', 'train_type', 'status')
    ordering = ('id',)
    list_per_page = 10
    list_filter = (
        # ('route_id', RelatedDropdownFilter),
        ('day_type', DropdownFilter),
        ('train_type', DropdownFilter),
        ('status', DropdownFilter),
    )
    def get_route_id(self, obj):
        name = obj.route_id.name
        idObj = obj.route_id.id
        url = reverse('admin:%s_%s_change' % (obj._meta.app_label,  obj.route_id._meta.model_name), args=[idObj])
        return format_html(u'<a href="%s">%s</a>' % (url,  name))
    get_route_id.short_description = 'Route Name'
    
    def add_view(self, request, extra_context=None):
        extra_context = {'title': 'Rail Schedule'}
        try:
            return super(RailScheduleAdmin, self).add_view(request, extra_context=extra_context)
        except Exception as e:
            print(e)
            messages.warning(request, 'Invalid Data')
            previous_url = request.META.get('HTTP_REFERER', reverse('admin:index'))
            return HttpResponseRedirect(previous_url)

    def changelist_view(self, request, extra_context=None):
        extra_context = {'title': 'Rail Schedule' }
        try:
            return super(RailScheduleAdmin, self).changelist_view(request, extra_context=extra_context)
        except Exception as e:
            message = e
            messages.add_message(request, messages.INFO,message)
            previous_url = request.META.get('HTTP_REFERER', reverse('admin:index'))
            return HttpResponseRedirect(previous_url)

    def selectCity(self, request, id: int):
        city = Cities.objects.filter(id=id).first()
        print(city.name)
        db_name = city.db_name
        city_name = city.name
        request.session["db_name"] = db_name
        request.session["city_name"] = city_name

        conf.settings.DATABASES['master']['NAME'] = db_name
        return redirect ('/admin/')


    def select_city(self, obj):
        return format_html('<option value="{{ city.id }}">',
            reverse_lazy("admin:selectCity", args=[obj.id])
            )
    
    def get_urls(self):
        my_urls = [
            path('selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),
            path('add/selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),
            path('change/selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),


        ]
        return my_urls + super().get_urls()
        
admin.site.register(RailApp.models.RailSchedule,RailScheduleAdmin)

class RailSchedulePointAdmin(admin.ModelAdmin):
    list_display = ('id', 'get_route_id', 'schedule_id', 'get_station_id', 'seq', 'start_time', 'status', 'service', 'platform_no')
    # readonly_fields = ('id','route_id','schedule_id', 'station_id', 'seq')
    readonly_fields = ('id', 'seq')
    search_fields = ('route_id__name','schedule_id', 'station_id__name', 'start_time', 'status')
    ordering = ('id',)
    list_per_page = 10
    list_filter = (
        # ('route_id', RelatedDropdownFilter),
        ('schedule_id', DropdownFilter),
        # ('station_id', RelatedDropdownFilter),
        ('status', DropdownFilter),
    )

    def get_route_id(self, obj):
        name = obj.route_id.name
        idObj = obj.route_id.id
        url = reverse('admin:%s_%s_change' % (obj._meta.app_label,  obj.route_id._meta.model_name), args=[idObj])
        return format_html(u'<a href="%s">%s</a>' % (url,  name))
    get_route_id.short_description = 'Route Name'


    def get_station_id(self, obj):
        name = obj.station_id.name
        idObj = obj.station_id.id
        url = reverse('admin:%s_%s_change' % (obj._meta.app_label,  obj.station_id._meta.model_name), args=[idObj])
        return format_html(u'<a href="%s">%s</a>' % (url,  name))
    get_station_id.short_description = 'Station Name'

    def add_view(self, request, extra_context=None):
        extra_context = {'title': 'RailSchedulePoint'}
        try:
            return super(RailSchedulePointAdmin, self).add_view(request, extra_context=extra_context)
        except Exception as e:
            print(e)
            messages.warning(request, 'Invalid Data')
            previous_url = request.META.get('HTTP_REFERER', reverse('admin:index'))
            return HttpResponseRedirect(previous_url)

    def changelist_view(self, request, extra_context=None):
        extra_context = {'title': 'Rail Schedule Point'}
        try:
            return super(RailSchedulePointAdmin, self).changelist_view(request, extra_context=extra_context)
        except Exception as e:
            message = e
            messages.add_message(request, messages.INFO,message)
            previous_url = request.META.get('HTTP_REFERER', reverse('admin:index'))
            return HttpResponseRedirect(previous_url)

    def selectCity(self, request, id: int):
        city = Cities.objects.filter(id=id).first()
        print(city.name)
        db_name = city.db_name
        city_name = city.name
        request.session["db_name"] = db_name
        request.session["city_name"] = city_name

        conf.settings.DATABASES['master']['NAME'] = db_name
        return redirect ('/admin/')


    def select_city(self, obj):
        return format_html('<option value="{{ city.id }}">',
            reverse_lazy("admin:selectCity", args=[obj.id])
            )
    
    def get_urls(self):
        my_urls = [
            path('selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),
            path('add/selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),
            path('change/selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),


        ]
        return my_urls + super().get_urls()
        
admin.site.register(RailApp.models.RailSchedulePoint,RailSchedulePointAdmin)

class RailStationAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'latitude', 'longitude', 'status', 'service', 'code')
    readonly_fields = ('id',)
    search_fields = ('name', 'latitude','longitude', 'status')
    ordering = ('id',)
    list_per_page = 10
    list_filter = (
        ('name', DropdownFilter),
        ('status', DropdownFilter),
    )

    def add_view(self, request, extra_context=None):
        extra_context = {'title': 'RailStations'}
        try:
            return super(RailStationAdmin, self).add_view(request, extra_context=extra_context)
        except Exception as e:
            print(e)
            messages.warning(request, 'Invalid Data')
            previous_url = request.META.get('HTTP_REFERER', reverse('admin:index'))
            return HttpResponseRedirect(previous_url)

    def changelist_view(self, request, extra_context=None):
        extra_context = {'title': 'Rail Stations'}
        try:
            return super(RailStationAdmin, self).changelist_view(request, extra_context=extra_context)
        except Exception as e:
            message = e
            messages.add_message(request, messages.INFO,message)
            previous_url = request.META.get('HTTP_REFERER', reverse('admin:index'))
            return HttpResponseRedirect(previous_url)

    def selectCity(self, request, id: int):
        city = Cities.objects.filter(id=id).first()
        print(city.name)
        db_name = city.db_name
        city_name = city.name
        request.session["db_name"] = db_name
        request.session["city_name"] = city_name

        conf.settings.DATABASES['master']['NAME'] = db_name
        return redirect ('/admin/')


    def select_city(self, obj):
        return format_html('<option value="{{ city.id }}">',
            reverse_lazy("admin:selectCity", args=[obj.id])
            )
    
    def get_urls(self):
        my_urls = [
            path('selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),
            path('add/selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),
            path('change/selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),


        ]
        return my_urls + super().get_urls()
        
admin.site.register(RailApp.models.RailStation,RailStationAdmin)


class RailStationCrossAdmin(admin.ModelAdmin):
    list_display = ('line', 'station_id', 'is_cross')
    readonly_fields = ('station_id',)
    search_fields = ('line', 'station_id', 'is_cross')
    ordering = ('line','station_id',)
    list_per_page = 10
    list_filter = (
        ('is_cross', DropdownFilter),
    )

    def add_view(self, request, extra_context=None):
        extra_context = {'title': 'RailStationCross'}
        try:
            return super(RailStationCrossAdmin, self).add_view(request, extra_context=extra_context)
        except Exception as e:
            print(e)
            messages.warning(request, 'Invalid Data')
            previous_url = request.META.get('HTTP_REFERER', reverse('admin:index'))
            return HttpResponseRedirect(previous_url)

    def changelist_view(self, request, extra_context=None):
        extra_context = {'title': 'Rail Station Cross'}
        try:
            return super(RailStationCrossAdmin, self).changelist_view(request, extra_context=extra_context)
        except Exception as e:
            message = e
            messages.add_message(request, messages.INFO,message)
            previous_url = request.META.get('HTTP_REFERER', reverse('admin:index'))
            return HttpResponseRedirect(previous_url)

    def selectCity(self, request, id: int):
        city = Cities.objects.filter(id=id).first()
        print(city.name)
        db_name = city.db_name
        city_name = city.name
        request.session["db_name"] = db_name
        request.session["city_name"] = city_name

        conf.settings.DATABASES['master']['NAME'] = db_name
        return redirect ('/admin/')


    def select_city(self, obj):
        return format_html('<option value="{{ city.id }}">',
            reverse_lazy("admin:selectCity", args=[obj.id])
            )
    
    def get_urls(self):
        my_urls = [
            path('selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),
            path('add/selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),
            path('change/selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),


        ]
        return my_urls + super().get_urls()

# admin.site.register(RailApp.models.RailStationCross,RailStationCrossAdmin)


class RailLineMasterAdmin(admin.ModelAdmin):
    list_display = ('id', 'line', 'station_id', 'order')
    readonly_fields = ('id',)
    search_fields = ('line', 'station_id', 'order')
    ordering = ('id',)
    list_per_page = 10
    list_filter = (
        ('order', DropdownFilter),
    )

    def add_view(self, request, extra_context=None):
        extra_context = {'title': 'RailLineMaster'}
        try:
            return super(RailLineMasterAdmin, self).add_view(request, extra_context=extra_context)
        except Exception as e:
            print(e)
            messages.warning(request, 'Invalid Data')
            previous_url = request.META.get('HTTP_REFERER', reverse('admin:index'))
            return HttpResponseRedirect(previous_url)

    def changelist_view(self, request, extra_context=None):
        extra_context = {'title': 'Rail Line Master'}
        try:
            return super(RailLineMasterAdmin, self).changelist_view(request, extra_context=extra_context)
        except Exception as e:
            message = e
            messages.add_message(request, messages.INFO,message)
            previous_url = request.META.get('HTTP_REFERER', reverse('admin:index'))
            return HttpResponseRedirect(previous_url)

    def selectCity(self, request, id: int):
        city = Cities.objects.filter(id=id).first()
        print(city.name)
        db_name = city.db_name
        city_name = city.name
        request.session["db_name"] = db_name
        request.session["city_name"] = city_name

        conf.settings.DATABASES['master']['NAME'] = db_name
        return redirect ('/admin/')


    def select_city(self, obj):
        return format_html('<option value="{{ city.id }}">',
            reverse_lazy("admin:selectCity", args=[obj.id])
            )
    
    def get_urls(self):
        my_urls = [
            path('selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),
            path('add/selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),
            path('change/selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),


        ]
        return my_urls + super().get_urls()
        
admin.site.register(RailApp.models.RailLineMaster,RailLineMasterAdmin)


class RailFareAdmin(admin.ModelAdmin):
    list_display = ('rail_fare_id', 'from_distance', 'to_distance', 'adult_fare', 'child_fare')
    readonly_fields = ('rail_fare_id',)
    search_fields = ('rail_fare_id',)
    ordering = ('rail_fare_id',)
    list_per_page = 10
    list_filter = (
        ('rail_fare_id', DropdownFilter),
    )

    def add_view(self, request, extra_context=None):
        extra_context = {'title': 'RailFare'}
        try:
            return super(RailFareAdmin, self).add_view(request, extra_context=extra_context)
        except Exception as e:
            print(e)
            messages.warning(request, 'Invalid Data')
            previous_url = request.META.get('HTTP_REFERER', reverse('admin:index'))
            return HttpResponseRedirect(previous_url) 

    def changelist_view(self, request, extra_context=None):
        extra_context = {'title': 'Rail Fare'}
        try:
            return super(RailFareAdmin, self).changelist_view(request, extra_context=extra_context)
        except Exception as e:
            message = e
            messages.add_message(request, messages.INFO,message)
            previous_url = request.META.get('HTTP_REFERER', reverse('admin:index'))
            return HttpResponseRedirect(previous_url)

    def selectCity(self, request, id: int):
        city = Cities.objects.filter(id=id).first()
        print(city.name)
        db_name = city.db_name
        city_name = city.name
        request.session["db_name"] = db_name
        request.session["city_name"] = city_name

        conf.settings.DATABASES['master']['NAME'] = db_name
        return redirect ('/admin/')


    def select_city(self, obj):
        return format_html('<option value="{{ city.id }}">',
            reverse_lazy("admin:selectCity", args=[obj.id])
            )
    
    def get_urls(self):
        my_urls = [
            path('selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),
            path('add/selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),
            path('change/selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),


        ]
        return my_urls + super().get_urls()
        
admin.site.register(RailApp.models.RailFare,RailFareAdmin)


class RailFareDistancePointAdmin(admin.ModelAdmin):
    list_display = ('rail_fare_distance_point_id', 'schedule_point_id', 'route_id', 'schedule_id', 'station_id', 'seq', 'start_time', 'distance', 'status','service')
    readonly_fields = ('rail_fare_distance_point_id',)
    search_fields = ('rail_fare_distance_point_id',)
    ordering = ('rail_fare_distance_point_id',)
    list_per_page = 10
    list_filter = (
        ('rail_fare_distance_point_id', DropdownFilter),
    )

    def add_view(self, request, extra_context=None):
        extra_context = {'title': 'Rail Fare Distance Point'}
        try:
            return super(RailFareDistancePointAdmin, self).add_view(request, extra_context=extra_context)
        except Exception as e:
            print(e)
            messages.warning(request, 'Invalid Data')
            previous_url = request.META.get('HTTP_REFERER', reverse('admin:index'))
            return HttpResponseRedirect(previous_url) 

    def changelist_view(self, request, extra_context=None):
        extra_context = {'title': 'Rail Fare Distance Point'}
        try:
            return super(RailFareDistancePointAdmin, self).changelist_view(request, extra_context=extra_context)
        except Exception as e:
            message = e
            messages.add_message(request, messages.INFO,message)
            previous_url = request.META.get('HTTP_REFERER', reverse('admin:index'))
            return HttpResponseRedirect(previous_url)

    def selectCity(self, request, id: int):
        city = Cities.objects.filter(id=id).first()
        print(city.name)
        db_name = city.db_name
        city_name = city.name
        request.session["db_name"] = db_name
        request.session["city_name"] = city_name

        conf.settings.DATABASES['master']['NAME'] = db_name
        return redirect ('/admin/')


    def select_city(self, obj):
        return format_html('<option value="{{ city.id }}">',
            reverse_lazy("admin:selectCity", args=[obj.id])
            )
    
    def get_urls(self):
        my_urls = [
            path('selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),
            path('add/selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),
            path('change/selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),


        ]
        return my_urls + super().get_urls()
        
admin.site.register(RailApp.models.RailFareDistancePoint,RailFareDistancePointAdmin)