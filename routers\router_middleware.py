from CityApp.models import Cities
from django.contrib import admin
from BusApp.models import RouteVehicleMap,BusFare,BusFare2, FareChartMaster, RateMasterDetails, RateMasterTable
from BusApp.admin import RouteVehicleMapAdmin,BusFareAdmin,BusFare2Admin, FareChartMasterAdmin, RateMasterDetailsAdmin, RateMasterTableAdmin  
from RailApp.models import RailRoute,RailSchedule,RailSchedulePoint,RailStation,RailStationCross,RailLineMaster, RailFareDistancePoint, RailFare
from RailApp.admin import RailRouteAdmin,RailScheduleAdmin,RailSchedulePointAdmin,RailStationAdmin,RailStationCrossAdmin,RailLineMasterAdmin, RailFareDistancePointAdmin, RailFareAdmin
from MetroApp.models import MetroFare,MetroRoute,Station,Times
from MetroApp.admin import MetroFareAdmin,MetroRouteAdmin,StationAdmin,TimesAdmin
# from ImportApp.models import Imports
# from ImportApp.admin import ImportsAdmin
import django.conf as conf

class RouterMiddleware(object):
    def __init__(self, get_response):
        """
        One-time configuration and initialisation.
        """
        self.get_response = get_response

    def __call__(self, request):
        """
        Code to be executed for each request before the view (and later
        middleware) are called.
        """
        if request.user.id:
            db_name = "adapter_bang"
            requiredPerm = 'CityApp' + '.is_city_banglore'
            if request.user.has_perm(requiredPerm):
                db_name = "adapter_bang"

            if "db_name" in request.session.keys():
                db_name = request.session["db_name"]
            else:
                request.session["db_name"] = db_name
            conf.settings.DATABASES['master']['NAME'] = db_name
            
            
            city = Cities.objects.get(db_name = db_name)
            
            if db_name != "adapter_trivan":
                if (admin.site.is_registered(RouteVehicleMap) == False ):
                    pass
                else:
                    admin.site.unregister(RouteVehicleMap)
            else:
                if (admin.site.is_registered(RouteVehicleMap) == True ):
                    pass
                else:
                    admin.site.register(RouteVehicleMap,RouteVehicleMapAdmin)

            if db_name not in ["adapter_bbsr", "adapter_del", "adapter_indore", "adapter_mum"]:
                if admin.site.is_registered(BusFare):
                    admin.site.unregister(BusFare)
            else:
                if not admin.site.is_registered(BusFare):
                    admin.site.register(BusFare, BusFareAdmin)
                    
            if db_name != "adapter_bang":
                if (admin.site.is_registered(BusFare2) == False and admin.site.is_registered(FareChartMaster) == False):
                    pass
                else:
                    admin.site.unregister(BusFare2)
                    admin.site.unregister(FareChartMaster)

            else:
                if (admin.site.is_registered(BusFare2) == True and admin.site.is_registered(FareChartMaster) == True):
                    pass
                else:
                    admin.site.register(BusFare2,BusFare2Admin)
                    admin.site.register(FareChartMaster,FareChartMasterAdmin)

            if db_name not in ["adapter_bang", "adapter_pune"]:
                if admin.site.is_registered(RateMasterDetails):
                    admin.site.unregister(RateMasterDetails)
            else:
                if not admin.site.is_registered(RateMasterDetails):
                    admin.site.register(RateMasterDetails, RateMasterDetailsAdmin)

            if db_name not in ["adapter_bang", "adapter_pune"]:
                if admin.site.is_registered(RateMasterTable):
                    admin.site.unregister(RateMasterTable)
            else:
                if not admin.site.is_registered(RateMasterTable):
                    admin.site.register(RateMasterTable, RateMasterTableAdmin)

            if city.is_metro == False:
                if (admin.site.is_registered(MetroFare) == False and admin.site.is_registered(MetroRoute) == False and admin.site.is_registered(Station) == False and admin.site.is_registered(Times) == False):
                    pass
                else:
                    admin.site.unregister(MetroFare)
                    admin.site.unregister(MetroRoute)
                    admin.site.unregister(Station)
                    admin.site.unregister(Times)
            else:
                if (admin.site.is_registered(MetroFare) == True and admin.site.is_registered(MetroRoute) == True and admin.site.is_registered(Station) == True and admin.site.is_registered(Times) == True):
                    pass
                else:
                    admin.site.register(MetroFare,MetroFareAdmin)
                    admin.site.register(MetroRoute,MetroRouteAdmin)
                    admin.site.register(Station,StationAdmin)
                    admin.site.register(Times,TimesAdmin)


            if db_name not in ["adapter_pune", "adapter_chennai", "adapter_mum"]:
                if admin.site.is_registered(RailLineMaster):
                    admin.site.unregister(RailLineMaster)
            else:
                if not admin.site.is_registered(RailLineMaster):
                    admin.site.register(RailLineMaster, RailLineMasterAdmin)

            # if db_name not in ["adapter_bang","adapter_pune", "adapter_chennai", "adapter_mum"]:
            #     if admin.site.is_registered(RailStationCross):
            #         admin.site.unregister(RailStationCross)
            # else:
            #     if not admin.site.is_registered(RailStationCross):
            #         admin.site.register(RailStationCross, RailStationCrossAdmin)



            if db_name != "adapter_chennai":
                if admin.site.is_registered(RailFareDistancePoint) and admin.site.is_registered(RailFare):
                    admin.site.unregister(RailFareDistancePoint)
                    admin.site.unregister(RailFare)
            else:
                if not admin.site.is_registered(RailFareDistancePoint) and admin.site.is_registered(RailFare):
                    admin.site.register(RailFareDistancePoint, RailFareDistancePointAdmin)
                    admin.site.register(RailFare, RailFareAdmin)

            
            if db_name != "adapter_pune":
                if admin.site.is_registered(RailFareDistancePoint):
                    admin.site.unregister(RailFareDistancePoint)
                if admin.site.is_registered(RailFare):
                    admin.site.unregister(RailFare)
                
            else:
                if not admin.site.is_registered(RailFareDistancePoint):
                    admin.site.register(RailFareDistancePoint, RailFareDistancePointAdmin)
                if not admin.site.is_registered(RailFare):
                    admin.site.register(RailFare, RailFareAdmin)
                



            if city.is_rail == False:
                if (admin.site.is_registered(RailRoute) == False and admin.site.is_registered(RailSchedule) == False and admin.site.is_registered(RailSchedulePoint) == False and admin.site.is_registered(RailStation) == False):
                    pass
                else:
                    admin.site.unregister(RailRoute)
                    admin.site.unregister(RailSchedule)
                    admin.site.unregister(RailSchedulePoint)
                    admin.site.unregister(RailStation)
            else:
                if (admin.site.is_registered(RailRoute) == True and admin.site.is_registered(RailSchedule) == True and admin.site.is_registered(RailSchedulePoint) == True and admin.site.is_registered(RailStation)== True):
                    pass
                else:
                    admin.site.register(RailRoute,RailRouteAdmin)
                    admin.site.register(RailSchedule,RailScheduleAdmin)
                    admin.site.register(RailSchedulePoint,RailSchedulePointAdmin)
                    admin.site.register(RailStation,RailStationAdmin)
                    

            

        response = self.get_response(request)
        return response

    def process_view(self, request, view_func, view_args, view_kwargs):
        """
        Called just before Django calls the view.
        """
        return None

    def process_exception(self, request, exception):
        """
        Called when a view raises an exception.
        """
        print(str(exception))
        return None

    def process_template_response(self, request, response):
        """
        Called just after the view has finished executing.
        """
        return response