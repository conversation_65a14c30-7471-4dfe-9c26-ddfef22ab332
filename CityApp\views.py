from django.shortcuts import render
from django.contrib.auth import authenticate, login, logout
from django.db import IntegrityError
from django.http import HttpResponse, HttpResponseRedirect
from django.shortcuts import render
from django.urls import reverse
from django.contrib.auth.decorators import login_required, permission_required
from CityApp.models import Cities
import MetroApp.models, BusApp.models, RailApp.models, ImportApp.models 
import django.conf as conf
from django.core.paginator import Paginator 
from django import forms
from django.db.models import Q
from django.db import ProgrammingError
import Templates

# Create your views here.
DB_TYPE = [
            ('Stage', 'Stage Server'),
            ('Production','Production Server'),
            ]

class CHOICES(forms.Form):
    DBS = forms.CharField(widget=forms.RadioSelect(choices=DB_TYPE))

@login_required
def index(request):
    if request.method == "POST":
        # serverS = request.POST["btnradio"]
        city1 = request.POST.get("city_name", False)
        # user = request.user
        if city1 == False:
            return HttpResponseRedirect(reverse("index"))
        else:
            city = request.POST["city_name"]

        city_db(city)

        city3 = Cities.objects.filter(name = city).first()
        # city_id = city3.id
        # db_name = city3.db_name
        # user_city = UserCities.objects.create(user = user, city_id = city_id, db_name = db_name)
        # user_city.save()
        ############## Check Transit Existances ###############################

        if (city3.is_bus and city3.is_metro and city3.is_rail):
            return render(request, "index.html",{
                # "cities" : Cities.objects.all().order_by('-is_selected'),
                "city": Cities.objects.all().filter(name = city),
                "busRoutes": BusApp.models.BusRoute.objects.count(),
                "busRoutePoint": BusApp.models.BusRoutePoint.objects.count(),
                "busScheduleDetails": BusApp.models.BusScheduleDetails.objects.count(),
                "busServiceType": BusApp.models.BusServiceType.objects.count(),
                "busStop": BusApp.models.BusStop.objects.count(),
                "metroRoutes": MetroApp.models.MetroRoute.objects.count(),
                "metroStation": MetroApp.models.Station.objects.count(),
                "metroTimes": MetroApp.models.Times.objects.count(),
                "metroFare": MetroApp.models.MetroFare.objects.count(),
                "railRoutes": RailApp.models.RailRoute.objects.count(),
                "railSchedule": RailApp.models.RailSchedule.objects.count(),
                "railStation": RailApp.models.RailStation.objects.count(),
                "railSchedulePoint": RailApp.models.RailSchedulePoint.objects.count(),
                # "imports":ImportApp.models.Imports.objects.count() 
                })
        elif (city3.is_bus == True and city3.is_metro == True and city3.is_rail == False):
            return render(request, "index.html",{
                # "cities" : Cities.objects.all().order_by('-is_selected'),
                "city": Cities.objects.all().filter(name = city),
                "busRoutes": BusApp.models.BusRoute.objects.count(),
                "busRoutePoint": BusApp.models.BusRoutePoint.objects.count(),
                "busScheduleDetails": BusApp.models.BusScheduleDetails.objects.count(),
                "busServiceType": BusApp.models.BusServiceType.objects.count(),
                "busStop": BusApp.models.BusStop.objects.count(),
                "metroRoutes": MetroApp.models.MetroRoute.objects.count(),
                "metroStation": MetroApp.models.Station.objects.count(),
                "metroTimes": MetroApp.models.Times.objects.count(),
                "metroFare": MetroApp.models.MetroFare.objects.count(),
                # "imports":ImportApp.models.Imports.objects.count()
                })
        else:
            return render(request, "index.html",{
                # "cities" : Cities.objects.all().order_by('-is_selected'),
                "city": Cities.objects.all().filter(name = city),
                "busRoutes": BusApp.models.BusRoute.objects.count(),
                "busRoutePoint": BusApp.models.BusRoutePoint.objects.count(),
                "busScheduleDetails": BusApp.models.BusScheduleDetails.objects.count(),
                "busServiceType": BusApp.models.BusServiceType.objects.count(),
                "busStop": BusApp.models.BusStop.objects.count(),
                # "imports":ImportApp.models.Imports.objects.count()
                })

        # print(city)

    else:
        form = CHOICES(request.POST)
        # Cities.objects.update(is_selected=False)
        # city2 = Cities.objects.filter(name = "Bangalore").first()
        # conf.settings.DATABASES['master']['NAME'] = city2.db_name
        # obj = Cities.objects.get(id=city2.id)
        # obj.is_selected = True
        obj.save()
        return render(request, "index.html",{
            # "cities" : Cities.objects.all().order_by('-is_selected'),
            "form" : form,
            "city": Cities.objects.all().filter(name = city2.name),
            "busRoutes": BusApp.models.BusRoute.objects.count(),
                "busRoutePoint": BusApp.models.BusRoutePoint.objects.count(),
                "busScheduleDetails": BusApp.models.BusScheduleDetails.objects.count(),
                "busServiceType": BusApp.models.BusServiceType.objects.count(),
                "busStop": BusApp.models.BusStop.objects.count(),
            "metroRoutes": MetroApp.models.MetroRoute.objects.count(),
                "metroStation": MetroApp.models.Station.objects.count(),
                "metroTimes": MetroApp.models.Times.objects.count(),
                "metroFare": MetroApp.models.MetroFare.objects.count(),
            "railRoutes": RailApp.models.RailRoute.objects.count(),
                "railSchedule": RailApp.models.RailSchedule.objects.count(),
                "railStation": RailApp.models.RailStation.objects.count(),
                "railSchedulePoint": RailApp.models.RailSchedulePoint.objects.count(),
                "railStationCross": RailApp.models.RailStationCross.objects.count(),
                "railLineMaster": RailApp.models.RailLineMaster.objects.count(),

                # "imports":ImportApp.models.Imports.objects.count() 

        })

def city_db(city_name):
    # Cities.objects.update(is_selected=False)
    city = Cities.objects.filter(name = city_name).first()
    conf.settings.DATABASES['master']['NAME'] = city.db_name
    obj = Cities.objects.get(id=city.id)
    # obj.is_selected = True
    obj.save()
    

def view_selection(request):
    return render(request, "viewSelection.html")


def login_view(request):
    if request.method == "POST":
        # Attempt to sign user in
        username = request.POST["username"]
        password = request.POST["password"]
        user = authenticate(request, username=username, password=password)
        # Check if authentication successful
        request.session.set_expiry(500)
        if user is not None:
            login(request, user)
            return HttpResponseRedirect(reverse("index"))
        else:
            return render(request, "login.html", {
                "message": "Invalid username and password."
            })
    else:
        return render(request, "login.html")


def logout_view(request):
    logout(request)
    return HttpResponseRedirect(reverse("view_selection"))


@login_required
def transit(request):
    return render(request, "transit.html")

@login_required
@permission_required('BusApp.can_view_bus_route', raise_exception=True)
def bus_route(request):
    if 'search' in request.GET:
        s = request.GET['search']
        busR = BusApp.models.BusRoute.objects.filter(Q(route_number__icontains=s)|Q(route_name__icontains=s))
        # busR = BusApp.models.BusRoute.objects.all()
        bus_route = Paginator(busR, 20)
        page_num = request.GET.get('page')
        page = bus_route.get_page(page_num)
        return render(request, "busRoute.html",{
        "buses" : bus_route,
        "page" : page
        })
    busR = BusApp.models.BusRoute.objects.all()
    bus_route = Paginator(busR, 20)
    page_num = request.GET.get('page')
    page = bus_route.get_page(page_num)
    return render(request, "busRoute.html",{
        "buses" : bus_route,
        "page" : page
    })

@login_required
@permission_required('BusApp.can_view_bus_route_point', raise_exception=True)
def bus_route_point(request):
    if 'search' in request.GET:
        s = request.GET['search']
        busRP = BusApp.models.BusRoutePoint.objects.filter(Q(route_id__icontains=s)|Q(bus_stop_id__icontains=s))
        bus_route_point = Paginator(busRP, 20)
        page_num = request.GET.get('page')
        page = bus_route_point.get_page(page_num)
        return render(request, "busRoutePoint.html", {
        "busrp" : busRP,
        "page" : page
        })
    busRP = BusApp.models.BusRoutePoint.objects.all()
    bus_route_point = Paginator(busRP, 20)
    page_num = request.GET.get('page')
    page = bus_route_point.get_page(page_num)
    return render(request, "busRoutePoint.html", {
        "busrp" : busRP,
        "page" : page
    })

@login_required
@permission_required('BusApp.can_view_bus_schedule_point', raise_exception=True)
def bus_schedule_point(request):
    if 'search' in request.GET:
        s = request.GET['search']
        busSP = BusApp.models.BusScheduleDetails.objects.filter(Q(route_number_id__icontains=s)|Q(schedule_number__icontains=s))
        bus_schedule_point = Paginator(busSP, 20)
        page_num = request.GET.get('page')
        page = bus_schedule_point.get_page(page_num)
        return render(request, "busSchedulePoint.html", {
            "bussp" : busSP,
            "page" : page
        })
    busSP = BusApp.models.BusScheduleDetails.objects.all()
    bus_schedule_point = Paginator(busSP, 20)
    page_num = request.GET.get('page')
    page = bus_schedule_point.get_page(page_num)
    return render(request, "busSchedulePoint.html", {
        "bussp" : busSP,
        "page" : page
    })

@login_required
@permission_required('BusApp.can_view_bus_stop', raise_exception=True)
def bus_stop(request):
    if 'search' in request.GET:
        s = request.GET['search']
        busS = BusApp.models.BusStop.objects.filter(Q(bus_stop_name__icontains=s)|Q(latitude_current__icontains=s)|Q(longitude_current__icontains=s))
        bus_stop = Paginator(busS, 20)
        page_num = request.GET.get('page')
        page = bus_stop.get_page(page_num)
        return render(request, "busStop.html", {
            "buss" : bus_stop,
            "page" : page
        })
    busS = BusApp.models.BusStop.objects.all()
    bus_stop = Paginator(busS, 20)
    page_num = request.GET.get('page')
    page = bus_stop.get_page(page_num)
    return render(request, "busStop.html", {
        "buss" : bus_stop,
        "page" : page
    })


@login_required
@permission_required('BusApp.can_view_bus_service_type', raise_exception=True)
def bus_service_type(request):
    if 'search' in request.GET:
        s = request.GET['search']
        busST = BusApp.models.BusServiceType.objects.filter(Q(service_type_name__icontains=s) | Q(service_type_id__icontains=s) )
        bus_service_type = Paginator(busST, 20)
        page_num = request.GET.get('page')
        page = bus_service_type.get_page(page_num)
        return render(request, "busServiceType.html", {
            "busst" : busST,
            "page" : page
        })
    busST = BusApp.models.BusServiceType.objects.all()
    bus_service_type = Paginator(busST, 20)
    page_num = request.GET.get('page')
    page = bus_service_type.get_page(page_num)
    return render(request, "busServiceType.html", {
        "busst" : busST,
        "page" : page
    })

@login_required
@permission_required('MetroApp.can_view_metro_route', raise_exception=True)
def metro_route(request):
    if 'search' in request.GET:
        s = request.GET['search']
        metroR = MetroApp.models.MetroRoute.objects.filter(Q(metro_route_id__icontains=s) | Q(route_name__icontains=s) )
        metro_route = Paginator(metroR, 20)
        page_num = request.GET.get('page')
        page = metro_route.get_page(page_num)
        return render(request, "metroRoute.html", {
            "metroR" : metroR,
            "page" : page
        })
    metroR = MetroApp.models.MetroRoute.objects.all()
    metro_route = Paginator(metroR, 20)
    page_num = request.GET.get('page')
    page = metro_route.get_page(page_num)
    return render(request, "metroRoute.html", {
        "metroR" : metroR,
        "page" : page
    })

@login_required
@permission_required('MetroApp.can_view_metro_fare', raise_exception=True)
def metro_fare(request):
    if 'search' in request.GET:
        s = request.GET['search']
        metroFa = MetroApp.models.MetroFare.objects.filter(Q(station_id_from__icontains=s) | Q(station_id_to__icontains=s) )
        metro_fare = Paginator(metroFa, 20)
        page_num = request.GET.get('page')
        page = metro_fare.get_page(page_num)
        return render(request, "metroFare.html", {
            "metroFa" : metroFa,
            "page" : page
        })
    metroFa = MetroApp.models.MetroFare.objects.all()
    metro_fare = Paginator(metroFa, 20)
    page_num = request.GET.get('page')
    page = metro_fare.get_page(page_num)
    return render(request, "metroFare.html", {
        "metroFa" : metroFa,
        "page" : page
    })

@login_required
@permission_required('MetroApp.can_view_metro_station', raise_exception=True)
def metro_station(request):
    if 'search' in request.GET:
        s = request.GET['search']
        metroS = MetroApp.models.Station.objects.filter(Q(station_name__icontains=s) | Q(station_id__icontains=s))
        metro_station = Paginator(metroS, 20)
        page_num = request.GET.get('page')
        page = metro_station.get_page(page_num)
        return render(request, "metroStation.html", {
            "metroS" : metroS,
            "page" : page
        })
    metroS = MetroApp.models.Station.objects.all()
    metro_station = Paginator(metroS, 20)
    page_num = request.GET.get('page')
    page = metro_station.get_page(page_num)
    return render(request, "metroStation.html", {
        "metroS" : metroS,
        "page" : page
    })

@login_required
@permission_required('MetroApp.can_view_metro_time', raise_exception=True)
def metro_time(request):
    if 'search' in request.GET:
        s = request.GET['search']
        metroT = MetroApp.models.Times.objects.filter(Q(station__icontains=s))
        metro_time = Paginator(metroT, 20)
        page_num = request.GET.get('page')
        page = metro_time.get_page(page_num)
        return render(request, "metroTime.html", {
            "metroT" : metroT,
            "page" : page
        })
    metroT = MetroApp.models.Times.objects.all()
    metro_time = Paginator(metroT, 20)
    page_num = request.GET.get('page')
    page = metro_time.get_page(page_num)
    return render(request, "metroTime.html", {
        "metroT" : metroT,
        "page" : page
    })

@login_required
@permission_required('RailApp.can_view_rail_route', raise_exception=True)
def rail_route(request):
    if 'search' in request.GET:
        s = request.GET['search']
        railR = RailApp.models.RailRoute.objects.filter(Q(name__icontains=s)|Q(line__icontains=s)|Q(service__icontains=s)|Q(status__icontains=s))
        rail_route = Paginator(railR, 20)
        page_num = request.GET.get('page')
        page = rail_route.get_page(page_num)
        return render(request, "railRoute.html", {
            "railR" : railR,
            "page" : page
        })
    railR = RailApp.models.RailRoute.objects.all()
    rail_route = Paginator(railR, 20)
    page_num = request.GET.get('page')
    page = rail_route.get_page(page_num)
    return render(request, "railRoute.html", {
        "railR" : railR,
        "page" : page
    })

@login_required
@permission_required('RailApp.can_view_rail_schedule', raise_exception=True)
def rail_schedule(request):
    if 'search' in request.GET:
        s = request.GET['search']
        railS = RailApp.models.RailSchedule.objects.filter(Q(route_id__icontains=s)|Q(train_no__icontains=s))
        rail_schedule = Paginator(railS, 20)
        page_num = request.GET.get('page')
        page = rail_schedule.get_page(page_num)
        return render(request, "railSchedule.html", {
            "railS" : railS,
            "page" : page
        })
    railS = RailApp.models.RailSchedule.objects.all()
    rail_schedule = Paginator(railS, 20)
    page_num = request.GET.get('page')
    page = rail_schedule.get_page(page_num)
    return render(request, "railSchedule.html", {
        "railS" : railS,
        "page" : page
    })

@login_required
@permission_required('RailApp.can_view_rail_schedule_point', raise_exception=True)
def rail_schedule_point(request):
    if 'search' in request.GET:
        s = request.GET['search']
        railSP = RailApp.models.RailSchedulePoint.objects.filter(Q(route_id__icontains=s)|Q(schedule_id__icontains=s)|Q(station_id__icontains=s))
        rail_schedule_point = Paginator(railSP, 20)
        page_num = request.GET.get('page')
        page = rail_schedule_point.get_page(page_num)
        return render(request, "railSchedulePoint.html", {
            "railSP" : railSP,
            "page" : page
        })
    railSP = RailApp.models.RailSchedulePoint.objects.all()
    rail_schedule_point = Paginator(railSP, 20)
    page_num = request.GET.get('page')
    page = rail_schedule_point.get_page(page_num)
    return render(request, "railSchedulePoint.html", {
        "railSP" : railSP,
        "page" : page
    })

@login_required
@permission_required('RailApp.can_view_rail_station', raise_exception=True)
def rail_station(request):
    if 'search' in request.GET:
        s = request.GET['search']
        railSa = RailApp.models.RailStation.objects.filter(Q(name__icontains=s)|Q(latitude__icontains=s)|Q(longitude__icontains=s))
        rail_station = Paginator(railSa, 20)
        page_num = request.GET.get('page')
        page = rail_station.get_page(page_num)
        return render(request, "railStation.html", {
            "railSa" : railSa,
            "page" : page
        })
    railSa = RailApp.models.RailStation.objects.all()
    rail_station = Paginator(railSa, 20)
    page_num = request.GET.get('page')
    page = rail_station.get_page(page_num)
    return render(request, "railStation.html", {
        "railSa" : railSa,
        "page" : page
    })

@login_required
@permission_required('RailApp.can_view_RailStationCross', raise_exception=True)
def rail_station(request):
    if 'search' in request.GET:
        s = request.GET['search']
        railSa = RailApp.models.RailStationCross.objects.filter(Q(line__icontains=s)|Q(station_id__icontains=s)|Q(is_cross__icontains=s))
        rail_station = Paginator(railSa, 20)
        page_num = request.GET.get('page')
        page = rail_station.get_page(page_num)
        return render(request, "railStation.html", {
            "railSa" : railSa,
            "page" : page
        })
    railSa = RailApp.models.RailStationCross.objects.all()
    RailStationCross = Paginator(railSa, 20)
    page_num = request.GET.get('page')
    page = RailStationCross.get_page(page_num)
    return render(request, "RailStationCross.html", {
        "railSa" : railSa,
        "page" : page
    })

@login_required
@permission_required('RailApp.can_view_RailLineMaster', raise_exception=True)
def rail_station(request):
    if 'search' in request.GET:
        s = request.GET['search']
        railSa = RailApp.models.RailLineMaster.objects.filter(Q(line__icontains=s)|Q(station_id__icontains=s)|Q(order__icontains=s))
        RailLineMaster = Paginator(railSa, 20)
        page_num = request.GET.get('page')
        page = RailLineMaster.get_page(page_num)
        return render(request, "RailLineMaster.html", {
            "railSa" : railSa,
            "page" : page
        })
    railSa = RailApp.models.RailLineMaster.objects.all()
    RailLineMaster = Paginator(railSa, 20)
    page_num = request.GET.get('page')
    page = RailLineMaster.get_page(page_num)
    return render(request, "RailLineMaster.html", {
        "railSa" : railSa,
        "page" : page
    })

# @login_required
# @permission_required('ImportApp.can_view_imports', raise_exception=True)
# def imports(request):
#     if 'search' in request.GET:
#         s = request.GET['search']
#         imports = ImportApp.models.Imports.objects.filter(Q(user_agent__icontains=s)|Q(user_id__icontains=s)|Q(city_name__icontains=s))
#         imp = Paginator(imports, 20)
#         page_num = request.GET.get('page')
#         page = imp.get_page(page_num)
#         return render(request, "imports.html", {
#             "imports" : imports,
#             "page" : page
#         })
#     imports = ImportApp.models.Imports.objects.all()
#     imp = Paginator(imports, 20)
#     page_num = request.GET.get('page')
#     page = imp.get_page(page_num)
#     return render(request, "imports.html", {
#         "imports" : imports,
#         "page" : page
#     })