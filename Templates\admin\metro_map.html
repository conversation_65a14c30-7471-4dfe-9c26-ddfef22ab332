{% extends "admin/base.html" %}
{% load static %}
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<!-- <style>
    .hidden {
        display: none;
    }
</style> -->



{% block content %}

{{ block.super }}

<body>
    <label for="module" class="form-group">Module:</label> 
         <select id="module" name="module" class="form-select" onchange="redirectToUrl()" required>
        <option value="" disabled>Choose Module</option>
            {% if city.is_bus == True and city.is_metro == True and city.is_rail == True %}
                <option value="Bus" selected>
                    {{ city.name }} - Bus
                </option>
                <option value="Metro" selected>
                    {{ city.name }} - Metro
                </option>
                <option value="Rail" >
                    {{ city.name }} - Rail
                </option>
            {% elif city.is_bus == True and city.is_metro == True and city.is_rail == False %}
                <option value="Bus">
                    {{ city.name }} - Bus
                </option>
                <option value="Metro" selected>
                    {{ city.name }} - Metro
                </option>

            {% else %}
            <option value="Bus">
                {{ city.name }} - Bus
            </option>

            {% endif %}
        </select>
    <label class="form-group">Route:</label>
            <select id="Routes">
            <option value="#" selected disabled>Choose Route</option>
            {% for route in routes %}
            <option value="{{route.metro_route_id }}">{{ route.metro_route_id  }}-({{route.route_name}})</option>
            {% endfor %}
            </select>
            <br><br>
            
{{ map|safe }}
</body>
<script>

function redirectToUrl() {
    // Get the selected option
    var selectElement = document.getElementById("module");
    var selectedValue = selectElement.value;

    // Define URLs for each option
    var url1 = window.location.origin + '/bus/';
    var url2 = window.location.origin + '/metro/';
    var url3 = window.location.origin + "/rail/";

    // Redirect to the URL based on the selected option
    if (selectedValue === "Bus") {
        window.location.href = url1;
      } else if (selectedValue === "Metro") {
        window.location.href =  url2;
      } else if (selectedValue === "Rail") {
        window.location.href = url3;
      }
}
document.addEventListener('DOMContentLoaded', function () {
    var routesSelect = document.getElementById('Routes'); 

    if (routesSelect) {
        console.log('Select element found', routesSelect);
        
        routesSelect.addEventListener('change', function () {
            var selectedRouteID = routesSelect.value;
            console.log('Selected Route ID:', selectedRouteID);

            // Construct the URL with the selectedRouteID
            var url = window.location.origin + '/get_metro_route/' + selectedRouteID;
            console.log('Constructed URL:', url);

            // Redirect to the constructed URL
            window.location.href = url;
        });
    } else {
        console.error('Select element not found');
    }
});



 </script>

{% endblock %}