"""
Local development settings for FileProcessorApp testing
This file provides SQLite database configuration for local development
"""

import os
from pathlib import Path

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent

# Import all settings from the main settings file
from TransitCMS.settings import *

# Override database settings for local development
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db_local.sqlite3',
    },
    'master': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db_master_local.sqlite3',
    }
}

# Disable database routing for local development
DATABASE_ROUTERS = []

# Enable debug mode for local development
DEBUG = True

# Allow all hosts for local development
ALLOWED_HOSTS = ['*']

# Disable CSRF trusted origins for local development
CSRF_TRUSTED_ORIGINS = []

# Use console email backend for local development
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'

# Media files configuration for local development
MEDIA_URL = '/media/'
MEDIA_ROOT = BASE_DIR / 'media'

# Static files configuration for local development
STATIC_URL = '/static/'
STATICFILES_DIRS = [BASE_DIR / "static"]

print("🔧 Using local development settings with SQLite database")
print(f"📁 Database location: {DATABASES['default']['NAME']}")
print(f"📁 Media files: {MEDIA_ROOT}")
