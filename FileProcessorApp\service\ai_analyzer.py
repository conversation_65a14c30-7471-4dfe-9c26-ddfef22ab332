import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional

try:
    import ollama
except ImportError:
    ollama = None

logger = logging.getLogger(__name__)


class AIAnalyzer:
    """Service for AI-powered analysis of transit data"""

    def __init__(self, model_name: str = "llama2"):
        self.model_name = model_name
        self.client = None

        # CPU-optimized configuration for EC2
        self.cpu_config = self._get_cpu_config()

        if ollama:
            try:
                self.client = ollama.Client()
                # Test if the model is available
                self._test_model_availability()
            except Exception as e:
                logger.warning(f"Could not initialize Ollama client: {e}")
                self.client = None

    def _get_cpu_config(self):
        """Get CPU-optimized configuration for EC2 deployment"""
        import os
        import multiprocessing

        # Get number of CPU cores
        cpu_count = multiprocessing.cpu_count()

        # For EC2, use most cores but leave some for system
        if cpu_count >= 4:
            num_threads = max(1, cpu_count - 1)  # Leave 1 core for system
        else:
            num_threads = cpu_count

        # EC2-optimized settings
        config = {
            "num_thread": num_threads,  # Use multiple CPU threads
            "num_gpu": 0,  # Force CPU-only processing
            "num_ctx": 2048,  # Context window (adjust based on RAM)
            "temperature": 0.1,  # Low temperature for consistent results
            "top_k": 10,  # Limit token selection for faster processing
            "top_p": 0.9,  # Nucleus sampling
            "repeat_penalty": 1.1,  # Prevent repetition
            "num_predict": 512,  # Limit response length for speed
            "stop": ["\n\n", "###", "---"],  # Stop tokens
        }

        # Adjust context window based on available memory
        try:
            import psutil
            available_memory_gb = psutil.virtual_memory().available / (1024**3)

            if available_memory_gb < 4:
                config["num_ctx"] = 1024  # Smaller context for low memory
                config["num_predict"] = 256
            elif available_memory_gb < 8:
                config["num_ctx"] = 2048  # Medium context
                config["num_predict"] = 512
            else:
                config["num_ctx"] = 4096  # Larger context for high memory
                config["num_predict"] = 1024

        except ImportError:
            logger.warning("psutil not available, using default memory settings")

        logger.info(f"CPU config: {num_threads} threads, {config['num_ctx']} context, CPU-only mode")
        return config

    def _test_model_availability(self):
        """Test if the specified model is available"""
        try:
            if self.client:
                # Try a simple test prompt with CPU config
                response = self.client.generate(
                    model=self.model_name,
                    prompt="Test",
                    options={
                        "num_predict": 1,
                        "num_thread": self.cpu_config["num_thread"],
                        "num_gpu": 0  # Force CPU
                    }
                )
                logger.info(f"Model {self.model_name} is available with {self.cpu_config['num_thread']} CPU threads")
        except Exception as e:
            logger.warning(f"Model {self.model_name} not available: {e}")
            # Try alternative models (prioritize CPU-efficient ones for EC2)
            alternative_models = ["phi", "llama2", "mistral", "llama3", "codellama"]
            for alt_model in alternative_models:
                try:
                    response = self.client.generate(
                        model=alt_model,
                        prompt="Test",
                        options={
                            "num_predict": 1,
                            "num_thread": self.cpu_config["num_thread"],
                            "num_gpu": 0
                        }
                    )
                    self.model_name = alt_model
                    logger.info(f"Using alternative CPU-optimized model: {alt_model}")
                    break
                except Exception:
                    continue
            else:
                logger.warning("No suitable AI model found")
                self.client = None
    
    def analyze_transit_data(self, text: str) -> str:
        """
        Analyze text to identify transit-related information
        
        Args:
            text: Extracted text from document
            
        Returns:
            JSON string with analysis results
        """
        if not self.client:
            logger.warning("AI client not available, falling back to rule-based analysis")
            return self._fallback_analysis(text)
        
        try:
            prompt = self._create_analysis_prompt(text)
            
            response = self.client.generate(
                model=self.model_name,
                prompt=prompt,
                options=self.cpu_config
            )
            
            # Parse and structure the response
            analysis_result = self._parse_ai_response(response['response'])
            return json.dumps(analysis_result, indent=2)
            
        except Exception as e:
            logger.error(f"Error in AI analysis: {e}")
            return self._fallback_analysis(text)
    
    def _create_analysis_prompt(self, text: str) -> str:
        """Create a structured prompt for transit data analysis"""
        prompt = f"""
You are an expert in public transit data analysis. Analyze the following text and identify any transit-related information.

Please identify and extract information about:
1. Transit agencies (names, contact info, service areas)
2. Routes (route numbers, names, descriptions, types)
3. Stops/Stations (names, locations, IDs, accessibility info)
4. Schedules (departure/arrival times, frequencies, service periods)
5. Fares (prices, payment methods, fare zones)
6. Service information (service types, vehicle types, operational details)

For each type of information found, provide:
- Category (agency/route/stop/schedule/fare/service)
- Confidence level (0.0 to 1.0)
- Specific data extracted
- Location in text (if possible)

Respond in JSON format with the following structure:
{{
    "analysis_type": "ai_powered",
    "model_used": "{self.model_name}",
    "timestamp": "{datetime.now().isoformat()}",
    "findings": [
        {{
            "category": "route",
            "confidence": 0.9,
            "data": {{
                "route_number": "123",
                "route_name": "Main Street Line",
                "description": "..."
            }},
            "text_location": "line 15-20"
        }}
    ],
    "summary": "Brief summary of transit data found"
}}

Text to analyze:
{text[:2000]}...
"""
        return prompt
    
    def _parse_ai_response(self, response: str) -> Dict:
        """Parse and validate AI response"""
        try:
            # Try to extract JSON from response
            json_start = response.find('{')
            json_end = response.rfind('}') + 1
            
            if json_start >= 0 and json_end > json_start:
                json_str = response[json_start:json_end]
                parsed = json.loads(json_str)
                
                # Validate structure
                if self._validate_analysis_structure(parsed):
                    return parsed
            
            # If parsing fails, create structured response from text
            return self._create_structured_response_from_text(response)
            
        except Exception as e:
            logger.error(f"Error parsing AI response: {e}")
            return self._create_error_response(str(e))
    
    def _validate_analysis_structure(self, data: Dict) -> bool:
        """Validate that the analysis has the expected structure"""
        required_fields = ["analysis_type", "timestamp", "findings"]
        return all(field in data for field in required_fields)
    
    def _create_structured_response_from_text(self, response: str) -> Dict:
        """Create structured response when JSON parsing fails"""
        return {
            "analysis_type": "ai_powered_text",
            "model_used": self.model_name,
            "timestamp": datetime.now().isoformat(),
            "findings": [],
            "raw_response": response,
            "summary": "AI analysis completed but response format was not structured"
        }
    
    def _create_error_response(self, error: str) -> Dict:
        """Create error response structure"""
        return {
            "analysis_type": "ai_error",
            "model_used": self.model_name,
            "timestamp": datetime.now().isoformat(),
            "findings": [],
            "error": error,
            "summary": "AI analysis failed"
        }
    
    def _fallback_analysis(self, text: str) -> str:
        """Fallback analysis when AI is not available"""
        analysis = {
            "analysis_type": "rule_based_fallback",
            "timestamp": datetime.now().isoformat(),
            "findings": []
        }
        
        # Simple keyword-based analysis
        keywords = {
            "agency": ["transit", "authority", "transport", "bus company", "metro", "railway"],
            "route": ["route", "line", "service", "bus", "train", "metro line"],
            "stop": ["stop", "station", "terminal", "depot", "platform", "bus stop"],
            "schedule": ["schedule", "timetable", "departure", "arrival", "frequency", "hours"],
            "fare": ["fare", "price", "cost", "ticket", "payment", "fee"]
        }
        
        text_lower = text.lower()
        
        for category, words in keywords.items():
            found_keywords = [word for word in words if word in text_lower]
            if found_keywords:
                confidence = min(len(found_keywords) / len(words), 1.0)
                analysis["findings"].append({
                    "category": category,
                    "confidence": confidence,
                    "keywords_found": found_keywords,
                    "data": {"keywords": found_keywords}
                })
        
        analysis["summary"] = f"Found {len(analysis['findings'])} categories of transit data using keyword analysis"
        
        return json.dumps(analysis, indent=2)
    
    def extract_gtfs_entities(self, text: str) -> Dict:
        """
        Extract specific GTFS entities from text
        
        Args:
            text: Text to analyze
            
        Returns:
            Dictionary with GTFS entities
        """
        if not self.client:
            return self._extract_gtfs_fallback(text)
        
        try:
            prompt = f"""
Extract GTFS (General Transit Feed Specification) entities from the following text.
Identify and structure data for these GTFS files:

1. agency.txt: agency_id, agency_name, agency_url, agency_timezone
2. routes.txt: route_id, route_short_name, route_long_name, route_type
3. stops.txt: stop_id, stop_name, stop_lat, stop_lon
4. trips.txt: route_id, service_id, trip_id, trip_headsign
5. stop_times.txt: trip_id, arrival_time, departure_time, stop_id, stop_sequence

Respond in JSON format with extracted entities:
{{
    "agencies": [...],
    "routes": [...],
    "stops": [...],
    "trips": [...],
    "stop_times": [...]
}}

Text: {text[:1500]}...
"""
            
            # Use CPU config but allow more tokens for GTFS extraction
            gtfs_config = self.cpu_config.copy()
            gtfs_config["num_predict"] = min(1500, gtfs_config["num_predict"] * 2)

            response = self.client.generate(
                model=self.model_name,
                prompt=prompt,
                options=gtfs_config
            )
            
            return self._parse_gtfs_response(response['response'])
            
        except Exception as e:
            logger.error(f"Error extracting GTFS entities: {e}")
            return self._extract_gtfs_fallback(text)
    
    def _parse_gtfs_response(self, response: str) -> Dict:
        """Parse GTFS extraction response"""
        try:
            json_start = response.find('{')
            json_end = response.rfind('}') + 1
            
            if json_start >= 0 and json_end > json_start:
                json_str = response[json_start:json_end]
                return json.loads(json_str)
            
            return {"agencies": [], "routes": [], "stops": [], "trips": [], "stop_times": []}
            
        except Exception as e:
            logger.error(f"Error parsing GTFS response: {e}")
            return {"agencies": [], "routes": [], "stops": [], "trips": [], "stop_times": []}
    
    def _extract_gtfs_fallback(self, text: str) -> Dict:
        """Fallback GTFS extraction using simple patterns"""
        return {
            "agencies": [],
            "routes": [],
            "stops": [],
            "trips": [],
            "stop_times": [],
            "extraction_method": "fallback",
            "note": "AI extraction not available, manual review recommended"
        }
