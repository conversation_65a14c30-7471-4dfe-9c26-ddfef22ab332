#!/usr/bin/env python
"""
Test script for FileProcessorApp
Run this script to test the file processing functionality
"""

import os
import sys
import django
from pathlib import Path

# Add the project directory to Python path
BASE_DIR = Path(__file__).resolve().parent
sys.path.append(str(BASE_DIR))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'TransitCMS.settings')
django.setup()

# Now we can import Django models and services
from FileProcessorApp.models import UploadedFile, GTFSDataset, ExtractedTransitData
from FileProcessorApp.service.file_processor import FileProcessorService
from FileProcessorApp.service.ai_analyzer import AIAnalyzer
from FileProcessorApp.service.gtfs_generator import GTFSGeneratorService
from FileProcessorApp.service.file_manager import FileManagerService
from django.contrib.auth.models import User


def test_imports():
    """Test that all imports work correctly"""
    print("🔍 Testing imports...")
    
    try:
        # Test service imports
        processor = FileProcessorService()
        print("✅ FileProcessorService imported successfully")
        
        analyzer = AIAnalyzer()
        print("✅ AIAnalyzer imported successfully")
        
        gtfs_gen = GTFSGeneratorService()
        print("✅ GTFSGeneratorService imported successfully")
        
        file_mgr = FileManagerService()
        print("✅ FileManagerService imported successfully")
        
        # Test model imports
        print(f"✅ UploadedFile model: {UploadedFile}")
        print(f"✅ GTFSDataset model: {GTFSDataset}")
        print(f"✅ ExtractedTransitData model: {ExtractedTransitData}")
        
        return True
        
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False


def test_file_processor_methods():
    """Test file processor methods"""
    print("\n🔧 Testing FileProcessor methods...")
    
    try:
        processor = FileProcessorService()
        
        # Test supported processors
        print(f"✅ Supported file types: {list(processor.supported_processors.keys())}")
        
        # Test text processing
        test_text = "This is a test document about bus routes and train schedules."
        analysis = processor._rule_based_analysis(test_text)
        print(f"✅ Rule-based analysis working: {len(analysis)} characters")
        
        return True
        
    except Exception as e:
        print(f"❌ FileProcessor error: {e}")
        return False


def test_ai_analyzer():
    """Test AI analyzer"""
    print("\n🤖 Testing AI Analyzer...")
    
    try:
        analyzer = AIAnalyzer()
        
        # Test fallback analysis
        test_text = "Bus Route 123 operates from Central Station to Airport Terminal. Departure times: 08:00, 10:00, 12:00."
        result = analyzer._fallback_analysis(test_text)
        print(f"✅ Fallback analysis working: {len(result)} characters")
        
        # Test GTFS extraction fallback
        gtfs_entities = analyzer._extract_gtfs_fallback(test_text)
        print(f"✅ GTFS extraction fallback: {list(gtfs_entities.keys())}")
        
        return True
        
    except Exception as e:
        print(f"❌ AI Analyzer error: {e}")
        return False


def test_gtfs_generator():
    """Test GTFS generator methods"""
    print("\n📊 Testing GTFS Generator...")
    
    try:
        generator = GTFSGeneratorService()
        
        # Test CSV content creation
        fieldnames = ['test_id', 'test_name']
        data = [{'test_id': '1', 'test_name': 'Test Item'}]
        csv_content = generator._create_csv_content(fieldnames, data)
        print(f"✅ CSV generation working: {len(csv_content)} characters")
        
        # Test minimal file generation
        minimal_agency = generator._generate_minimal_file('agency.txt')
        print(f"✅ Minimal file generation: {len(minimal_agency)} characters")
        
        return True
        
    except Exception as e:
        print(f"❌ GTFS Generator error: {e}")
        return False


def test_file_manager():
    """Test file manager"""
    print("\n📁 Testing File Manager...")
    
    try:
        manager = FileManagerService()
        
        # Test directory creation
        print(f"✅ File manager initialized")
        
        # Test storage statistics
        stats = manager.get_storage_statistics()
        print(f"✅ Storage statistics: {list(stats.keys())}")
        
        return True
        
    except Exception as e:
        print(f"❌ File Manager error: {e}")
        return False


def test_database_models():
    """Test database model creation"""
    print("\n🗄️ Testing Database Models...")
    
    try:
        # Test model field access
        file_types = UploadedFile.FILE_TYPES
        print(f"✅ UploadedFile file types: {len(file_types)} types")
        
        processing_statuses = UploadedFile.PROCESSING_STATUS
        print(f"✅ Processing statuses: {len(processing_statuses)} statuses")
        
        data_types = ExtractedTransitData.DATA_TYPES
        print(f"✅ Data types: {len(data_types)} types")
        
        return True
        
    except Exception as e:
        print(f"❌ Database model error: {e}")
        return False


def check_dependencies():
    """Check if required dependencies are available"""
    print("\n📦 Checking Dependencies...")
    
    dependencies = {
        'PyPDF2': 'PDF processing',
        'pdfplumber': 'Advanced PDF processing',
        'PIL': 'Image processing',
        'pytesseract': 'OCR functionality',
        'pandas': 'Data manipulation',
        'docx': 'Word document processing',
        'ollama': 'AI model integration'
    }
    
    available = []
    missing = []
    
    for dep, description in dependencies.items():
        try:
            if dep == 'PIL':
                from PIL import Image
            elif dep == 'docx':
                from docx import Document
            else:
                __import__(dep)
            available.append(f"✅ {dep}: {description}")
        except ImportError:
            missing.append(f"❌ {dep}: {description}")
    
    print("Available dependencies:")
    for dep in available:
        print(f"  {dep}")
    
    if missing:
        print("\nMissing dependencies:")
        for dep in missing:
            print(f"  {dep}")
        print("\nTo install missing dependencies:")
        print("pip install PyPDF2 pdfplumber Pillow pytesseract pandas python-docx ollama")
    
    return len(missing) == 0


def main():
    """Run all tests"""
    print("🚀 Starting FileProcessorApp Tests\n")
    
    tests = [
        ("Dependencies", check_dependencies),
        ("Imports", test_imports),
        ("Database Models", test_database_models),
        ("File Processor", test_file_processor_methods),
        ("AI Analyzer", test_ai_analyzer),
        ("GTFS Generator", test_gtfs_generator),
        ("File Manager", test_file_manager),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "="*50)
    print("📋 TEST SUMMARY")
    print("="*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! FileProcessorApp is ready to use.")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
    
    return passed == total


if __name__ == "__main__":
    main()
