from django.shortcuts import render,redirect
import folium
from django.urls import reverse_lazy
from django.utils.html import format_html
from geopy.geocoders import Nominatim
from folium import Map, FeatureGroup, Marker, LayerControl, PolyLine
from MetroApp.models import *
from CityApp.models import *
from django.contrib import admin
from django import conf 
import osmnx as ox
from django.contrib.auth.decorators import login_required, permission_required


# Create your views here.


# Create your views here.
@login_required
def metro(request):
    city1 = Cities.objects.exclude(id=8).filter(is_metro='t')
    name = request.session['city_name']
    city = Cities.objects.get(name=name)
    route = MetroRoute.objects.all().first()
    sta = Station.objects.filter(metro_route_id=route.metro_route_id).first()
    map = folium.Map(location=[float(sta.latitude),float(sta.longitude)], zoom_start=12, height=500)
    Fullscreen = folium.plugins.Fullscreen(position='topright').add_to(map)
    routes = MetroRoute.objects.all()
    try:
        for index,route in enumerate(routes):
            locations=[]
            route_name = route.route_name
            routeID = route.metro_route_id
            stations = Station.objects.filter(metro_route_id=route.metro_route_id).order_by('metro_route_id','seq')
            # print(stations.values())
            marker_group = folium.FeatureGroup(name=f"Route_Points_{index}", show=True)
            for station in stations:
                name = station.station_name
                id=station.station_id
                coordinates = [float(station.latitude),float(station.longitude)]
                locations.append(coordinates)
                if station.seq == 1:
                    folium.Marker(coordinates, tooltip=(station.station_id,station.station_name), icon=folium.Icon(color='green')).add_to(marker_group)
                elif station.seq == len(stations):
                    folium.Marker(coordinates, tooltip=(station.station_id,station.station_name), icon=folium.Icon(color='red')).add_to(marker_group)
                else :
                    # folium.Marker(coordinates, popup=station.station_name).add_to(marker_group)
                    folium.CircleMarker(coordinates, tooltip=(station.station_id,station.station_name), radius=8,color='white', fill=True, fill_color='black', fill_opacity=0.7).add_to(marker_group)
            total_distance = 0
            for i in range(len(locations) - 1):
                lat1, lon1 = locations[i]
                lat2, lon2 = locations[i + 1]
                segment_distance = ox.distance.great_circle(lat1, lon1, lat2, lon2, earth_radius=6378.14)
                total_distance += segment_distance
            iframe = folium.IFrame('Route Id:' + str(routeID) + '<br>' + 'Route Name: ' + route_name + '<br>' + 'Distance: ' + str(total_distance) + '{Kms}')
            popup = folium.Popup(iframe, min_width=250, max_width=280)
            folium.PolyLine(popup=popup,locations=locations,color=route.route_color_code, weight=5).add_to(marker_group)
            map.add_child(marker_group)
        map.fit_bounds([locations])
        # map.save("map.html")
        context ={"site_title": admin.site.site_title, "site_version": admin.site.site_version,'map':map._repr_html_(),"routes":routes, "city1":city1, "city":city}
        return render(request,'admin/metro_map.html',context)
    except Exception as e:
        city1 = Cities.objects.exclude(id=8).filter(is_metro='t')
        # map = folium.Map(location=(12.97750000,77.57180000), zoom_start=12)
        route = MetroRoute.objects.all().first()
        sta = Station.objects.filter(metro_route_id=route.metro_route_id).first()
        map = folium.Map(location=[float(sta.latitude),float(sta.longitude)], zoom_start=12, height=500)
        Fullscreen = folium.plugins.Fullscreen(position='topright').add_to(map)
        # map.save("map.html")
        context ={"site_title": admin.site.site_title, "site_version": admin.site.site_version,'map':map._repr_html_(),"routes":routes, "city1":city1, "city":city}
        return render(request,'admin/metro_map.html',context)


@login_required
def get_metro_route(request,metro_route_id):
    name = request.session['city_name']
    city = Cities.objects.get(name=name)
    city1 = Cities.objects.exclude(id=8).filter(is_metro='t')
    map = folium.Map(location=(12.97750000,77.57180000), zoom_start=15, height=500)
    Fullscreen = folium.plugins.Fullscreen(position='topright').add_to(map)
    routes = MetroRoute.objects.all()
    # print("hi")
    route=MetroRoute.objects.get(metro_route_id=metro_route_id)
    # print(route.metro_route_id)
    stations = Station.objects.filter(metro_route_id=route.metro_route_id).order_by('metro_route_id','seq')
    # print(stations.values())
    # stations = Station.objects.filter(metro_route_id=route.metro_route_id).order_by('seq')
    try:
        locations=[]
        for station in stations:
            name = station.station_name
            id=station.station_id
            coordinates = (float(station.latitude),float(station.longitude))
            # folium.Marker(coordinates, popup=name).add_to(map)
            # folium.Marker(coordinates, popup=station.station_name).add_to(map)
            if station.seq == 1:
                # print(station.seq)
                folium.Marker(coordinates, tooltip=(id,name), icon=folium.Icon(color='green')).add_to(map)
            elif station.seq == len(stations):
                # print(len(stations))
                folium.Marker(coordinates, tooltip=(id,name), icon=folium.Icon(color='red')).add_to(map)
            else :
                folium.CircleMarker(coordinates, tooltip=(id,name),radius=8,color='white', fill=True, fill_color='black', fill_opacity=0.7).add_to(map)
            locations.append(coordinates)
        total_distance = 0
        for i in range(len(locations) - 1):
            lat1, lon1 = locations[i]
            lat2, lon2 = locations[i + 1]
            segment_distance = ox.distance.great_circle(lat1, lon1, lat2, lon2, earth_radius=6378.14)
            total_distance += segment_distance
        map.fit_bounds([locations])
        folium.PolyLine(locations=locations, color=route.route_color_code, popup={f'{route.metro_route_id},{route.route_name},{int(total_distance)}Kms'}, weight=5).add_to(map)
        # map.add_child(marker_group)
        # map.save("metro_map.html")
        route_id=route.metro_route_id
        context ={"site_title": admin.site.site_title, "site_version": admin.site.site_version,'map':map._repr_html_(),"stations":stations, "routes":routes, "route_id":route_id, "city1":city1, "city":city}
        return render(request,'admin/metro_map.html',context)
    except Exception as e:
        print(e)
        city1 = Cities.objects.exclude(id=8).filter(is_metro='t')
        # map.save("metro_map.html")
        map = folium.Map(location=(12.97750000,77.57180000), zoom_start=15)
        Fullscreen = folium.plugins.Fullscreen(position='topright').add_to(map)
        routes = MetroRoute.objects.all()
        context ={"site_title": admin.site.site_title, "site_version": admin.site.site_version,'map':map._repr_html_(),"stations":stations,"routes":routes, "city1":city1, "city":city}
        return render(request,'admin/metro_map.html',context)

def selectCity(request, id: int):
        city = Cities.objects.filter(id=id).first()
        # print(city.name)
        db_name = city.db_name
        city_name = city.name
        request.session["db_name"] = db_name
        request.session["city_name"] = city_name
        conf.settings.DATABASES['master']['NAME'] = db_name
        return redirect ('/metro/')
        # return redirect ('/admin/' % (city._meta.app_label, city._meta.model_name))

# def get_urls(self):
#     urls = super().get_urls()
#     my_urls = [
#         path('selectCity/<int:id>', self.admin_view(self.selectCity), name="selectCity"),
#     ]
#     return my_urls + urls

def select_city(self, obj):
    return format_html('<a href="{}" class="link">Select</a>',
        reverse_lazy("metro:selectCity", args=[obj.id])
        )