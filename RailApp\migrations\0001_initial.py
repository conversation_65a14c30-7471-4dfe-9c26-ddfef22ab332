# Generated by Django 4.0.6 on 2022-07-15 06:25

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='RailRoute',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=255)),
                ('line', models.CharField(blank=True, max_length=255, null=True)),
                ('status', models.CharField(max_length=50)),
                ('service', models.CharField(blank=True, max_length=50, null=True)),
                ('direction', models.CharField(max_length=50)),
                ('is_main_route', models.TextField()),
                ('description', models.TextField(blank=True, null=True)),
            ],
            options={
                'db_table': 'rail_route',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='RailSchedule',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False)),
                ('route_id', models.BigIntegerField()),
                ('start_time', models.TimeField()),
                ('end_time', models.TimeField()),
                ('train_no', models.CharField(max_length=15)),
                ('day_type', models.CharField(max_length=50)),
                ('train_type', models.CharField(max_length=50)),
                ('status', models.CharField(max_length=50)),
                ('service', models.CharField(blank=True, max_length=50, null=True)),
            ],
            options={
                'db_table': 'rail_schedule',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='RailSchedulePoint',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False)),
                ('route_id', models.BigIntegerField()),
                ('schedule_id', models.BigIntegerField()),
                ('station_id', models.BigIntegerField()),
                ('seq', models.IntegerField(blank=True, null=True)),
                ('start_time', models.TimeField()),
                ('status', models.CharField(max_length=50)),
                ('service', models.CharField(blank=True, max_length=50, null=True)),
                ('platform_no', models.CharField(blank=True, max_length=10, null=True)),
            ],
            options={
                'db_table': 'rail_schedule_point',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='RailStation',
            fields=[
                ('id', models.BigIntegerField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=255)),
                ('latitude', models.FloatField()),
                ('longitude', models.FloatField()),
                ('status', models.CharField(max_length=50)),
                ('service', models.CharField(blank=True, max_length=50, null=True)),
                ('code', models.CharField(blank=True, max_length=255, null=True)),
            ],
            options={
                'db_table': 'rail_station',
                'managed': False,
            },
        ),
    ]
