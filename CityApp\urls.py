from django.urls import path

from . import views
from django.contrib import admin



admin.site.index_title = 'Choose A City'

urlpatterns = [
    path("transit_cms/", views.index, name="index"),
    path("login/", views.login_view, name="login"),
    path("logout/", views.logout_view, name="logout"),
    path("bus_route/", views.bus_route, name="bus_route"),
    path("bus_stop/", views.bus_stop, name="bus_stop"),
    path("bus_route_point/", views.bus_route_point, name="bus_route_point"),
    path("bus_schedule_point/", views.bus_schedule_point, name="bus_schedule_point"),
    path("bus_service_type/", views.bus_service_type, name="bus_service_type"),
    path("metro_route/", views.metro_route, name="metro_route"),
    path("metro_fare/", views.metro_fare, name="metro_fare"),
    path("metro_station/", views.metro_station, name="metro_station"),
    path("metro_time/", views.metro_time, name="metro_time"),
    path("rail_route/", views.rail_route, name="rail_route"),
    path("rail_schedule/", views.rail_schedule, name="rail_schedule"),
    path("rail_schedule_point/", views.rail_schedule_point, name="rail_schedule_point"),
    path("rail_station/", views.rail_station, name="rail_station"),
    path("transit/", views.transit, name="transit"),
    path("view_selection/", views.view_selection, name="view_selection"),
    path('imports/',views.imports,name="imports"),
    path('imports/',views.imports,name="imports")
]