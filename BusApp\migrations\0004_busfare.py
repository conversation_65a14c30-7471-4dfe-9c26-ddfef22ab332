# Generated by Django 4.2.3 on 2023-09-15 06:59

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('BusApp', '0003_alter_busroute_options_alter_busroutepoint_options_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='BusFare',
            fields=[
                ('fare_chart_id', models.AutoField(primary_key=True, serialize=False)),
                ('start_point_id', models.IntegerField()),
                ('end_point_id', models.IntegerField()),
                ('number_of_kms', models.IntegerField()),
                ('fare_amount', models.FloatField(blank=True, null=True)),
                ('toll_fee', models.FloatField(blank=True, null=True)),
                ('status', models.CharField(blank=True, max_length=50)),
            ],
            options={
                'verbose_name_plural': 'Bus Fare',
                'db_table': 'bus_fare',
                'managed': False,
            },
        ),
    ]
