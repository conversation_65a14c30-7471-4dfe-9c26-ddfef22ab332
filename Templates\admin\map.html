{% extends "admin/base.html" %}
{% load static %}



{% block content %}

{{ block.super }}
<head>
    <!-- <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-EVSTQN3/azprG1Anm3QDgpJLIm9Nao0Yz1ztcQTwFspd3yD65VohhpuuCOmLASjC" crossorigin="anonymous"> -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js" integrity="sha384-MrcW6ZMFYlzcLA8Nl+NtUVF0sA7MsXsP1UyJoMp4YLEuNSfAP+JcXn/tWtIaxVXM" crossorigin="anonymous"></script>
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />

   <style>
    /* .container {
        display: flex;
} */
.custom-select {
  min-width: 350px;
}
/* 
body {
  background: #f0f0f0;
  padding: 50px;
} */

.container {
  background: #fff;
  border: 1px solid #ccc;
  padding: 30px;
}

.form-control {
    border: 1px solid #ccc;
    border-radius: 3px;
    box-shadow: none !important;
    margin-bottom: 15px;
}

.form-control:focus {
    border: 1px solid #34495e;
}

#myForm select { 
width:450px; }

#myForm select:focus {
width:auto; }


   </style>
    
</head>
<body>
    <!-- <div class="container"> -->
    <label for="module" class="form-group">Module:</label> 
         <select id="module" name="module" onchange="redirectToUrl()" required>
        {% if city.is_bus == True and city.is_metro == True and city.is_rail == True %}
                <option value="Bus" selected>
                    {{ city.name }} - Bus
                </option>
                <option value="Metro">
                    {{ city.name }} - Metro
                </option>
                <option value="Rail" >
                    {{ city.name }} - Rail
                </option>
            {% elif city.is_bus == True and city.is_metro == True and city.is_rail == False %}
                <option value="Bus" selected>
                    {{ city.name }} - Bus
                </option>
                <option value="Metro">
                    {{ city.name }} - Metro
                </option>

            {% else %}
            <option value="Bus" selected>
                {{ city.name }} - Bus
            </option>

            {% endif %}
        </select>
        
        <form id="myForm" action="{% url 'get_bus_route' %}">
        <div class="form-group">
          <label for="Routes">Route:</label>
            <select class="form-control js-select2-multi" name="Routes" id="Routes" multiple="multiple">
                <option>{{seleted_routes}}</option>
                {% for route in routes %}
                <option value="{{route.route_id}}">
                    <span>Route ID:</span>{{route.route_id}} -<span>Route NO:</span>{{route.route_number}} -<span>Route Name:</span>{{route.route_name}}</option>
            {% endfor %}
            </select>
          
        <input type="submit" class="btn btn-primary" value="Submit"/>
      </div>
    </form>
        
<br>
        <div style="text-align:center">{{msg|upper}}</div>
<div id="map-container">
    {{ map|safe }}
</div>

<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script src="https://unpkg.com/folium@0.12.1"></script>

</body>
<script>

   
function redirectToUrl() {
    // Get the selected option
    var selectElement = document.getElementById("module");
    var selectedValue = selectElement.value;

    // Define URLs for each option
    var url1 = window.location.origin + '/bus/';
    var url2 = window.location.origin + '/metro/';
    var url3 = window.location.origin + "/rail/";

    // Redirect to the URL based on the selected option
    if (selectedValue === "Bus") {
        window.location.href = url1;
      } else if (selectedValue === "Metro") {
        window.location.href =  url2;
      } else if (selectedValue === "Rail") {
        window.location.href = url3;
      }
}


$(document).ready(function() {
  $(".js-select2-multi").select2({
    closeOnSelect: false
  });
});

</script>

{% endblock %}