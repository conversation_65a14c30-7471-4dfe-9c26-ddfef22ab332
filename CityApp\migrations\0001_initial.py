# Generated by Django 4.2.3 on 2023-08-02 05:52

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Cities',
            fields=[
                ('id', models.IntegerField(primary_key=True, serialize=False)),
                ('name', models.Char<PERSON>ield(blank=True, max_length=100, null=True)),
                ('code', models.CharField(blank=True, max_length=10, null=True)),
                ('alias', models.CharField(blank=True, max_length=500, null=True)),
                ('db_name', models.CharField(blank=True, max_length=100, null=True)),
                ('is_bus', models.BooleanField(default=False)),
                ('is_metro', models.BooleanField(default=False)),
                ('is_rail', models.BooleanField(default=False)),
            ],
            options={
                'verbose_name_plural': 'Cities',
                'db_table': 'cities',
                'permissions': (('is_city_trivandrum', 'Hide cities for this city'),),
                'managed': False,
            },
        ),
    ]
