<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>


{% extends "admin/base.html" %}
{% load static %}



{% block header %}
<link rel="shortcut icon" href="{% static 'favicon.ico/favicon.png' %}"  type="image/x-icon"/>
{% block extrastyle %}


  {{ block.super }}
  <style>
    :root {
      --primary: #4d4d4d;
      --secondary: #2596be;
      --accent: #4dca92;
      --primary-fg: #fff; /* for the text*/
      --header-color: #ccffe8;
      /* --darkened-bg: #dffcee; for entire background */
    }
  </style>
  {% endblock %}

<style>
  body {
    margin: 0;
    font-family: Arial, Helvetica, sans-serif;
  }
  
  .topnav {
    position: relative;
    overflow: hidden;
    background-color: #11b4bc;
  }
  
  .topnav a {
    float: left;
    color: white;
    text-align: center;
    padding: 14px 16px;
    text-decoration: none;
    font-size: 17px;
  }

  
  .topnav-right {
    float: right;
  }

  .version{
    margin-left: -10px; 
    font-size: 18px; 
    font-weight: bold; 
    color: #333;
  }

  #grad1 {
  background-color: #11b4aa; /* For browsers that do not support gradients */
  background-image: linear-gradient(to right, #15cfd9, #91bd4f, #ffc505);
}

  .city {
    position: absolute;
    padding-left: 345px;
    top: 40px;
    right: 16px;
    font-size: 18px;

}

  .module caption {
    background: black;
  }
  div.breadcrumbs{
    background: black;
  }
  input[type=submit]{
    background: black;
  }
  </style>

<script>
  function runFunction(selectElement) {
      var selectedValue = selectElement.value;
      var url = "selectCity/" + selectedValue; // Replace with your actual URL
      selectElement.options[0].text = "Selected: " + selectedValue;
      window.location.href = url;
  }
        document.body.addEventListener('htmx:configRequest', (event) => {
        event.detail.headers['X-CSRFToken'] = '{{ csrf_token }}';
        })
</script>


<div class="topnav" id="grad1">
  
  <a href="/" ><img src="{% static 'tummoc-logo.png' %}" height="50px" style="vertical-align: middle;"/><span style="font-size:20px;">{{ site_title }}</span><br><span class="version">Version - {{ site_version }}</span></a>

  <div class="topnav-right">
    <div>
        {% if user.get_username %}
          {% block userlinks %}
            <a  style="text-align: unset;">
              <span>Welcome, {{  user.get_username }}</span> &nbsp;

              <span>{% if request.session.city_name %}City - {{ request.session.city_name }}{% endif %}
              </a>
              </span>
            
            {% comment %} {{block.super}} {% endcomment %}
            <a class="nav-link" href="{% url 'bus'%}">Map View</a>
            <a href="{% url 'password_change'%}">Change Password</a>
            <a href="{% url 'logout'%}">Logout</a>
            <span><div class="city">
              <select  id="select_cities" onchange="runFunction(this)">
                {% if request.session.city_name %}<option value="Choose a city" disabled selected>{{request.session.city_name}}</option>{% endif %}
            
                {% for city in city1 %}
                    <option value="{{city.id}}">{{city.name}}</option>
                {% endfor %}
            </select>
        </div></span>
            <!-- <br> -->

            {% comment %} {% include "admin/color_theme_toggle.html" %} {% endcomment %}
            
            

            
            
          {% endblock %}
        {% endif %}
    </div>
      
  
  </div>
</div>


{% endblock %}




  


