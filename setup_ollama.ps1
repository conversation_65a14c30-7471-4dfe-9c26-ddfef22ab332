# PowerShell script to install and setup Ollama for FileProcessorApp
# Run this script as Administrator

Write-Host "🚀 Setting up Ollama for FileProcessorApp" -ForegroundColor Green
Write-Host "=" * 50

# Check if running as Administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")

if (-not $isAdmin) {
    Write-Host "⚠️  This script needs to be run as Administrator" -ForegroundColor Yellow
    Write-Host "Please right-click PowerShell and select 'Run as Administrator'" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Step 1: Download Ollama
Write-Host "`n📥 Step 1: Downloading Ollama..." -ForegroundColor Cyan

$ollamaUrl = "https://ollama.ai/download/windows"
$ollamaInstaller = "$env:TEMP\OllamaSetup.exe"

try {
    Write-Host "Downloading from: $ollamaUrl"
    Invoke-WebRequest -Uri $ollamaUrl -OutFile $ollamaInstaller -UseBasicParsing
    Write-Host "✅ Ollama downloaded successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to download Ollama: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Please download manually from: https://ollama.ai/download" -ForegroundColor Yellow
    Read-Host "Press Enter to continue with manual installation"
}

# Step 2: Install Ollama
Write-Host "`n🔧 Step 2: Installing Ollama..." -ForegroundColor Cyan

if (Test-Path $ollamaInstaller) {
    try {
        Write-Host "Running installer..."
        Start-Process -FilePath $ollamaInstaller -Wait
        Write-Host "✅ Ollama installation completed" -ForegroundColor Green
    } catch {
        Write-Host "❌ Installation failed: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "⚠️  Installer not found. Please install Ollama manually from https://ollama.ai/download" -ForegroundColor Yellow
}

# Step 3: Wait for Ollama to be available
Write-Host "`n⏳ Step 3: Waiting for Ollama to be available..." -ForegroundColor Cyan

$maxAttempts = 30
$attempt = 0

do {
    $attempt++
    Write-Host "Attempt $attempt/$maxAttempts - Checking Ollama..."
    
    try {
        $result = & ollama --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Ollama is available: $result" -ForegroundColor Green
            break
        }
    } catch {
        # Ollama not yet available
    }
    
    if ($attempt -lt $maxAttempts) {
        Write-Host "Waiting 5 seconds..."
        Start-Sleep -Seconds 5
    }
} while ($attempt -lt $maxAttempts)

if ($attempt -eq $maxAttempts) {
    Write-Host "❌ Ollama is not responding after installation" -ForegroundColor Red
    Write-Host "Please restart your computer and try again" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Step 4: Start Ollama service
Write-Host "`n🔄 Step 4: Starting Ollama service..." -ForegroundColor Cyan

try {
    Write-Host "Starting Ollama service in background..."
    Start-Process -FilePath "ollama" -ArgumentList "serve" -WindowStyle Hidden
    Start-Sleep -Seconds 5
    Write-Host "✅ Ollama service started" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Could not start Ollama service automatically" -ForegroundColor Yellow
    Write-Host "You may need to run 'ollama serve' manually" -ForegroundColor Yellow
}

# Step 5: Download recommended models
Write-Host "`n📦 Step 5: Downloading AI models..." -ForegroundColor Cyan

$models = @(
    @{name="llama2"; description="General purpose model (3.8GB)"; priority=1},
    @{name="phi"; description="Lightweight model (1.6GB)"; priority=2},
    @{name="mistral"; description="Fast and efficient (4.1GB)"; priority=3}
)

Write-Host "Available models for download:"
for ($i = 0; $i -lt $models.Count; $i++) {
    $model = $models[$i]
    Write-Host "  $($i+1). $($model.name) - $($model.description)"
}

Write-Host "`nRecommended: Start with 'phi' (lightweight) or 'llama2' (more capable)"
$choice = Read-Host "Enter model number to download (1-3), or 'all' for all models, or 'skip' to skip"

$modelsToDownload = @()

switch ($choice.ToLower()) {
    "1" { $modelsToDownload = @("llama2") }
    "2" { $modelsToDownload = @("phi") }
    "3" { $modelsToDownload = @("mistral") }
    "all" { $modelsToDownload = @("phi", "llama2", "mistral") }
    "skip" { 
        Write-Host "⏭️  Skipping model download" -ForegroundColor Yellow
        $modelsToDownload = @()
    }
    default { 
        Write-Host "Invalid choice. Downloading 'phi' (recommended for testing)"
        $modelsToDownload = @("phi")
    }
}

foreach ($modelName in $modelsToDownload) {
    Write-Host "`n📥 Downloading model: $modelName..." -ForegroundColor Cyan
    try {
        & ollama pull $modelName
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Model '$modelName' downloaded successfully" -ForegroundColor Green
        } else {
            Write-Host "❌ Failed to download model '$modelName'" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ Error downloading model '$modelName': $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Step 6: Test the setup
Write-Host "`n🧪 Step 6: Testing Ollama setup..." -ForegroundColor Cyan

if ($modelsToDownload.Count -gt 0) {
    $testModel = $modelsToDownload[0]
    Write-Host "Testing with model: $testModel"
    
    try {
        Write-Host "Sending test prompt..."
        $testResult = & ollama run $testModel "Hello, can you help with transit data analysis?" --verbose
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Ollama is working correctly!" -ForegroundColor Green
            Write-Host "Response preview: $($testResult | Select-Object -First 100)..."
        } else {
            Write-Host "⚠️  Test completed but may have issues" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "⚠️  Test failed: $($_.Exception.Message)" -ForegroundColor Yellow
    }
} else {
    Write-Host "⏭️  Skipping test (no models downloaded)" -ForegroundColor Yellow
}

# Step 7: Setup summary
Write-Host "`n" + "=" * 50
Write-Host "🎉 Ollama Setup Complete!" -ForegroundColor Green
Write-Host "=" * 50

Write-Host "`n📋 Setup Summary:"
Write-Host "✅ Ollama installed and configured"
Write-Host "✅ Service started (running in background)"

if ($modelsToDownload.Count -gt 0) {
    Write-Host "✅ AI models downloaded: $($modelsToDownload -join ', ')"
} else {
    Write-Host "⚠️  No AI models downloaded"
}

Write-Host "`n🔧 Next Steps:"
Write-Host "1. The Ollama service is now running in the background"
Write-Host "2. FileProcessorApp will automatically detect and use Ollama"
Write-Host "3. Upload files to test AI-powered transit data extraction"
Write-Host "4. If needed, restart Ollama with: ollama serve"

Write-Host "`n📚 Useful Commands:"
Write-Host "• Check status: ollama list"
Write-Host "• Download model: ollama pull <model-name>"
Write-Host "• Test model: ollama run <model-name> 'test prompt'"
Write-Host "• Start service: ollama serve"

Write-Host "`n🌐 FileProcessorApp URLs:"
Write-Host "• Dashboard: http://localhost:8000/fileprocessor/"
Write-Host "• Upload: http://localhost:8000/fileprocessor/upload/"
Write-Host "• Admin: http://localhost:8000/admin/"

Write-Host "`n✨ Ollama is ready for AI-powered transit data processing!" -ForegroundColor Green

Read-Host "`nPress Enter to exit"
