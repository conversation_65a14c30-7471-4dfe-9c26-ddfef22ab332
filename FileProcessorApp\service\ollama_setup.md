# Ollama Setup Guide for Local AI Processing

This guide explains how to set up Ollama for local AI-powered document analysis in the File Processor App.

## What is Ollama?

Ollama is a tool that allows you to run large language models locally on your machine. This provides:
- Privacy (no data sent to external services)
- No API costs
- Offline functionality
- Fast processing for document analysis

## Installation

### Windows
1. Download Ollama from: https://ollama.ai/download
2. Run the installer
3. Ollama will be available in your system PATH

### macOS
```bash
brew install ollama
```

### Linux
```bash
curl -fsSL https://ollama.ai/install.sh | sh
```

## Setting up Models

After installing Ollama, you need to download AI models. The File Processor App supports several models:

### Recommended Models

1. **Llama 2** (Default, good balance of performance and accuracy)
```bash
ollama pull llama2
```

2. **Llama 3** (Better performance, larger size)
```bash
ollama pull llama3
```

3. **Mistral** (Fast and efficient)
```bash
ollama pull mistral
```

4. **Phi** (Lightweight, good for basic analysis)
```bash
ollama pull phi
```

### Starting Ollama Service

Start the Ollama service:
```bash
ollama serve
```

The service will run on `http://localhost:11434` by default.

## Testing the Setup

You can test if Ollama is working correctly:

```bash
# Test with a simple prompt
ollama run llama2 "Hello, how are you?"
```

## Configuration in Django

The File Processor App will automatically detect and use Ollama if it's available. The AI analyzer will:

1. Try to connect to Ollama on localhost:11434
2. Test available models (llama2, llama3, mistral, phi)
3. Use the first available model
4. Fall back to rule-based analysis if no AI models are available

## Model Selection

You can specify which model to use by modifying the `AIAnalyzer` initialization in `ai_analyzer.py`:

```python
# Use a specific model
analyzer = AIAnalyzer(model_name="llama3")
```

## Performance Considerations

- **RAM Requirements**: 
  - Llama 2: ~4GB RAM
  - Llama 3: ~8GB RAM
  - Mistral: ~4GB RAM
  - Phi: ~2GB RAM

- **Processing Speed**: Depends on your hardware (CPU/GPU)
- **First Run**: Models are downloaded on first use (can be large files)

## Troubleshooting

### Common Issues

1. **"Model not found" error**
   - Make sure you've pulled the model: `ollama pull llama2`
   - Check available models: `ollama list`

2. **Connection refused**
   - Ensure Ollama service is running: `ollama serve`
   - Check if port 11434 is available

3. **Slow processing**
   - Try a smaller model like `phi`
   - Ensure sufficient RAM is available

4. **Python import errors**
   - Install the ollama Python package: `pip install ollama`

### Logs

Check the Django logs for AI processing information:
- Successful model detection
- Fallback to rule-based analysis
- Processing errors

## Alternative: Rule-based Analysis

If you don't want to use AI or can't install Ollama, the File Processor App will automatically fall back to rule-based analysis using keyword matching. This provides basic transit data identification without requiring AI models.

## Security Notes

- All processing happens locally on your machine
- No data is sent to external services
- Models and data remain on your local system
- Suitable for sensitive transit data processing

## Model Updates

To update models:
```bash
ollama pull llama2  # Re-download latest version
```

To remove models:
```bash
ollama rm llama2
```

To see model information:
```bash
ollama show llama2
```
