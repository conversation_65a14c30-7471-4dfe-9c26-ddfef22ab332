from django.contrib import admin
from CityApp.models import Cities
from BusApp.models import RouteVehicleMap
from BusApp.admin import RouteVehicleMapAdmin
from RailApp.models import RailRoute,RailSchedule,RailSchedulePoint,RailStation,RailStationCross,RailLineMaster
from RailApp.admin import RailRouteAdmin,RailScheduleAdmin,RailSchedulePointAdmin,RailStationAdmin,RailStationCrossAdmin,RailLineMasterAdmin
from MetroApp.models import MetroFare,MetroRoute,Station,Times
from MetroApp.admin import MetroFareAdmin,MetroRouteAdmin,StationAdmin,TimesAdmin
 # from ImportApp.models import Imports
# from ImportApp.admin import ImportsAdmin
import django.conf as conf
from django.contrib.auth import get_permission_codename
from django.contrib.admin.models import LogEntry,  ADDITION, DELETION
from django.contrib.contenttypes.models import ContentType
from django.utils.html import escape
from django.urls import reverse
from django.utils.safestring import mark_safe
import random
from django.utils.html import format_html
from django import forms
from django.urls import reverse
from django.urls import reverse_lazy
from django.urls import path
from django.shortcuts import redirect
from django.utils import timezone
from django.contrib import messages
from django.http import HttpResponseRedirect

# from django.urls import reverse

# Register your models here.
class CityAdmin(admin.ModelAdmin):
    list_display = ('id', 'name','code', 'alias','is_bus','is_metro','is_rail')
    readonly_fields = ('id', 'name','code', 'alias', 'db_name','is_bus','is_metro','is_rail')
    search_fields = ('name','alias')
    ordering = ('id',)
    list_per_page = 10
    actions = ['make_selected']
    
    ################### TO GIVE SPECIFIC CITY  ONLY FOR A USER ########################
    # def get_model_perms(self, request):
    #     """
    #     Return empty perms dict thus hiding the model from admin index.
    #     """
    #     all_perms = super(CityAdmin, self).get_model_perms(request)
        
    #     if request.user.is_superuser:
    #         return all_perms
        
    #     requiredPerm = self.opts.app_label + '.is_city_trivandrum'
    #     print('Wew', 'requiredPerm=',requiredPerm, (request.user.has_perm(requiredPerm)), request.user.is_superuser)
        
    #     if request.user.has_perm(requiredPerm):
    #         return {}
    #     return all_perms
    
    def selectCity(self, request, id: int):
        city = Cities.objects.filter(id=id).first()
        print(city.name)
        db_name = city.db_name
        city_name = city.name
        request.session["db_name"] = db_name
        request.session["city_name"] = city_name

        conf.settings.DATABASES['master']['NAME'] = db_name
        return redirect ('/admin/')
        # return redirect ('/admin/' % (city._meta.app_label, city._meta.model_name))

    def get_urls(self):
        urls = super().get_urls()
        my_urls = [
            path('selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),
        ]
        return my_urls + urls
    
    def select_city(self, obj):
        return format_html('<a href="{}" class="link">Select</a>',
            reverse_lazy("admin:selectCity", args=[obj.id])
        )
    
    
    def make_selected(self, request, queryset):
        city = queryset.first()
        user_id = request.user.id
        city_id = city.id
        db_name = city.db_name
        city_name = city.name
        request.session["db_name"] = db_name
        request.session["city_name"] = city_name

        conf.settings.DATABASES['master']['NAME'] = db_name

        if db_name == "adapter_trivan":
            if (admin.site.is_registered(RouteVehicleMap) == True ):
                pass
            else:
                admin.site.register(RouteVehicleMap,RouteVehicleMapAdmin)
        else:
            if (admin.site.is_registered(RouteVehicleMap) == False ):
                pass
            else:
                admin.site.unregister(RouteVehicleMap)

        if city.is_metro == False:
            if (admin.site.is_registered(MetroFare) == False and admin.site.is_registered(MetroRoute) == False and admin.site.is_registered(Station) == False and admin.site.is_registered(Times) == False):
                pass
            else:
                admin.site.unregister(MetroFare)
                admin.site.unregister(MetroRoute)
                admin.site.unregister(Station)
                admin.site.unregister(Times)
        else:
            if (admin.site.is_registered(MetroFare) == True and admin.site.is_registered(MetroRoute) == True and admin.site.is_registered(Station) == True and admin.site.is_registered(Times) == True):
                pass
            else:
                admin.site.register(MetroFare,MetroFareAdmin)
                admin.site.register(MetroRoute,MetroRouteAdmin)
                admin.site.register(Station,StationAdmin)
                admin.site.register(Times,TimesAdmin)

        if city.is_rail == False:
            if (admin.site.is_registered(RailRoute) == False and admin.site.is_registered(RailSchedule) == False and admin.site.is_registered(RailSchedulePoint) == False and admin.site.is_registered(RailStation) == False and admin.site.is_registered(RailStationCross) == False and admin.site.is_registered(RailLineMaster) == False):
                pass
            else:
                admin.site.unregister(RailRoute)
                admin.site.unregister(RailSchedule)
                admin.site.unregister(RailSchedulePoint)
                admin.site.unregister(RailStation)
                admin.site.unregister(RailStationCross)
                admin.site.unregister(RailLineMaster)
        else:
            if (admin.site.is_registered(RailRoute) == True and admin.site.is_registered(RailSchedule) == True and admin.site.is_registered(RailSchedulePoint) == True and admin.site.is_registered(RailStation) == True and admin.site.is_registered(RailStationCross) == True and admin.site.is_registered(RailLineMaster) == True):
                pass
            else:
                admin.site.register(RailRoute,RailRouteAdmin)
                admin.site.register(RailSchedule,RailScheduleAdmin)
                admin.site.register(RailSchedulePoint,RailSchedulePointAdmin)
                admin.site.register(RailStation,RailStationAdmin)
                admin.site.register(RailStationCross,RailStationCrossAdmin)
                admin.site.register(RailLineMaster,RailLineMasterAdmin)
        
            

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False    

    def has_selected_permission(self, request):
        """Does the user have the publish permission?"""
        opts = self.opts
        codename = get_permission_codename('selected', opts)
        return request.user.has_perm('%s.%s' % (opts.app_label, codename))
    
    def get_actions(self, request):
        actions = super().get_actions(request)
        opts = self.opts
        if 'delete_selected' in actions:
            del actions['delete_selected']
        return actions
    
    def changelist_view(self, request, extra_context=None):
        if "db_name" in request.session.keys():
            db_name = request.session["db_name"]
            obj = Cities.objects.get(db_name = db_name)
            extra_context = {'title': 'Selected City - ' + obj.name}
        else:
            extra_context = {'title': 'Select City'}

        try:
            return super(CityAdmin, self).changelist_view(request, extra_context=extra_context)
        except Exception as e:
            message = e
            messages.add_message(request, messages.INFO,message)
            previous_url = request.META.get('HTTP_REFERER', reverse('admin:index'))
            return HttpResponseRedirect(previous_url)

# admin.site.unregister(CityAdmin)
admin.site.register(Cities,CityAdmin)


class LogEntryAdmin(admin.ModelAdmin):
    date_hierarchy = 'action_time'
    list_filter = [
        'user', 'content_type', 'action_flag'
    ]
    search_fields = [
        'object_repr', 'change_message'
    ]
    list_display = [
        'action_time','object_id','object_repr','action_flag','change_message','content_type_id','user',
    ]
    def has_add_permission(self, request):
        return False
    def has_change_permission(self, request, obj=None):
        return False
    def has_delete_permission(self, request, obj=None):
        return False
    def has_view_permission(self, request, obj=None):
        return request.user.is_superuser

    def create_addition_log_entry(self, request, object_repr):
        content_type = ContentType.objects.get_for_model(LogEntry)
        LogEntry.objects.create(
            action_time=timezone.now(),
            user=request.user,
            content_type=content_type,
            object_id=None,
            object_repr=object_repr,
            action_flag=ADDITION,
            change_message="Custom addition log entry"
        )

    def create_deletion_log_entry(self, request, object_repr):
        content_type = ContentType.objects.get_for_model(LogEntry)
        LogEntry.objects.create(
            action_time=timezone.now(),
            user=request.user,
            content_type=content_type,
            object_id=None,
            object_repr=object_repr,
            action_flag=DELETION,
            change_message="Custom deletion log entry"
        )

    def selectCity(self, request, id: int):
        city = Cities.objects.filter(id=id).first()
        db_name = city.db_name
        city_name = city.name
        request.session["db_name"] = db_name
        request.session["city_name"] = city_name
        conf.settings.DATABASES['master']['NAME'] = db_name
        return redirect ('/admin/')
        # return redirect ('/admin/' % (city._meta.app_label, city._meta.model_name))

    def get_urls(self):
        urls = super().get_urls()
        my_urls = [
            path('selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),
            
        ]
        return my_urls + urls

    def select_city(self, obj):
        return format_html('<option value="{{ city.id }}">',
            reverse_lazy("admin:selectCity", args=[obj.id])
        )
        
admin.site.register(LogEntry,LogEntryAdmin)






