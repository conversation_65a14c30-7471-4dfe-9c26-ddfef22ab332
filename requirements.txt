amqp==5.2.0
asgiref==3.7.2
async-timeout==4.0.3
billiard==4.2.0
branca==0.7.0
celery==5.3.6
certifi==2023.11.17
charset-normalizer==3.3.2
click==8.1.7
click-didyoumean==0.3.0
click-plugins==1.1.1
click-repl==0.3.0
cloudpickle==2.2.1
colorama==0.4.6
coloredlogs==15.0.1
dask==2023.9.1
defusedxml==0.7.1
diff-match-patch==20230430
Django==4.2.3
django-admin-list-filter-dropdown==1.0.3
django-adminlte3==0.1.6
django-bitfield==2.2.0
django-cors-headers==4.2.0
django-import-export==3.2.0
django-loading==0.1.1
django-progressbarupload==0.1.7
django-session-timeout==0.1.0
et-xmlfile==1.1.0
folium==0.15.1
fsspec==2023.9.0
geographiclib==2.0
geopy==2.4.1
humanfriendly==10.0
idna==3.6
importlib-metadata==6.8.0
Jinja2==3.1.2
kombu==5.3.4
locket==1.0.0
MarkupPy==1.14
MarkupSafe==2.1.3
numpy==1.25.2
odfpy==1.4.1
openpyxl==3.1.2
packaging==23.1
pandas==2.0.3
partd==1.4.0
polyline==2.0.1
prompt-toolkit==3.0.41
psutil==5.9.6
psycopg2-binary==2.9.6
pyreadline3==3.4.1
python-dateutil==2.8.2
python-dotenv==1.0.0
pytz==2023.3
PyYAML==6.0.1
requests==2.31.0
six==1.16.0
sniffio==1.3.0
sqlparse==0.4.4
tablib==3.5.0
toolz==0.12.0
tqdm==4.66.1
typing_extensions==4.9.0
tzdata==2023.3
urllib3==2.1.0
vine==5.1.0
wcwidth==0.2.12
xlrd==2.0.1
xlwt==1.3.0
xyzservices==2023.10.1
zipp==3.16.2
osmnx==1.9.1
django_select2==8.1.2
whitenoise==6.6.0
# File processing dependencies
PyPDF2==3.0.1
python-docx==0.8.11
Pillow==10.0.0
pdfplumber==0.9.0
tabula-py==2.8.2
pytesseract==0.3.10
# AI and ML dependencies
ollama==0.1.7
# GTFS processing
gtfs-kit==6.1.0