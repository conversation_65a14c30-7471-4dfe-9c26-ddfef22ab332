from django.shortcuts import render,redirect
import folium
from django.urls import reverse_lazy
from django.utils.html import format_html
import geopy
# from geopy.geocoders import Nominatim
from geopy.distance import geodesic
from folium import Map, FeatureGroup, Marker, LayerControl, plugins
from CityApp.models import *
from BusApp.models import *
from MetroApp.models import *
from RailApp.models import *
import polyline
from django.contrib import admin
from urllib import request
from django import conf
from django.shortcuts import render, get_object_or_404
from django.http import JsonResponse, HttpResponse
from .models import *
# from geopy.geocoders import Nominatim
import osmnx as ox
# import geopy.distance as distance

from django.http import JsonResponse
from django.template.loader import render_to_string
from django.contrib.auth.decorators import login_required, permission_required


# Create your views here.

@login_required
def bus(request):
    name = request.session['city_name']
    city = Cities.objects.get(name=name)
    city1 = Cities.objects.exclude(id=8).filter(is_bus='t')
    stops = BusStop.objects.all().first()
    # print(stops)
    location = (stops.latitude_current, stops.longitude_current)
    map = folium.Map(location=location, zoom_start=15, height=500)
    Fullscreen = folium.plugins.Fullscreen(position='topright').add_to(map)
    routes = BusRoute.objects.all()
    try:
        marker = folium.Marker(location=location, popup=("Stop ID",stops.bus_stop_id,"Stop Name",stops.bus_stop_name),color='blue', zoom_start=15).add_to(map)
        # map.save("map.html")
        context ={"site_title": admin.site.site_title, "site_version": admin.site.site_version,'map':map._repr_html_(),"routes":routes, "city1":city1,"city":city}
        return render(request,'admin/map.html',context)
    except Exception as e:
        city1 = Cities.objects.exclude(id=8).filter(is_bus='t')
        # print(e)
        bus_stops = BusStop.objects.all().first()
        marker = folium.Marker(location=(bus_stops.latitude_current, bus_stops.longitude_current),color='blue', zoom_start=15).add_to(map)
        # map.save("map.html")
        context ={"site_title": admin.site.site_title, "site_version": admin.site.site_version,'map':map._repr_html_(),"routes":routes, "city1":city1,'"city':city}
        return render(request,'admin/map.html',context)


@login_required
def get_bus_route(request):
    try:
        name = request.session['city_name']
        city = Cities.objects.get(name=name)
        city1 = Cities.objects.exclude(id=8).filter(is_bus='t')
        map = folium.Map(zoom_start=15, height=500)
        Fullscreen = folium.plugins.Fullscreen(position='topright').add_to(map)
        rout = BusRoute.objects.all()
        routes = request.GET.getlist('Routes')
        if not routes:
            # Handle the case where 'Routes' parameter is not present
            # You may want to return an error response or provide a default behavior
            return HttpResponse("Routes parameter is missing", status=400)

        for index, route_id in enumerate(routes):
            route = BusRoute.objects.get(route_id=route_id)
            routeID = route.route_id
            route_name = route.route_name
            route_points = BusRoutePoint.objects.filter(route_id=route).order_by('route_order')
            locations = []
            first = route_points.first()
            last = route_points.last()

            for i in route_points:
                stop = BusStop.objects.get(bus_stop_id=i.bus_stop_id.bus_stop_id)
                bus_stop_id = stop.bus_stop_id
                bus_stop_name = stop.bus_stop_name
                coordinates = (float(stop.latitude_current), float(stop.longitude_current))
                locations.append(coordinates)
                marker_group = folium.FeatureGroup(name=f"Bus_Route_Points_{index}", show=True)
                if i.route_order == 1:
                    origin = (coordinates)
                    folium.Marker(coordinates, tooltip=(stop.bus_stop_id, stop.bus_stop_name), icon=folium.Icon(color='green')).add_to(marker_group)
                elif i.route_order == last.route_order:
                    destination = (coordinates)
                    folium.Marker(coordinates, tooltip=(stop.bus_stop_id, stop.bus_stop_name), icon=folium.Icon(color='red')).add_to(marker_group)
                else:
                    folium.CircleMarker(coordinates, tooltip=(stop.bus_stop_id, stop.bus_stop_name), radius=8, color='white', fill=True, fill_color='black', fill_opacity=0.7).add_to(marker_group)
                total_distance = 0
                for i in range(len(locations) - 1):
                    lat1, lon1 = locations[i]
                    lat2, lon2 = locations[i + 1]
                    segment_distance = ox.distance.great_circle(lat1, lon1, lat2, lon2, earth_radius=6378.14)
                    total_distance += segment_distance
                iframe = folium.IFrame('Route Id:' + str(routeID) + '<br>' + 'Route Name: ' + route_name + '<br>' + 'Distance: ' + str(total_distance)+'kms')
                popup = folium.Popup(iframe, min_width=250, max_width=280)
                viw = folium.PolyLine(locations=locations, color='blue', popup=popup, weight=5).add_to(marker_group)
                map.fit_bounds([locations])
                map.add_child(marker_group)
        context = {"site_title": admin.site.site_title, "site_version": admin.site.site_version, 'map': map._repr_html_(), "routes": rout, "city1": city1, "city": city, "selected_routes":routes}
        return render(request, 'admin/map.html', context)

    except Exception as e:
        print(e)
        msg = "searching route is not available right now!"
        city1 = Cities.objects.exclude(id=8).filter(is_bus='t')
        bus_stops = BusStop.objects.all().first()
        marker = folium.Marker(location=(bus_stops.latitude_current, bus_stops.longitude_current), color='blue', zoom_start=15).add_to(map)
        context = {"site_title": admin.site.site_title, "site_version": admin.site.site_version, 'map': map._repr_html_(), "routes": rout, "city1": city1, "city": city, "msg": msg}
        return render(request, 'admin/map.html', context)




def selectCity(request, id: int):
        city = Cities.objects.filter(id=id).first()
        db_name = city.db_name
        city_name = city.name
        request.session["db_name"] = db_name
        request.session["city_name"] = city_name
        conf.settings.DATABASES['master']['NAME'] = db_name
        return redirect ('/bus/')
        # return redirect ('/admin/' % (city._meta.app_label, city._meta.model_name))



def select_city(self, obj):
    return format_html('<a href="{}" class="link">Select</a>',
        reverse_lazy("bus:selectCity", args=[obj.id])
        )

