from django import forms
from .models import UploadedFile
import os


class FileUploadForm(forms.ModelForm):
    """Form for uploading files"""
    
    class Meta:
        model = UploadedFile
        fields = ['file']
        widgets = {
            'file': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': '.pdf,.csv,.xlsx,.xls,.jpg,.jpeg,.png,.tiff,.txt,.docx,.doc',
                'multiple': False,
            })
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['file'].help_text = (
            "Supported formats: PDF, CSV, Excel (xlsx/xls), Images (jpg/jpeg/png/tiff), "
            "Text files (txt), Word documents (docx/doc). Maximum file size: 50MB."
        )
    
    def clean_file(self):
        file = self.cleaned_data.get('file')
        
        if file:
            # Check file size (50MB limit)
            if file.size > 50 * 1024 * 1024:
                raise forms.ValidationError("File size cannot exceed 50MB.")
            
            # Check file extension
            file_extension = os.path.splitext(file.name)[1].lower().lstrip('.')
            allowed_extensions = [choice[0] for choice in UploadedFile.FILE_TYPES]
            
            if file_extension not in allowed_extensions:
                raise forms.ValidationError(
                    f"File type '{file_extension}' is not supported. "
                    f"Allowed types: {', '.join(allowed_extensions)}"
                )
        
        return file
    
    def save(self, commit=True):
        instance = super().save(commit=False)
        
        if self.cleaned_data.get('file'):
            file = self.cleaned_data['file']
            instance.original_filename = file.name
            instance.file_size = file.size
            instance.file_type = os.path.splitext(file.name)[1].lower().lstrip('.')
        
        if commit:
            instance.save()
        
        return instance


class GTFSGenerationForm(forms.Form):
    """Form for GTFS generation options"""
    
    dataset_name = forms.CharField(
        max_length=255,
        widget=forms.TextInput(attrs={'class': 'form-control'}),
        help_text="Name for the generated GTFS dataset"
    )
    
    description = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        help_text="Optional description for the GTFS dataset"
    )
    
    include_optional_files = forms.BooleanField(
        required=False,
        initial=True,
        help_text="Include optional GTFS files (fares, shapes, frequencies, etc.) if data is available"
    )
    
    validate_gtfs = forms.BooleanField(
        required=False,
        initial=True,
        help_text="Validate the generated GTFS data for compliance"
    )


class FileSearchForm(forms.Form):
    """Form for searching and filtering files"""
    
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Search by filename, type, or status...'
        })
    )
    
    status = forms.ChoiceField(
        required=False,
        choices=[('', 'All Statuses')] + UploadedFile.PROCESSING_STATUS,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    file_type = forms.ChoiceField(
        required=False,
        choices=[('', 'All Types')] + UploadedFile.FILE_TYPES,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'})
    )
    
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'})
    )
