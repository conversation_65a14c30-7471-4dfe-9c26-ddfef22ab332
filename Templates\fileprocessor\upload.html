{% extends 'fileprocessor/base.html' %}

{% block page_title %}Upload File{% endblock %}

{% block page_actions %}
    <a href="{% url 'fileprocessor:dashboard' %}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left"></i> Back to Dashboard
    </a>
{% endblock %}

{% block content %}
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-upload"></i> Upload File for Processing
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data" id="uploadForm">
                        {% csrf_token %}
                        
                        <!-- File Upload Area -->
                        <div class="file-upload-area mb-3" id="fileUploadArea">
                            <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                            <h5>Drag and drop your file here</h5>
                            <p class="text-muted">or click to browse</p>
                            {{ form.file }}
                        </div>
                        
                        <!-- File Info Display -->
                        <div id="fileInfo" class="alert alert-info" style="display: none;">
                            <h6><i class="fas fa-file"></i> Selected File:</h6>
                            <div id="fileName"></div>
                            <div id="fileSize"></div>
                            <div id="fileType"></div>
                        </div>
                        
                        <!-- Help Text -->
                        <div class="alert alert-light">
                            <h6><i class="fas fa-info-circle"></i> Supported File Types:</h6>
                            <ul class="mb-0">
                                <li><strong>Documents:</strong> PDF, Word (docx/doc), Text files</li>
                                <li><strong>Spreadsheets:</strong> CSV, Excel (xlsx/xls)</li>
                                <li><strong>Images:</strong> JPEG, PNG, TIFF (with OCR text extraction)</li>
                            </ul>
                            <hr>
                            <small class="text-muted">
                                <i class="fas fa-exclamation-triangle"></i>
                                Maximum file size: 50MB. Files will be processed to extract transit-related information and generate GTFS data.
                            </small>
                        </div>
                        
                        <!-- Form Errors -->
                        {% if form.errors %}
                            <div class="alert alert-danger">
                                {% for field, errors in form.errors.items %}
                                    {% for error in errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                {% endfor %}
                            </div>
                        {% endif %}
                        
                        <!-- Submit Button -->
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg" id="submitBtn" disabled>
                                <i class="fas fa-upload"></i> Upload and Process File
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Processing Information -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-cogs"></i> What happens after upload?
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-1 text-primary"></i> File Processing</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success"></i> Text extraction from documents</li>
                                <li><i class="fas fa-check text-success"></i> OCR for images</li>
                                <li><i class="fas fa-check text-success"></i> Data parsing from spreadsheets</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-2 text-primary"></i> AI Analysis</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success"></i> Transit data identification</li>
                                <li><i class="fas fa-check text-success"></i> Route and stop extraction</li>
                                <li><i class="fas fa-check text-success"></i> Schedule information parsing</li>
                            </ul>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <h6><i class="fas fa-3 text-primary"></i> GTFS Generation</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success"></i> Standard GTFS file creation</li>
                                <li><i class="fas fa-check text-success"></i> Data validation</li>
                                <li><i class="fas fa-check text-success"></i> ZIP package generation</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-4 text-primary"></i> Download & Use</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success"></i> Download GTFS files</li>
                                <li><i class="fas fa-check text-success"></i> Import to transit apps</li>
                                <li><i class="fas fa-check text-success"></i> Use in route planning</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const fileInput = document.getElementById('id_file');
        const fileUploadArea = document.getElementById('fileUploadArea');
        const fileInfo = document.getElementById('fileInfo');
        const submitBtn = document.getElementById('submitBtn');
        
        // File input change handler
        fileInput.addEventListener('change', function(e) {
            handleFileSelect(e.target.files[0]);
        });
        
        // Drag and drop handlers
        fileUploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            fileUploadArea.classList.add('dragover');
        });
        
        fileUploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            fileUploadArea.classList.remove('dragover');
        });
        
        fileUploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            fileUploadArea.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                handleFileSelect(files[0]);
            }
        });
        
        // Click to browse
        fileUploadArea.addEventListener('click', function() {
            fileInput.click();
        });
        
        function handleFileSelect(file) {
            if (file) {
                // Display file information
                document.getElementById('fileName').textContent = file.name;
                document.getElementById('fileSize').textContent = formatFileSize(file.size);
                document.getElementById('fileType').textContent = file.type || 'Unknown type';
                
                fileInfo.style.display = 'block';
                submitBtn.disabled = false;
                
                // Update upload area
                fileUploadArea.innerHTML = `
                    <i class="fas fa-file fa-3x text-success mb-3"></i>
                    <h5 class="text-success">File Selected</h5>
                    <p class="text-muted">${file.name}</p>
                `;
            }
        }
        
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        // Form submission handler
        document.getElementById('uploadForm').addEventListener('submit', function() {
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Uploading...';
            submitBtn.disabled = true;
        });
    });
</script>
{% endblock %}
