from django.db import models


# Create your models here.

class Cities(models.Model):
    id = models.IntegerField(primary_key=True)
    name = models.CharField(max_length=100, blank=True, null=True)
    code = models.CharField(max_length=10, blank=True, null=True)
    alias = models.CharField(max_length=500, blank=True, null=True)
    db_name = models.CharField(max_length=100, blank=True, null=True)
    is_bus = models.BooleanField(default=False)
    is_metro = models.BooleanField(default=False)
    is_rail = models.BooleanField(default=False)
    class Meta:
        permissions = (
            ("is_city_trivandrum", "Hide cities for this city"),
        )
        managed = False
        db_table = 'cities'
        verbose_name_plural = 'Cities'