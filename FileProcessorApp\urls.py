from django.urls import path
from . import views

app_name = 'fileprocessor'

urlpatterns = [
    # Dashboard and main views
    path('', views.file_upload_dashboard, name='dashboard'),
    path('upload/', views.upload_file, name='upload'),

    # File management
    path('file/<int:file_id>/', views.file_detail, name='file_detail'),
    path('file/<int:file_id>/reprocess/', views.reprocess_file, name='reprocess_file'),
    path('file/<int:file_id>/delete/', views.delete_file, name='delete_file'),
    path('file/<int:file_id>/status/', views.processing_status, name='processing_status'),

    # GTFS generation and management
    path('file/<int:file_id>/generate-gtfs/', views.generate_gtfs, name='generate_gtfs'),
    path('gtfs/', views.gtfs_datasets, name='gtfs_datasets'),
    path('gtfs/<int:dataset_id>/', views.gtfs_detail, name='gtfs_detail'),
    path('gtfs/<int:dataset_id>/download/', views.download_gtfs, name='download_gtfs'),
]
