from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse, HttpResponse, Http404
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.core.paginator import Paginator
from django.db.models import Q
from django.conf import settings
import os
import json
import mimetypes
from .models import UploadedFile, GTFSDataset, ExtractedTransitData
from .forms import FileUploadForm
from .service.file_processor import FileProcessorService
from .service.gtfs_generator import GTFSGeneratorService


@login_required
def file_upload_dashboard(request):
    """Main dashboard for file uploads and processing"""
    # Get user's uploaded files
    files = UploadedFile.objects.filter(uploaded_by=request.user)

    # Apply search filter if provided
    search_query = request.GET.get('search', '')
    if search_query:
        files = files.filter(
            Q(original_filename__icontains=search_query) |
            Q(file_type__icontains=search_query) |
            Q(processing_status__icontains=search_query)
        )

    # Apply status filter if provided
    status_filter = request.GET.get('status', '')
    if status_filter:
        files = files.filter(processing_status=status_filter)

    # Pagination
    paginator = Paginator(files, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Get statistics
    stats = {
        'total_files': UploadedFile.objects.filter(uploaded_by=request.user).count(),
        'pending_files': UploadedFile.objects.filter(uploaded_by=request.user, processing_status='pending').count(),
        'processing_files': UploadedFile.objects.filter(uploaded_by=request.user, processing_status='processing').count(),
        'completed_files': UploadedFile.objects.filter(uploaded_by=request.user, processing_status='completed').count(),
        'failed_files': UploadedFile.objects.filter(uploaded_by=request.user, processing_status='failed').count(),
        'gtfs_datasets': GTFSDataset.objects.filter(source_file__uploaded_by=request.user).count(),
    }

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'status_filter': status_filter,
        'stats': stats,
        'status_choices': UploadedFile.PROCESSING_STATUS,
    }

    return render(request, 'fileprocessor/dashboard.html', context)


@login_required
def upload_file(request):
    """Handle file upload"""
    if request.method == 'POST':
        form = FileUploadForm(request.POST, request.FILES)
        if form.is_valid():
            uploaded_file = form.save(commit=False)
            uploaded_file.uploaded_by = request.user

            # Get file information
            file_obj = request.FILES['file']
            uploaded_file.original_filename = file_obj.name
            uploaded_file.file_size = file_obj.size
            uploaded_file.file_type = uploaded_file.file_extension

            uploaded_file.save()

            # Start processing asynchronously (if celery is available)
            try:
                from .tasks import process_uploaded_file
                process_uploaded_file.delay(uploaded_file.id)
                messages.success(request, f'File "{file_obj.name}" uploaded successfully and processing started.')
            except ImportError:
                # Fallback to synchronous processing
                processor = FileProcessorService()
                processor.process_file(uploaded_file)
                messages.success(request, f'File "{file_obj.name}" uploaded and processed successfully.')

            return redirect('fileprocessor:dashboard')
        else:
            messages.error(request, 'Please correct the errors below.')
    else:
        form = FileUploadForm()

    return render(request, 'fileprocessor/upload.html', {'form': form})


@login_required
def file_detail(request, file_id):
    """View details of an uploaded file"""
    uploaded_file = get_object_or_404(UploadedFile, id=file_id, uploaded_by=request.user)

    # Get related GTFS datasets
    gtfs_datasets = GTFSDataset.objects.filter(source_file=uploaded_file)

    # Get extracted data
    extracted_data = ExtractedTransitData.objects.filter(source_file=uploaded_file)

    context = {
        'uploaded_file': uploaded_file,
        'gtfs_datasets': gtfs_datasets,
        'extracted_data': extracted_data,
    }

    return render(request, 'fileprocessor/file_detail.html', context)


@login_required
def reprocess_file(request, file_id):
    """Reprocess a file"""
    uploaded_file = get_object_or_404(UploadedFile, id=file_id, uploaded_by=request.user)

    if request.method == 'POST':
        # Reset processing status
        uploaded_file.processing_status = 'pending'
        uploaded_file.processing_started_at = None
        uploaded_file.processing_completed_at = None
        uploaded_file.processing_error = None
        uploaded_file.save()

        # Start processing
        try:
            from .tasks import process_uploaded_file
            process_uploaded_file.delay(uploaded_file.id)
            messages.success(request, 'File reprocessing started.')
        except ImportError:
            processor = FileProcessorService()
            processor.process_file(uploaded_file)
            messages.success(request, 'File reprocessed successfully.')

        return redirect('fileprocessor:file_detail', file_id=file_id)

    return render(request, 'fileprocessor/confirm_reprocess.html', {'uploaded_file': uploaded_file})


@login_required
def generate_gtfs(request, file_id):
    """Generate GTFS data from processed file"""
    uploaded_file = get_object_or_404(UploadedFile, id=file_id, uploaded_by=request.user)

    if uploaded_file.processing_status != 'completed':
        messages.error(request, 'File must be processed successfully before generating GTFS data.')
        return redirect('fileprocessor:file_detail', file_id=file_id)

    if request.method == 'POST':
        try:
            gtfs_generator = GTFSGeneratorService()
            gtfs_dataset = gtfs_generator.generate_gtfs_from_file(uploaded_file)

            messages.success(request, f'GTFS dataset "{gtfs_dataset.dataset_name}" generated successfully.')
            return redirect('fileprocessor:gtfs_detail', dataset_id=gtfs_dataset.id)
        except Exception as e:
            messages.error(request, f'Error generating GTFS data: {str(e)}')
            return redirect('fileprocessor:file_detail', file_id=file_id)

    return render(request, 'fileprocessor/confirm_gtfs_generation.html', {'uploaded_file': uploaded_file})


@login_required
def gtfs_datasets(request):
    """List all GTFS datasets for the user"""
    datasets = GTFSDataset.objects.filter(source_file__uploaded_by=request.user)

    # Apply search filter
    search_query = request.GET.get('search', '')
    if search_query:
        datasets = datasets.filter(
            Q(dataset_name__icontains=search_query) |
            Q(description__icontains=search_query)
        )

    # Pagination
    paginator = Paginator(datasets, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
    }

    return render(request, 'fileprocessor/gtfs_datasets.html', context)


@login_required
def gtfs_detail(request, dataset_id):
    """View details of a GTFS dataset"""
    dataset = get_object_or_404(GTFSDataset, id=dataset_id, source_file__uploaded_by=request.user)

    context = {
        'dataset': dataset,
    }

    return render(request, 'fileprocessor/gtfs_detail.html', context)


@login_required
def download_gtfs(request, dataset_id):
    """Download GTFS dataset as ZIP file"""
    dataset = get_object_or_404(GTFSDataset, id=dataset_id, source_file__uploaded_by=request.user)

    if not dataset.gtfs_zip_file:
        raise Http404("GTFS ZIP file not found")

    file_path = dataset.gtfs_zip_file.path
    if not os.path.exists(file_path):
        raise Http404("GTFS ZIP file not found on disk")

    with open(file_path, 'rb') as f:
        response = HttpResponse(f.read(), content_type='application/zip')
        response['Content-Disposition'] = f'attachment; filename="{dataset.dataset_name}.zip"'
        return response


@login_required
@require_http_methods(["GET"])
def processing_status(request, file_id):
    """AJAX endpoint to get file processing status"""
    try:
        uploaded_file = get_object_or_404(UploadedFile, id=file_id, uploaded_by=request.user)

        data = {
            'status': uploaded_file.processing_status,
            'status_display': uploaded_file.get_processing_status_display(),
            'progress': 100 if uploaded_file.processing_status == 'completed' else 0,
            'error': uploaded_file.processing_error,
            'gtfs_generated': uploaded_file.gtfs_generated,
        }

        if uploaded_file.processing_status == 'completed':
            data['extracted_text_length'] = len(uploaded_file.extracted_text or '')
            data['ai_analysis_length'] = len(uploaded_file.ai_analysis or '')

        return JsonResponse(data)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@login_required
def delete_file(request, file_id):
    """Delete an uploaded file"""
    uploaded_file = get_object_or_404(UploadedFile, id=file_id, uploaded_by=request.user)

    if request.method == 'POST':
        filename = uploaded_file.original_filename

        # Delete associated GTFS datasets
        GTFSDataset.objects.filter(source_file=uploaded_file).delete()

        # Delete the file
        uploaded_file.delete()

        messages.success(request, f'File "{filename}" and associated data deleted successfully.')
        return redirect('fileprocessor:dashboard')

    return render(request, 'fileprocessor/confirm_delete.html', {'uploaded_file': uploaded_file})
