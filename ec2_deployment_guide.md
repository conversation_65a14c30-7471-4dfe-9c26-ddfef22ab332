# 🚀 EC2 Deployment Guide for FileProcessorApp

This guide covers deploying the FileProcessorApp on AWS EC2 with CPU-optimized AI processing.

## 🖥️ EC2 Instance Recommendations

### Minimum Requirements
- **Instance Type**: t3.medium (2 vCPUs, 4GB RAM)
- **Storage**: 20GB EBS (for OS + models)
- **OS**: Ubuntu 20.04 LTS or Amazon Linux 2

### Recommended for Production
- **Instance Type**: c5.large or c5.xlarge (CPU-optimized)
- **Storage**: 30GB EBS SSD
- **Memory**: 8GB+ RAM for better AI model performance

### High Performance
- **Instance Type**: c5.2xlarge (8 vCPUs, 16GB RAM)
- **Storage**: 50GB EBS SSD
- **Network**: Enhanced networking enabled

## 🔧 CPU Optimization Features

The FileProcessorApp has been optimized for CPU-only processing on EC2:

### Automatic CPU Detection
- Detects available CPU cores
- Uses `cpu_count - 1` threads (leaves 1 core for system)
- Automatically configures Ollama for multi-threading

### Memory-Adaptive Configuration
- Adjusts context window based on available RAM
- Scales model parameters for optimal performance
- Prevents out-of-memory errors

### Model Prioritization
Models are prioritized for CPU efficiency:
1. **phi** (1.6GB) - Fastest, good for basic analysis
2. **llama2** (3.8GB) - Balanced performance
3. **mistral** (4.1GB) - Best accuracy, slower

## 📦 Installation on EC2

### 1. System Setup
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Python and dependencies
sudo apt install python3 python3-pip python3-venv git -y

# Install system dependencies for file processing
sudo apt install tesseract-ocr libtesseract-dev -y
```

### 2. Clone and Setup Project
```bash
# Clone repository
git clone <your-repo-url>
cd transitcms-python
git checkout feature/fileprocessor

# Create virtual environment
python3 -m venv venv
source venv/bin/activate

# Install Python dependencies
pip install -r requirements.txt
```

### 3. Install Ollama for CPU
```bash
# Download and install Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# Start Ollama service
ollama serve &

# Download CPU-optimized model
ollama pull phi  # Lightweight model for EC2
```

### 4. Configure Django
```bash
# Set up database (use PostgreSQL RDS for production)
python manage.py makemigrations FileProcessorApp
python manage.py migrate

# Create superuser
python manage.py createsuperuser

# Collect static files
python manage.py collectstatic --noinput
```

## ⚙️ CPU Configuration Details

### Automatic Thread Optimization
```python
# The AI analyzer automatically configures:
cpu_count = multiprocessing.cpu_count()
num_threads = max(1, cpu_count - 1)  # Leave 1 core for system

config = {
    "num_thread": num_threads,  # Multi-threading
    "num_gpu": 0,              # Force CPU-only
    "num_ctx": 2048,           # Context window
    "temperature": 0.1,        # Consistent results
    "num_predict": 512,        # Response length
}
```

### Memory-Based Scaling
```python
# Automatically adjusts based on available RAM:
if available_memory_gb < 4:
    config["num_ctx"] = 1024    # Smaller context
elif available_memory_gb < 8:
    config["num_ctx"] = 2048    # Medium context
else:
    config["num_ctx"] = 4096    # Larger context
```

## 🚀 Performance Optimization

### EC2 Instance Tuning
```bash
# Optimize for CPU performance
echo 'performance' | sudo tee /sys/devices/system/cpu/cpu*/cpufreq/scaling_governor

# Increase file limits
echo '* soft nofile 65536' | sudo tee -a /etc/security/limits.conf
echo '* hard nofile 65536' | sudo tee -a /etc/security/limits.conf
```

### Ollama CPU Configuration
```bash
# Set environment variables for CPU optimization
export OLLAMA_NUM_PARALLEL=1
export OLLAMA_MAX_LOADED_MODELS=1
export OLLAMA_FLASH_ATTENTION=false
export OLLAMA_HOST=127.0.0.1:11434
```

### Django Production Settings
```python
# In production settings
DEBUG = False
ALLOWED_HOSTS = ['your-ec2-domain.com', 'your-elastic-ip']

# Use production database
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': 'transitcms',
        'USER': 'your-user',
        'PASSWORD': 'your-password',
        'HOST': 'your-rds-endpoint',
        'PORT': '5432',
    }
}

# File processing limits for EC2
FILEPROCESSOR_MAX_FILE_SIZE = 25 * 1024 * 1024  # 25MB for EC2
```

## 📊 Performance Benchmarks

### Expected Processing Times (c5.large)

| File Type | Size | Processing Time | AI Analysis |
|-----------|------|----------------|-------------|
| PDF | 1MB | 5-10 seconds | 15-30 seconds |
| CSV | 500KB | 2-5 seconds | 10-20 seconds |
| Image | 2MB | 10-15 seconds | 20-40 seconds |
| Word Doc | 1MB | 3-8 seconds | 12-25 seconds |

### Model Performance Comparison

| Model | RAM Usage | CPU Usage | Speed | Accuracy |
|-------|-----------|-----------|-------|----------|
| phi | 2-3GB | 60-80% | Fast | Good |
| llama2 | 4-6GB | 70-90% | Medium | Better |
| mistral | 5-8GB | 80-95% | Slow | Best |

## 🔍 Monitoring and Logging

### System Monitoring
```bash
# Monitor CPU usage
htop

# Monitor memory usage
free -h

# Monitor Ollama process
ps aux | grep ollama

# Check Ollama logs
journalctl -u ollama -f
```

### Application Monitoring
```python
# Add to Django settings for production
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': '/var/log/fileprocessor.log',
        },
    },
    'loggers': {
        'FileProcessorApp': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}
```

## 🛡️ Security Considerations

### EC2 Security Group
```bash
# Allow only necessary ports
Port 22 (SSH) - Your IP only
Port 80 (HTTP) - 0.0.0.0/0
Port 443 (HTTPS) - 0.0.0.0/0
Port 8000 (Django dev) - Your IP only (development)
```

### Ollama Security
```bash
# Ollama runs on localhost only by default
# No external access to AI models
# All processing happens locally on EC2
```

## 🔄 Auto-Scaling Considerations

### Horizontal Scaling
- Use Application Load Balancer
- Multiple EC2 instances with shared RDS
- Shared file storage (EFS or S3)

### Vertical Scaling
- Start with t3.medium
- Scale up to c5.large/xlarge based on usage
- Monitor CPU and memory utilization

## 📈 Cost Optimization

### Instance Selection
- **Development**: t3.micro (free tier eligible)
- **Testing**: t3.small ($15-20/month)
- **Production**: c5.large ($70-80/month)
- **High Load**: c5.xlarge ($140-160/month)

### Storage Optimization
- Use gp3 EBS for better price/performance
- Implement file cleanup policies
- Consider S3 for long-term file storage

## 🚀 Deployment Script

```bash
#!/bin/bash
# EC2 deployment script

# Install system dependencies
sudo apt update && sudo apt upgrade -y
sudo apt install python3 python3-pip python3-venv git tesseract-ocr -y

# Install Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# Setup application
git clone <your-repo>
cd transitcms-python
git checkout feature/fileprocessor

python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# Start services
ollama serve &
sleep 10
ollama pull phi

# Configure Django
python manage.py migrate
python manage.py collectstatic --noinput

# Start application
gunicorn TransitCMS.wsgi:application --bind 0.0.0.0:8000
```

## ✅ Deployment Checklist

- [ ] EC2 instance launched with appropriate size
- [ ] Security groups configured
- [ ] System dependencies installed
- [ ] Ollama installed and running
- [ ] AI model downloaded (phi recommended)
- [ ] Django application configured
- [ ] Database migrations applied
- [ ] Static files collected
- [ ] Application server running (gunicorn)
- [ ] Load balancer configured (if needed)
- [ ] Monitoring setup
- [ ] Backup strategy implemented

---

## 🎯 Ready for Production!

Your FileProcessorApp is now optimized for CPU-only processing on EC2, with automatic thread management and memory-adaptive configuration. The system will efficiently use all available CPU cores while maintaining stability and performance.

**Happy EC2 deployment! ☁️🚀**
