# Generated by Django 4.0.6 on 2022-07-20 07:40

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('MetroApp', '0001_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='metrofare',
            options={'managed': False, 'permissions': (('can_add_metro_fare', 'Can add metro fare'), ('can_change_metro_fare', 'Can change metro fare'), ('can_delete_metro_fare', 'Can delete metro fare'), ('can_view_metro_fare', 'Can view metro fare'))},
        ),
        migrations.AlterModelOptions(
            name='metroroute',
            options={'managed': False, 'permissions': (('can_add_metro_route', 'Can add metro route'), ('can_change_metro_route', 'Can change metro route'), ('can_delete_metro_route', 'Can delete metro route'), ('can_view_metro_route', 'Can view metro route'))},
        ),
        migrations.AlterModelOptions(
            name='station',
            options={'managed': False, 'permissions': (('can_add_station', 'Can add station'), ('can_change_station', 'Can change station'), ('can_delete_station', 'Can delete station'), ('can_view_station', 'Can view station'))},
        ),
        migrations.AlterModelOptions(
            name='times',
            options={'managed': False, 'permissions': (('can_add_times', 'Can add times'), ('can_change_times', 'Can change times'), ('can_delete_times', 'Can delete times'), ('can_view_times', 'Can view times'))},
        ),
    ]
