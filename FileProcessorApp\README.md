# File Processor & GTFS Generator

A Django application that accepts various file types (PDF, CSV, images, etc.), extracts transit-related data using AI analysis, and generates GTFS (General Transit Feed Specification) format files.

## Features

### File Processing
- **Multiple File Types**: Supports PDF, CSV, Excel, Word documents, images, and text files
- **Text Extraction**: Extracts text from documents using appropriate libraries
- **OCR Support**: Optical Character Recognition for images using Tesseract
- **Data Parsing**: Intelligent parsing of structured data from spreadsheets

### AI-Powered Analysis
- **Local AI Integration**: Uses Ollama for local AI processing (privacy-focused)
- **Transit Data Identification**: Automatically identifies routes, stops, schedules, fares, and agencies
- **Confidence Scoring**: Provides confidence levels for extracted data
- **Fallback Analysis**: Rule-based analysis when AI is not available

### GTFS Generation
- **Standard Compliance**: Generates GTFS files according to official specification
- **Required Files**: Creates all mandatory GTFS files (agency.txt, routes.txt, etc.)
- **Optional Files**: Supports optional GTFS files when data is available
- **Validation**: Built-in GTFS data validation
- **ZIP Packaging**: Packages GTFS files into downloadable ZIP archives

### User Interface
- **Dashboard**: Overview of uploaded files and processing status
- **File Upload**: Drag-and-drop file upload interface
- **Progress Tracking**: Real-time processing status updates
- **GTFS Management**: View and download generated GTFS datasets
- **Admin Interface**: Comprehensive admin panel for file management

## Installation

### Prerequisites
- Python 3.8+
- Django 4.2+
- PostgreSQL (for the main application)

### Required Python Packages
```bash
pip install -r requirements.txt
```

Key dependencies:
- `PyPDF2` - PDF text extraction
- `pdfplumber` - Advanced PDF processing
- `python-docx` - Word document processing
- `Pillow` - Image processing
- `pytesseract` - OCR functionality
- `pandas` - Data manipulation
- `ollama` - Local AI model integration

### Optional: AI Setup
For AI-powered analysis, install Ollama:

1. Download from https://ollama.ai/
2. Install a language model:
   ```bash
   ollama pull llama2
   ```
3. Start the service:
   ```bash
   ollama serve
   ```

See `FileProcessorApp/service/ollama_setup.md` for detailed setup instructions.

## Configuration

### Django Settings
Add to `INSTALLED_APPS`:
```python
INSTALLED_APPS = [
    # ... other apps
    'FileProcessorApp',
]
```

### File Storage Settings
```python
# Media files
MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

# File upload limits
FILE_UPLOAD_MAX_MEMORY_SIZE = 50 * 1024 * 1024  # 50MB
DATA_UPLOAD_MAX_MEMORY_SIZE = 50 * 1024 * 1024  # 50MB

# FileProcessorApp specific
FILEPROCESSOR_UPLOAD_PATH = 'uploads/'
FILEPROCESSOR_GTFS_PATH = 'gtfs_datasets/'
FILEPROCESSOR_MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB
```

### URL Configuration
```python
# In main urls.py
urlpatterns = [
    # ... other patterns
    path('fileprocessor/', include('FileProcessorApp.urls')),
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
```

## Usage

### 1. Upload Files
- Navigate to `/fileprocessor/upload/`
- Drag and drop files or click to browse
- Supported formats: PDF, CSV, Excel, Word, Images, Text

### 2. Processing
Files are automatically processed through these stages:
1. **Text Extraction**: Extract readable text from the file
2. **AI Analysis**: Identify transit-related information
3. **Data Structuring**: Organize extracted data by type
4. **Status Updates**: Track progress in real-time

### 3. GTFS Generation
- Once processing is complete, generate GTFS data
- Review extracted information
- Download GTFS ZIP file
- Use in transit applications

### 4. Management
- View all files in the dashboard
- Monitor processing status
- Reprocess failed files
- Manage GTFS datasets

## API Endpoints

### File Management
- `GET /fileprocessor/` - Dashboard
- `POST /fileprocessor/upload/` - Upload file
- `GET /fileprocessor/file/<id>/` - File details
- `POST /fileprocessor/file/<id>/reprocess/` - Reprocess file
- `DELETE /fileprocessor/file/<id>/delete/` - Delete file

### GTFS Operations
- `POST /fileprocessor/file/<id>/generate-gtfs/` - Generate GTFS
- `GET /fileprocessor/gtfs/` - List GTFS datasets
- `GET /fileprocessor/gtfs/<id>/` - GTFS dataset details
- `GET /fileprocessor/gtfs/<id>/download/` - Download GTFS ZIP

### Status Monitoring
- `GET /fileprocessor/file/<id>/status/` - Processing status (AJAX)

## Models

### UploadedFile
Stores uploaded file metadata and processing status.

### GTFSDataset
Represents generated GTFS datasets with validation information.

### ExtractedTransitData
Stores structured transit data extracted from files.

## Services

### FileProcessorService
Handles file processing and text extraction for different file types.

### AIAnalyzer
Provides AI-powered analysis of extracted text to identify transit data.

### GTFSGeneratorService
Converts extracted data into GTFS format files.

### FileManagerService
Manages file storage, cleanup, and statistics.

## Admin Interface

Comprehensive admin interface for:
- File management and monitoring
- Processing status tracking
- GTFS dataset management
- Bulk operations (reprocess, generate GTFS, cleanup)
- Detailed data viewing

## Troubleshooting

### Common Issues

1. **File Upload Fails**
   - Check file size limits
   - Verify supported file types
   - Ensure media directory permissions

2. **Processing Errors**
   - Check required libraries are installed
   - Verify file is not corrupted
   - Review processing logs

3. **AI Analysis Not Working**
   - Ensure Ollama is installed and running
   - Check model availability
   - Falls back to rule-based analysis

4. **GTFS Generation Issues**
   - Verify extracted data quality
   - Check GTFS validation reports
   - Review generated file structure

### Logs
Check Django logs for detailed error information:
```python
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': 'fileprocessor.log',
        },
    },
    'loggers': {
        'FileProcessorApp': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make changes with tests
4. Submit a pull request

## License

This project is part of the Transit CMS system. See main project license for details.
