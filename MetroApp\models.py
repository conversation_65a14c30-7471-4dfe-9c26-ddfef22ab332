from django.db import models
from django.db import transaction,IntegrityError


class MetroRoute(models.Model):
    metro_route_id = models.BigAutoField(primary_key=True)
    route_name = models.CharField(max_length=100, blank=True, null=True)
    route_color_code = models.CharField(max_length=150, blank=True, null=True)
    metro_operator = models.CharField(max_length=100, blank=True, null=True)
    op_logo_url = models.URLField()

    class Meta:
        managed = False
        db_table = 'metro_route'
        verbose_name_plural = 'Metro Route'
        # permissions = (
        #     ('can_add_metro_route', 'Can add metro route'),
        #     ('can_change_metro_route', 'Can change metro route'),
        #     ('can_delete_metro_route', 'Can delete metro route'),
        #     ('can_view_metro_route', 'Can view metro route'),
        # )
    def save(self, *args, **kwargs):
        if not self.metro_route_id:
            last_instance = MetroRoute.objects.last()
            self.metro_route_id = last_instance.metro_route_id + 1 if last_instance else 1
        super().save(*args, **kwargs)

    def __str__(self):
        return self.route_name

class Station(models.Model):
    station_id = models.BigAutoField(primary_key=True)
    station_name = models.CharField(max_length=150, blank=True, null=True)
    latitude = models.FloatField(blank=True, null=True)
    longitude = models.FloatField(blank=True, null=True)
    seq = models.IntegerField(blank=True, null=True)
    metro_route = models.ForeignKey(MetroRoute, related_name='routeId', on_delete=models.CASCADE, db_column='metro_route_id')
    # metro_route_id = models.IntegerField(blank=True, null=True)
    is_cross = models.TextField(max_length=1,choices=(('0','0'),('1','1')),default='0')  # This field type is a guess.
    cross_alias = models.BigIntegerField(default=0)

    class Meta:
        managed = False
        db_table = 'station'
        verbose_name_plural = 'Station'
        unique_together = ['metro_route', 'seq'] 
        # permissions = (
        #     ('can_add_station', 'Can add station'),
        #     ('can_change_station', 'Can change station'),
        #     ('can_delete_station', 'Can delete station'),
        #     ('can_view_station', 'Can view station'),
        # )

    def save(self, *args, **kwargs):
        # Check how many times the value of metro_route appears in other instances
        if not self.station_id:
            last_instance = Station.objects.last()
            self.station_id = last_instance.station_id + 1 if last_instance else 1

        if self.metro_route_id:
            count = Station.objects.filter(metro_route_id=self.metro_route_id).exclude(pk=self.pk).count()
            count = count+1
            self.seq = count
        super().save(*args, **kwargs)

    def __str__(self):
        return self.station_name

class Times(models.Model):
    times_id = models.BigAutoField(primary_key=True)
    diparture_time = models.TimeField(blank=True, null=True)
    slot_identity = models.BigIntegerField(blank=True, null=True)
    direction = models.CharField(max_length=5, blank=True, null=True, choices=(('Down','Down'),('Up','Up')))
    day_type = models.IntegerField(blank=True, null=True)
    station_id = models.IntegerField(blank=True, null=True)
    # station_id = models.ForeignKey(Station, related_name='stationId1', on_delete=models.CASCADE, blank=True, null=True, db_column='station_id')

    class Meta:
        managed = False
        db_table = 'times'
        verbose_name_plural = 'Time'
        unique_together = ['station_id', 'slot_identity'] 
        # permissions = (
        #     ('can_add_times', 'Can add times'),
        #     ('can_change_times', 'Can change times'),
        #     ('can_delete_times', 'Can delete times'),
        #     ('can_view_times', 'Can view times'),
        # )

    # def save(self, *args, **kwargs):
    #     if not self.times_id:
    #         last_instance = Times.objects.last()
    #         self.times_id = last_instance.times_id + 1 if last_instance else 1
    #     super().save(*args, **kwargs)

    def save(self, *args, **kwargs):
        with transaction.atomic():  # Ensure consistency
            if not self.times_id:
                last_instance = Times.objects.last()
                self.times_id = last_instance.times_id + 1 if last_instance else 1

            if not self.slot_identity and self.times_id:
                existing_time = Times.objects.filter(
                    times_id=self.times_id
                ).order_by('slot_identity', 'day_type')

                if existing_time.exists():
                    existing_time.filter(slot_identity__gte=self.slot_identity).update(slot_identity=F('slot_identity') + 1)
                    self.slot_identity = self.slot_identity  
                else:
                    self.slot_identity = 1 

            try:
                super(Times, self).save(*args, **kwargs)
            except IntegrityError:
                print("Error: slot_identity already exists for the Time. Please choose a different slot_identity  or update an existing record.")


class MetroFare(models.Model):
    metro_fare_id = models.BigAutoField(primary_key=True)
    amount = models.FloatField(blank=True, null=True)
    station_id_from = models.ForeignKey(Station, related_name='startStationId', on_delete=models.CASCADE, blank=True, null=True, db_column='station_id_from')
    station_id_to = models.ForeignKey(Station, related_name='endStationId', on_delete=models.CASCADE, blank=True, null=True, db_column='station_id_to')

    class Meta:
        managed = False
        db_table = 'metro_fare'
        verbose_name_plural = 'Metro Fare'
        # permissions = (
        #     ('can_add_metro_fare', 'Can add metro fare'),
        #     ('can_change_metro_fare', 'Can change metro fare'),
        #     ('can_delete_metro_fare', 'Can delete metro fare'),
        #     ('can_view_metro_fare', 'Can view metro fare'),
        # )

    def save(self, *args, **kwargs):
        if not self.metro_fare_id:
            last_instance = MetroFare.objects.last()
            self.metro_fare_id = last_instance.metro_fare_id + 1 if last_instance else 1
        super().save(*args, **kwargs)