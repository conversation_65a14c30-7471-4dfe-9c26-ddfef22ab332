from email.policy import default
from django import forms
from django.db import models
from django.db import transaction


# Create your models here.

class BusServiceType(models.Model):
    service_type_id = models.AutoField(primary_key=True)
    service_type_name = models.CharField(max_length=50, blank=True, null=True)
    abbreviation = models.CharField(max_length=50, blank=True, null=True)
    status = models.CharField(max_length=50, blank=True, null=True,default= 'ACTIVE',choices=(('ACTIVE','ACTIVE'),('INACTIVE','INACTIVE')))
    deleted_status = models.IntegerField(default=0, blank=True, null=True)
    updated_by = models.IntegerField()
    updated_date = models.CharField(max_length=50, blank=True)
    service_type_code = models.CharField(max_length=10, blank=True, null=True)
    sync_updated_date = models.Char<PERSON>ield(max_length=50, blank=True)

    

    class Meta:
        managed = False
        db_table = 'bus_service_type'
        verbose_name_plural = 'Bus Service Type'
        # permissions = (
        #     ('can_add_bus_service_type', 'Can add bus service type'),
        #     ('can_change_bus_service_type', 'Can change bus service type'),
        #     ('can_delete_bus_service_type', 'Can delete bus service type'),
        #     ('can_view_bus_service_type', 'Can view bus service type'),
        # )
        # default_permissions = ('change', 'add', 'delete', 'view')

    def save(self, *args, **kwargs):
        if not self.service_type_id:
            last_instance = BusServiceType.objects.last()
            self.service_type_id = last_instance.service_type_id + 1 if last_instance else 1
        super().save(*args, **kwargs)

    def __str__(self):
        return self.service_type_name


class BusStop(models.Model):
    bus_stop_id = models.AutoField(primary_key=True, db_column='bus_stop_id')
    bus_stop_name = models.CharField(max_length=100, blank=True, null=True)
    bus_stop_code = models.CharField(max_length=100, blank=True, null=True)
    status = models.CharField(max_length=100, blank=True, null=True, default='New', choices=(('Approved','Approved'),('New','New')))
    landmark = models.CharField(max_length=45, blank=True, null=True)
    latitude_current = models.DecimalField(max_digits=12, decimal_places=8, blank=True, null=True)
    longitude_current = models.DecimalField(max_digits=12, decimal_places=8, blank=True, null=True)
    fare_stage = models.CharField(max_length=5, blank=True, null=True, default='Y', choices=(('Y','Y'),('N','N')))
    sub_stage = models.CharField(max_length=5,default='N', choices=(('Y','Y'),('N','N')))
    description = models.CharField(max_length=100, blank=True, null=True)
    bmtc_status = models.CharField(max_length=10, blank=True, null=True, default='Y', choices=(('Y','Y'),('N','N')))
    route_status = models.CharField(max_length=1, default='Y', choices=(('Y','Y'),('N','N')))
    stop_type_id = models.IntegerField(blank=True, null=True)
    stop_group_id = models.BigIntegerField(blank=True, null=True)
    toll_zone = models.CharField(max_length=1, blank=True, null=True, default='N', choices=(('Y','Y'),('N','N')))
    # toll_fee = models.BigIntegerField(null=True, default='NULL')
    # stop_direction = models.CharField(max_length=100,blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'bus_stop'
        verbose_name_plural = 'Bus Stop'
        # permissions = (
        #     ('can_add_bus_stop', 'Can add bus stop'),
        #     ('can_change_bus_stop', 'Can change bus stop'),
        #     ('can_delete_bus_stop', 'Can delete bus stop'),
        #     ('can_view_bus_stop', 'Can view bus stop'),
        # )

    # def save(self, *args, **kwargs):
    #     if not self.bus_stop_id or self.bus_stop_id is None:
    #         last_bus_stop_id = BusStop.objects.get(bus_stop_id=bus_stop_id).last()
    #         max_bus_stop_id = last_bus_stop_id.bus_stop_id if last_bus_stop_id else 0
    #         print("max",max_bus_stop_id)
    #         self.bus_stop_id = max_bus_stop_id + 1

    #     super().save(*args, **kwargs)

    def save(self, *args, **kwargs):
        if not self.bus_stop_id:
            last_instance = BusStop.objects.last()
            self.bus_stop_id = last_instance.bus_stop_id + 1 if last_instance else 1
        super().save(*args, **kwargs) 

    def __str__(self):
        return self.bus_stop_name




    


class BusRoute(models.Model):
    route_id = models.AutoField(primary_key=True)
    route_number = models.CharField(max_length=250, blank=True, null=True)
    start_point_id = models.ForeignKey(BusStop, related_name='startPoint', on_delete=models.CASCADE, blank=True,null=True, db_column='start_point_id')
    # start_point_id = models.IntegerField()
    end_point_id = models.ForeignKey(BusStop, related_name='endPoint', on_delete=models.CASCADE, blank=True, null=True,db_column='end_point_id')
    # end_point_id = models.IntegerField()
    route_type_id = models.IntegerField(blank=True, null=True)
    route_name = models.CharField(max_length=250, blank=True, null=True)
    status = models.CharField(max_length=250, blank=True, null=True, default='Active' ,choices=(('Active', 'Active'),('Inactive', 'Inactive')))
    via = models.CharField(max_length=250, blank=True, null=True)
    description = models.CharField(max_length=250, blank=True, null=True)
    deleted_status = models.IntegerField(default=0, blank=True, null=True)
    route_direction = models.CharField(max_length=50, blank=True, null=True, choices=(('UP','UP'),('RING','RING'),('DOWN','DOWN')))
    # effective_from = models.DateTimeField(max_length=50, blank=True, null=True)
    effective_from = models.CharField(max_length=50, blank=True, null=True)
    # effective_till = models.DateTimeField(max_length=50, blank=True, null=True)
    effective_till = models.CharField(max_length=50, blank=True, null=True)
    route_string = models.TextField(default=0,blank=True, null=True)
    bus_service_type_id = models.ForeignKey(BusServiceType, related_name='serviceTypeId', on_delete=models.CASCADE,blank=True, null=True, db_column='bus_service_type_id')
    # bus_service_type_id = models.IntegerField(max_length=50, blank=True, null=True)
    route_group = models.CharField(max_length=50)
    no_of_schedule = models.BigIntegerField()
    route_alias = models.CharField(max_length=250,null=True)

    class Meta:
        managed = False
        db_table = 'bus_route'
        verbose_name_plural = 'Bus Route'
        # permissions = (
        #     ('can_add_bus_route', 'Can add bus route'),
        #     ('can_change_bus_route', 'Can change bus route'),
        #     ('can_delete_bus_route', 'Can delete bus route'),
        #     ('can_view_bus_route', 'Can view bus route'),
        # )

    def save(self, *args, **kwargs):
        if not self.route_id:
            last_instance = BusRoute.objects.last()
            self.route_id = last_instance.route_id + 1 if last_instance else 1
        super().save(*args, **kwargs) 

    def __str__(self):
        return str(self.route_id)

    


class BusRoutePoint(models.Model):
    route_points_id = models.AutoField(primary_key=True, db_column='route_points_id')
    route_id = models.ForeignKey(BusRoute, related_name='routeId', on_delete=models.CASCADE, blank=True, null=True,db_column='route_id')
    bus_stop_id = models.ForeignKey(BusStop, related_name='busStopId', on_delete=models.CASCADE, blank=True, null=True,db_column='bus_stop_id')
    # bus_stop_id = models.IntegerField(blank=True, null=True)
    route_order = models.IntegerField(blank=True, null=True)
    point_status = models.CharField(max_length=20, blank=True, null=True, choices=(('ACTIVE', 'ACTIVE'),('INACTIVE', 'INACTIVE')))
    fare_stage = models.CharField(max_length=5, blank=True, null=True, default='Y',choices=(('Y','Y'),('N','N')))
    sub_stage = models.CharField(max_length=5, blank=True, null=True,default='N',choices=(('Y','Y'),('N','N')))
    travel_distance = models.IntegerField(blank=True, null=True)
    travel_time = models.CharField(blank=True, null=True,max_length=20)
    deleted_status = models.SmallIntegerField(default=0, blank=True, null=True)

    class Meta:
        managed = False 
        db_table = 'bus_route_point'
        verbose_name_plural = 'Bus Route Point'
        unique_together = ['route_id', 'route_order'] 
        # permissions = (
        #     ('can_add_bus_route_point', 'Can add bus route point'),
        #     ('can_change_bus_route_point', 'Can change bus route point'),
        #     ('can_delete_bus_route_point', 'Can delete bus route point'),
        #     ('can_view_bus_route_point', 'Can view bus route point'),
        # )

    # def save(self, *args, **kwargs):
    #     # Check if route_order is not set and route_id is provided
    #     if not self.route_order and self.route_id:
    #         # Calculate the route_order based on your logic
    #         # For example, you can retrieve the maximum route_order for the given route_id and increment it.
    #         max_order = BusRoutePoint.objects.filter(route_id=self.route_id).aggregate(models.Max('route_order'))['route_order__max']
    #         self.route_order = max_order + 1 if max_order is not None else 1

    #     super(BusRoutePoint, self).save(*args, **kwargs)

    # def save(self, *args, **kwargs):
    #     if not self.route_order and self.route_id:
    #         # Calculate the route_order as one more than the maximum route_order
    #         max_order = BusRoutePoint.objects.filter(route_id=self.route_id).aggregate(models.Max('route_order'))['route_order__max']
    #         self.route_order = max_order + 1 if max_order is not None else 1

    #     # Fetch all existing route_point_id values
    #     existing_route_point_ids = BusRoutePoint.objects.all().values_list('route_point_id', flat=True)

    #     # Calculate the new route_point_id as one more than the maximum existing value
    #     self.route_point_id = max(existing_route_point_ids, default=0) + 1

    #     super().save(*args, **kwargs)

    def save(self, *args, **kwargs):
        with transaction.atomic():  # Ensure consistency
            if not self.route_points_id:
                last_instance = BusRoutePoint.objects.last()
                self.route_points_id = last_instance.route_points_id + 1 if last_instance and last_instance.route_points_id else 1

            if not self.route_order and self.route_id:
                existing_route_points = BusRoutePoint.objects.filter(route_id=self.route_id).order_by('route_order')

                if existing_route_points.exists():
                    new_order = self.route_order

                    # Update all existing orders for the route_id to ensure consistency
                    existing_route_points.update(route_order=F('route_order') + 1)

                    self.route_order = 1  # Assign the new record the first position
                else:
                    self.route_order = 1

            try:
                super(BusRoutePoint, self).save(*args, **kwargs)
            except IntegrityError:
                # Handle potential errors gracefully
                print("Error: Route order already exists for the route. All route orders for the route have been updated. Please try saving again.")
            # super().save(*args, **kwargs) 


    # def route_point_id(self):
    #     # This property should return the existing route_point_id
    #     return self.id  # Assuming that route_point_id is equivalent to the primary key (id)

    # def save(self, *args, **kwargs):
    #     if not self.route_order and self.route_id:
    #         # Calculate the route_order as one more than the maximum route_order
    #         max_order = BusRoutePoint.objects.filter(route_id=self.route_id).aggregate(models.Max('route_order'))['route_order__max']
    #         self.route_order = max_order + 1 if max_order is not None else 1

    #     super().save(*args, **kwargs)

    # def save(self, *args, **kwargs):
    #     if not self.route_order and self.route_id:
    #         # Get the last route_points_id and increment by 1
    #         last_route_point = YourModel.objects.order_by('-route_points_id').first()
    #         max_route_point_id = last_route_point.route_points_id if last_route_point else 0
    #         self.route_order = max_route_point_id + 1

    #     super().save(*args, **kwargs)
    

class RouteVehicleMap(models.Model):
    id = models.AutoField(primary_key=True, db_column='id')
    route_id = models.IntegerField()
    route_number = models.CharField(max_length=250)
    vehicle_number = models.CharField(max_length=50)
    device_imei = models.CharField(max_length=15)
    vstc_id = models.CharField(max_length=250)

    class Meta:
        managed = False
        db_table = 'route_vehicle_map'
        verbose_name_plural = 'Route Vehicle Map'
        # permissions = (
        #     ('can_add_route_vehicle_map', 'Can add route vehicle map'),
        #     ('can_change_route_vehicle_map', 'Can change route vehicle map'),
        #     ('can_delete_route_vehicle_map', 'Can delete route vehicle map'),
        #     ('can_view_route_vehicle_map', 'Can view route vehicle map'),
        # )

    def save(self, *args, **kwargs):
        if not self.id:
            last_instance = RouteVehicleMap.objects.last()
            self.id = last_instance.id + 1 if last_instance else 1
        super().save(*args, **kwargs)

    def __str__(self):
        return self.id

class BusScheduleDetails(models.Model):
    schedule_details_id = models.AutoField(primary_key=True)
    schedule_number = models.CharField(max_length=50)
    route_number_id = models.ForeignKey(BusRoute, related_name='routeNumId', on_delete=models.CASCADE,db_column='route_number_id')
    trip_number = models.IntegerField()
    distance = models.FloatField()
    start_time = models.TimeField()
    end_time = models.TimeField()
    running_time = models.TimeField()
    shift_type_name = models.CharField(max_length=50)
    org_name = models.CharField(max_length=50, blank=True)
    day_type = models.CharField(max_length=50, blank=True)
    # route_vehicle_map_id = models.ForeignKey(RouteVehicleMap, related_name='routeVehicleMap', on_delete=models.CASCADE, db_column='route_vehicle_map_id', blank=True, null=True)
    # route_vehicle_map_id = models.BigIntegerField(db_column='route_vehicle_map_id', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'bus_schedule_details'
        verbose_name_plural = 'Bus Schedule Detail'
        unique_together = ['trip_number', 'schedule_number'] 
        # permissions = (
        #     ('can_add_bus_schedule_details', 'Can add bus schedule details'),
        #     ('can_change_bus_schedule_details', 'Can change bus schedule details'),
        #     ('can_delete_bus_schedule_details', 'Can delete bus schedule details'),
        #     ('can_view_bus_schedule_details', 'Can view bus schedule details'),
        # )

    def save(self, *args, **kwargs):
        if not self.schedule_details_id:
            last_instance = BusScheduleDetails.objects.last()
            self.schedule_details_id = last_instance.schedule_details_id + 1 if last_instance else 1
        super().save(*args, **kwargs)


class BusFare(models.Model):
    fare_chart_id = models.AutoField(primary_key=True)
    # route_id = models.ForeignKey(BusRoute, related_name='routeNumIdforfare', on_delete=models.CASCADE,db_column='route_id')
    route_id = models.IntegerField()
    # start_point_id = models.ForeignKey(BusStop, related_name='start_point_id1', on_delete=models.CASCADE, db_column='start_point_id')
    start_point_id = models.IntegerField()
    # end_point_id = models.ForeignKey(BusStop, related_name='end_point_id1', on_delete=models.CASCADE, db_column='end_point_id')
    end_point_id = models.IntegerField()
    number_of_kms = models.IntegerField()
    fare_amount = models.FloatField(blank=True, null=True)
    toll_fee = models.FloatField(blank=True, null=True)
    status = models.CharField(max_length=50, blank=True)
    


    class Meta:
        managed = False
        db_table = 'fare_chart'
        verbose_name_plural = 'Fare Chart'

    def save(self, *args, **kwargs):
        if not self.fare_chart_id:
            last_instance = BusFare.objects.last()
            self.fare_chart_id = last_instance.fare_chart_id + 1 if last_instance else 1
        super().save(*args, **kwargs)

class RateMasterTable(models.Model):
    rate_master_id=models.AutoField(primary_key=True)
    parent_rate_master=models.IntegerField(null=True,blank=True)
    version_number=models.CharField(max_length=50, blank=True)
    version_number_service_stype=models.CharField(max_length=50, blank=True)
    service_type_id=models.IntegerField()
    effective_start_date=models.CharField(max_length=5, blank=True)
    effective_end_date=models.CharField(max_length=5, blank=True,null=True)
    status=models.CharField(max_length=50, blank=True)
    deleted_status =models.IntegerField(default=0, blank=True, null=True)
    created_by = models.IntegerField()
    created_date =  models.CharField(max_length=50, blank=True)
    updated_by = models.IntegerField()
    updated_date = models.CharField(max_length=5, blank=True)
    sync_updated_date = models.CharField(max_length=5, blank=True)


    class Meta:
        managed = False
        db_table = 'rate_master'
        verbose_name_plural = 'Rate Master Table'

    def save(self, *args, **kwargs):
        if not self.rate_master_id:
            last_instance = Rate_Master_Table.objects.last()
            self.rate_master_id = last_instance.rate_master_id + 1 if last_instance else 1
        super().save(*args, **kwargs)
    
    def __str__(self):
        return str(self.rate_master_id)


class FareChartMaster(models.Model):
    farechart_master_id=models.AutoField(primary_key=True)
    # route_id = models.ForeignKey(BusRoute, related_name='routeNumIdforfare1', on_delete=models.CASCADE,db_column='route_id')
    route_id=models.IntegerField()
    route_name = models.CharField(max_length=50, null=True)
    service_type_id=models.IntegerField() 
    passenger_type_id=models.IntegerField()
    # rate_master_id=models.ForeignKey(RateMasterTable, related_name='rate_master_id2', on_delete=models.CASCADE,db_column='rate_master_id')
    rate_master_id=models.IntegerField()
    route_fare_map_id=models.IntegerField()
    farechart_name=models.CharField(max_length=50, blank=True)
    schedule_service =models.IntegerField()
    percentage = models.IntegerField()
    deleted_status = models.IntegerField(default=0, blank=True, null=True)
    ceiling_fare = models.IntegerField()
    nignt_service = models.CharField(max_length=5, blank=True)
    flexi_fare = models.CharField(max_length=5, blank=True)

    class Meta:
        managed = False
        db_table = 'farechart_master'
        verbose_name_plural = 'Fare Chart Master'

    def save(self, *args, **kwargs):
        if not self.fare_chart_master_id:
            last_instance = FareChartMaster.objects.last()
            self.fare_chart_master_id = last_instance.fare_chart_master_id + 1 if last_instance else 1
        super().save(*args, **kwargs)

    def __str__(self):
        return str(self.farechart_master_id)  
        
class RateMasterDetails(models.Model):
    rate_master_details_id=models.AutoField(primary_key=True)
    # rate_master_id=models.ForeignKey(RateMasterTable, related_name='rate_master_id1', on_delete=models.CASCADE,db_column='rate_master_id')
    rate_master_id=models.IntegerField()
    service_type_id=models.IntegerField()
    stage_no=models.IntegerField()
    adult=models.IntegerField() 
    children=models.IntegerField()
    senior_citizen=models.IntegerField()
    luggage=models.FloatField()
    happy_hour1=models.IntegerField()
    happy_hour2=models.IntegerField()
    deleted_status =models.IntegerField(default=0, blank=True, null=True)
    created_by = models.IntegerField()
    created_date = models.CharField(max_length=50, blank=True)
    updated_by = models.IntegerField()
    updated_date = models.CharField(max_length=50, blank=True)
    sync_updated_date = models.CharField(max_length=50, blank=True)


    class Meta:
        managed = False
        db_table = 'rate_master_details'
        verbose_name_plural = 'Rate Master Details'
        unique_together = ['rate_master_id', 'stage_no'] 


    def save(self, *args, **kwargs):
        with transaction.atomic():  # Ensure consistency
            if not self.rate_master_details_id:
                last_instance = RateMasterDetails.objects.last()
                self.rate_master_details_id = last_instance.rate_master_details_id + 1 if last_instance else 1

            if not self.stage_no and self.rate_master_details_id:
                existing_rate_master_details = RateMasterDetails.objects.filter(
                    rate_master_details_id=self.rate_master_details_id
                ).order_by('stage_no')

                if existing_rate_master_details.exists():
                    # Shift existing stages to make room for the new stage
                    existing_rate_master_details.filter(stage_no__gte=self.stage_no).update(stage_no=F('stage_no') + 1)
                    self.stage_no = self.stage_no  # Keep the intended stage_no
                else:
                    self.stage_no = 1  # First stage for a new rate_master_details_id

            try:
                super(RateMasterDetails, self).save(*args, **kwargs)
            except IntegrityError:
                print("Error: stage_no already exists for the Rate_master_details. Please choose a different stage number or update an existing record.")




class BusFare2(models.Model):
    fare_chart_id = models.AutoField(primary_key=True)
    # farechart_master_id = models.ForeignKey(FareChartMaster, related_name='faremasterId', on_delete=models.CASCADE,db_column='farechart_master_id')
    farechart_master_id= models.IntegerField()
    # route_id = models.ForeignKey(BusRoute, related_name='routeNumIdforfare2', on_delete=models.CASCADE,db_column='route_id')
    route_id = models.IntegerField()
    service_type_id= models.IntegerField()
    passenger_type_id= models.IntegerField()
    schedule_type_id=models.IntegerField()
    # start_point_id = models.ForeignKey(BusStop, related_name='start_point', on_delete=models.CASCADE, db_column='start_point_id')
    start_point_id = models.IntegerField()
    # end_point_id = models.ForeignKey(BusStop, related_name='end_point', on_delete=models.CASCADE, db_column='end_point_id')
    end_point_id =models.IntegerField()
    number_of_kms = models.IntegerField(blank=True, null=True)
    fare_amount = models.FloatField(blank=True, null=True)
    toll_fee = models.FloatField(blank=True, null=True)
    


    class Meta:
        managed = False
        db_table = 'fare_chart'
        verbose_name_plural = 'Fare Chart'

    def save(self, *args, **kwargs):
        if not self.fare_chart_id:
            last_instance = BusFare2.objects.last()
            self.fare_chart_id = last_instance.fare_chart_id + 1 if last_instance else 1
        super().save(*args, **kwargs)