import os
import csv
import json
import zipfile
import logging
from datetime import datetime, date
from typing import Dict, List, Any, Optional
from django.conf import settings
from django.core.files.base import ContentFile
from django.utils import timezone

from ..models import UploadedFile, GTFSDataset, ExtractedTransitData
from .ai_analyzer import AI<PERSON><PERSON><PERSON><PERSON>

logger = logging.getLogger(__name__)


class GTFSGeneratorService:
    """Service for generating GTFS data from extracted transit information"""
    
    def __init__(self):
        self.gtfs_files = {
            'agency.txt': self._generate_agency_file,
            'routes.txt': self._generate_routes_file,
            'trips.txt': self._generate_trips_file,
            'stops.txt': self._generate_stops_file,
            'stop_times.txt': self._generate_stop_times_file,
            'calendar.txt': self._generate_calendar_file,
            'calendar_dates.txt': self._generate_calendar_dates_file,
            'fare_attributes.txt': self._generate_fare_attributes_file,
            'fare_rules.txt': self._generate_fare_rules_file,
            'shapes.txt': self._generate_shapes_file,
            'frequencies.txt': self._generate_frequencies_file,
            'transfers.txt': self._generate_transfers_file,
        }
        
        # Required GTFS files
        self.required_files = [
            'agency.txt', 'routes.txt', 'trips.txt', 
            'stops.txt', 'stop_times.txt', 'calendar.txt'
        ]
    
    def generate_gtfs_from_file(self, uploaded_file: UploadedFile) -> GTFSDataset:
        """
        Generate GTFS dataset from an uploaded file
        
        Args:
            uploaded_file: UploadedFile instance with processed data
            
        Returns:
            GTFSDataset instance
        """
        try:
            logger.info(f"Starting GTFS generation for file: {uploaded_file.original_filename}")
            
            # Create GTFS dataset record
            dataset_name = f"GTFS_{uploaded_file.original_filename}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            gtfs_dataset = GTFSDataset.objects.create(
                source_file=uploaded_file,
                dataset_name=dataset_name,
                description=f"GTFS data generated from {uploaded_file.original_filename}"
            )
            
            # Get extracted transit data
            extracted_data = ExtractedTransitData.objects.filter(source_file=uploaded_file)
            
            # Use AI to extract structured GTFS entities if available
            gtfs_entities = self._extract_gtfs_entities(uploaded_file, extracted_data)
            
            # Generate GTFS files
            gtfs_files_content = {}
            
            for filename, generator_func in self.gtfs_files.items():
                try:
                    content = generator_func(gtfs_entities, uploaded_file)
                    if content or filename in self.required_files:
                        gtfs_files_content[filename] = content
                        logger.info(f"Generated {filename}")
                except Exception as e:
                    logger.error(f"Error generating {filename}: {e}")
                    if filename in self.required_files:
                        # Generate minimal required file
                        gtfs_files_content[filename] = self._generate_minimal_file(filename)
            
            # Create ZIP file
            zip_content = self._create_gtfs_zip(gtfs_files_content)
            
            # Save ZIP file
            zip_filename = f"{dataset_name}.zip"
            gtfs_dataset.gtfs_zip_file.save(
                zip_filename,
                ContentFile(zip_content),
                save=False
            )
            
            # Save individual files
            self._save_individual_files(gtfs_dataset, gtfs_files_content)
            
            # Validate GTFS data
            validation_result = self._validate_gtfs_data(gtfs_files_content)
            gtfs_dataset.is_valid = validation_result['is_valid']
            gtfs_dataset.validation_report = json.dumps(validation_result, indent=2)
            gtfs_dataset.validation_date = timezone.now()
            
            gtfs_dataset.save()
            
            # Update source file
            uploaded_file.gtfs_generated = True
            uploaded_file.gtfs_generation_date = timezone.now()
            uploaded_file.save()
            
            logger.info(f"Successfully generated GTFS dataset: {dataset_name}")
            return gtfs_dataset
            
        except Exception as e:
            logger.error(f"Error generating GTFS data: {e}")
            raise e
    
    def _extract_gtfs_entities(self, uploaded_file: UploadedFile, extracted_data) -> Dict:
        """Extract GTFS entities using AI analysis"""
        try:
            # Try AI extraction first
            analyzer = AIAnalyzer()
            gtfs_entities = analyzer.extract_gtfs_entities(uploaded_file.extracted_text or "")
            
            # Enhance with extracted transit data
            for data_entry in extracted_data:
                self._merge_extracted_data(gtfs_entities, data_entry)
            
            return gtfs_entities
            
        except Exception as e:
            logger.warning(f"AI extraction failed, using fallback: {e}")
            return self._fallback_gtfs_extraction(uploaded_file, extracted_data)
    
    def _fallback_gtfs_extraction(self, uploaded_file: UploadedFile, extracted_data) -> Dict:
        """Fallback GTFS entity extraction"""
        entities = {
            "agencies": [],
            "routes": [],
            "stops": [],
            "trips": [],
            "stop_times": []
        }
        
        # Create default agency
        entities["agencies"].append({
            "agency_id": "1",
            "agency_name": "Transit Agency",
            "agency_url": "http://example.com",
            "agency_timezone": "America/New_York"
        })
        
        # Extract basic information from extracted data
        for data_entry in extracted_data:
            if data_entry.data_type == "route":
                entities["routes"].append({
                    "route_id": f"route_{len(entities['routes']) + 1}",
                    "agency_id": "1",
                    "route_short_name": f"Route {len(entities['routes']) + 1}",
                    "route_long_name": "Generated Route",
                    "route_type": "3"  # Bus
                })
            elif data_entry.data_type == "stop":
                entities["stops"].append({
                    "stop_id": f"stop_{len(entities['stops']) + 1}",
                    "stop_name": f"Stop {len(entities['stops']) + 1}",
                    "stop_lat": "0.0",
                    "stop_lon": "0.0"
                })
        
        return entities
    
    def _merge_extracted_data(self, gtfs_entities: Dict, data_entry: ExtractedTransitData):
        """Merge extracted transit data into GTFS entities"""
        try:
            structured_data = data_entry.structured_data
            data_type = data_entry.data_type
            
            if data_type == "agency" and "agencies" in gtfs_entities:
                # Add agency information
                pass  # Implementation depends on data structure
            elif data_type == "route" and "routes" in gtfs_entities:
                # Add route information
                pass  # Implementation depends on data structure
            # Add more data type handling as needed
            
        except Exception as e:
            logger.warning(f"Error merging extracted data: {e}")
    
    def _generate_agency_file(self, entities: Dict, uploaded_file: UploadedFile) -> str:
        """Generate agency.txt content"""
        agencies = entities.get("agencies", [])
        
        if not agencies:
            # Create default agency
            agencies = [{
                "agency_id": "1",
                "agency_name": "Transit Agency",
                "agency_url": "http://example.com",
                "agency_timezone": "America/New_York"
            }]
        
        # Required fields for agency.txt
        fieldnames = ["agency_id", "agency_name", "agency_url", "agency_timezone"]
        
        return self._create_csv_content(fieldnames, agencies)
    
    def _generate_routes_file(self, entities: Dict, uploaded_file: UploadedFile) -> str:
        """Generate routes.txt content"""
        routes = entities.get("routes", [])
        
        if not routes:
            # Create default route
            routes = [{
                "route_id": "1",
                "agency_id": "1",
                "route_short_name": "1",
                "route_long_name": "Main Route",
                "route_type": "3"  # Bus
            }]
        
        fieldnames = ["route_id", "agency_id", "route_short_name", "route_long_name", "route_type"]
        
        return self._create_csv_content(fieldnames, routes)
    
    def _generate_trips_file(self, entities: Dict, uploaded_file: UploadedFile) -> str:
        """Generate trips.txt content"""
        trips = entities.get("trips", [])
        
        if not trips:
            # Create default trip
            trips = [{
                "route_id": "1",
                "service_id": "1",
                "trip_id": "1",
                "trip_headsign": "Main Destination"
            }]
        
        fieldnames = ["route_id", "service_id", "trip_id", "trip_headsign"]
        
        return self._create_csv_content(fieldnames, trips)
    
    def _generate_stops_file(self, entities: Dict, uploaded_file: UploadedFile) -> str:
        """Generate stops.txt content"""
        stops = entities.get("stops", [])
        
        if not stops:
            # Create default stops
            stops = [
                {
                    "stop_id": "1",
                    "stop_name": "Stop 1",
                    "stop_lat": "40.7128",
                    "stop_lon": "-74.0060"
                },
                {
                    "stop_id": "2",
                    "stop_name": "Stop 2",
                    "stop_lat": "40.7589",
                    "stop_lon": "-73.9851"
                }
            ]
        
        fieldnames = ["stop_id", "stop_name", "stop_lat", "stop_lon"]
        
        return self._create_csv_content(fieldnames, stops)
    
    def _generate_stop_times_file(self, entities: Dict, uploaded_file: UploadedFile) -> str:
        """Generate stop_times.txt content"""
        stop_times = entities.get("stop_times", [])
        
        if not stop_times:
            # Create default stop times
            stop_times = [
                {
                    "trip_id": "1",
                    "arrival_time": "08:00:00",
                    "departure_time": "08:00:00",
                    "stop_id": "1",
                    "stop_sequence": "1"
                },
                {
                    "trip_id": "1",
                    "arrival_time": "08:15:00",
                    "departure_time": "08:15:00",
                    "stop_id": "2",
                    "stop_sequence": "2"
                }
            ]
        
        fieldnames = ["trip_id", "arrival_time", "departure_time", "stop_id", "stop_sequence"]
        
        return self._create_csv_content(fieldnames, stop_times)

    def _generate_calendar_file(self, entities: Dict, uploaded_file: UploadedFile) -> str:
        """Generate calendar.txt content"""
        # Create default service calendar
        calendar_data = [{
            "service_id": "1",
            "monday": "1",
            "tuesday": "1",
            "wednesday": "1",
            "thursday": "1",
            "friday": "1",
            "saturday": "1",
            "sunday": "0",
            "start_date": "20240101",
            "end_date": "20241231"
        }]

        fieldnames = ["service_id", "monday", "tuesday", "wednesday", "thursday",
                     "friday", "saturday", "sunday", "start_date", "end_date"]

        return self._create_csv_content(fieldnames, calendar_data)

    def _generate_calendar_dates_file(self, entities: Dict, uploaded_file: UploadedFile) -> str:
        """Generate calendar_dates.txt content (optional)"""
        # Return empty content for optional file
        return ""

    def _generate_fare_attributes_file(self, entities: Dict, uploaded_file: UploadedFile) -> str:
        """Generate fare_attributes.txt content (optional)"""
        fares = entities.get("fares", [])

        if not fares:
            return ""  # Optional file

        fieldnames = ["fare_id", "price", "currency_type", "payment_method", "transfers"]

        return self._create_csv_content(fieldnames, fares)

    def _generate_fare_rules_file(self, entities: Dict, uploaded_file: UploadedFile) -> str:
        """Generate fare_rules.txt content (optional)"""
        return ""  # Optional file

    def _generate_shapes_file(self, entities: Dict, uploaded_file: UploadedFile) -> str:
        """Generate shapes.txt content (optional)"""
        return ""  # Optional file

    def _generate_frequencies_file(self, entities: Dict, uploaded_file: UploadedFile) -> str:
        """Generate frequencies.txt content (optional)"""
        return ""  # Optional file

    def _generate_transfers_file(self, entities: Dict, uploaded_file: UploadedFile) -> str:
        """Generate transfers.txt content (optional)"""
        return ""  # Optional file

    def _create_csv_content(self, fieldnames: List[str], data: List[Dict]) -> str:
        """Create CSV content from fieldnames and data"""
        import io

        output = io.StringIO()
        writer = csv.DictWriter(output, fieldnames=fieldnames)
        writer.writeheader()

        for row in data:
            # Ensure all required fields are present
            filtered_row = {field: row.get(field, "") for field in fieldnames}
            writer.writerow(filtered_row)

        return output.getvalue()

    def _generate_minimal_file(self, filename: str) -> str:
        """Generate minimal content for required GTFS files"""
        minimal_content = {
            'agency.txt': 'agency_id,agency_name,agency_url,agency_timezone\n1,Transit Agency,http://example.com,America/New_York\n',
            'routes.txt': 'route_id,agency_id,route_short_name,route_long_name,route_type\n1,1,1,Main Route,3\n',
            'trips.txt': 'route_id,service_id,trip_id,trip_headsign\n1,1,1,Main Destination\n',
            'stops.txt': 'stop_id,stop_name,stop_lat,stop_lon\n1,Stop 1,40.7128,-74.0060\n2,Stop 2,40.7589,-73.9851\n',
            'stop_times.txt': 'trip_id,arrival_time,departure_time,stop_id,stop_sequence\n1,08:00:00,08:00:00,1,1\n1,08:15:00,08:15:00,2,2\n',
            'calendar.txt': 'service_id,monday,tuesday,wednesday,thursday,friday,saturday,sunday,start_date,end_date\n1,1,1,1,1,1,1,0,20240101,20241231\n'
        }

        return minimal_content.get(filename, "")

    def _create_gtfs_zip(self, gtfs_files_content: Dict[str, str]) -> bytes:
        """Create ZIP file containing GTFS files"""
        import io

        zip_buffer = io.BytesIO()

        with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
            for filename, content in gtfs_files_content.items():
                if content:  # Only add non-empty files
                    zip_file.writestr(filename, content)

        return zip_buffer.getvalue()

    def _save_individual_files(self, gtfs_dataset: GTFSDataset, gtfs_files_content: Dict[str, str]):
        """Save individual GTFS files"""
        file_mapping = {
            'agency.txt': 'agency_file',
            'routes.txt': 'routes_file',
            'trips.txt': 'trips_file',
            'stops.txt': 'stops_file',
            'stop_times.txt': 'stop_times_file',
            'calendar.txt': 'calendar_file',
            'calendar_dates.txt': 'calendar_dates_file',
            'fare_attributes.txt': 'fare_attributes_file',
            'fare_rules.txt': 'fare_rules_file',
            'shapes.txt': 'shapes_file',
            'frequencies.txt': 'frequencies_file',
            'transfers.txt': 'transfers_file',
        }

        for filename, content in gtfs_files_content.items():
            if content and filename in file_mapping:
                field_name = file_mapping[filename]
                file_field = getattr(gtfs_dataset, field_name)
                file_field.save(
                    filename,
                    ContentFile(content.encode('utf-8')),
                    save=False
                )

    def _validate_gtfs_data(self, gtfs_files_content: Dict[str, str]) -> Dict:
        """Validate GTFS data for compliance"""
        validation_result = {
            "is_valid": True,
            "errors": [],
            "warnings": [],
            "validation_timestamp": datetime.now().isoformat()
        }

        # Check required files
        for required_file in self.required_files:
            if required_file not in gtfs_files_content or not gtfs_files_content[required_file]:
                validation_result["errors"].append(f"Missing required file: {required_file}")
                validation_result["is_valid"] = False

        # Basic content validation
        for filename, content in gtfs_files_content.items():
            if content:
                lines = content.strip().split('\n')
                if len(lines) < 2:  # Header + at least one data row
                    validation_result["warnings"].append(f"File {filename} has no data rows")

        # Additional validation could be added here
        # - Check field formats
        # - Validate relationships between files
        # - Check for required fields

        return validation_result
