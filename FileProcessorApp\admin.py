from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from django.http import HttpResponse
from django.shortcuts import redirect
from django.contrib import messages
import j<PERSON>

from .models import UploadedFile, GTFSDataset, ExtractedTransitData
from .service.file_processor import FileProcessorService
from .service.gtfs_generator import GTFSGeneratorService
from .service.file_manager import FileManagerService


@admin.register(UploadedFile)
class UploadedFileAdmin(admin.ModelAdmin):
    list_display = [
        'id', 'original_filename', 'file_type', 'file_size_display',
        'processing_status', 'uploaded_by', 'upload_date', 'gtfs_generated',
        'processing_duration_display', 'actions_column'
    ]

    list_filter = [
        'processing_status', 'file_type', 'gtfs_generated',
        'upload_date', 'uploaded_by'
    ]

    search_fields = [
        'original_filename', 'uploaded_by__username', 'uploaded_by__email'
    ]

    readonly_fields = [
        'id', 'original_filename', 'file_type', 'file_size', 'upload_date',
        'processing_started_at', 'processing_completed_at', 'gtfs_generation_date',
        'processing_duration_display', 'extracted_text_preview', 'ai_analysis_preview'
    ]

    fieldsets = (
        ('File Information', {
            'fields': ('id', 'file', 'original_filename', 'file_type', 'file_size', 'uploaded_by')
        }),
        ('Processing Status', {
            'fields': ('processing_status', 'processing_started_at', 'processing_completed_at',
                      'processing_duration_display', 'processing_error')
        }),
        ('Extracted Content', {
            'fields': ('extracted_text_preview', 'ai_analysis_preview'),
            'classes': ('collapse',)
        }),
        ('GTFS Generation', {
            'fields': ('gtfs_generated', 'gtfs_generation_date', 'gtfs_files_path')
        }),
    )

    actions = ['reprocess_files', 'generate_gtfs_for_files', 'cleanup_failed_files']

    def file_size_display(self, obj):
        """Display file size in human readable format"""
        if obj.file_size:
            size = obj.file_size
            for unit in ['B', 'KB', 'MB', 'GB']:
                if size < 1024.0:
                    return f"{size:.1f} {unit}"
                size /= 1024.0
            return f"{size:.1f} TB"
        return "Unknown"
    file_size_display.short_description = "File Size"

    def processing_duration_display(self, obj):
        """Display processing duration"""
        duration = obj.get_processing_duration()
        if duration:
            total_seconds = int(duration.total_seconds())
            minutes, seconds = divmod(total_seconds, 60)
            return f"{minutes}m {seconds}s"
        return "N/A"
    processing_duration_display.short_description = "Processing Duration"

    def extracted_text_preview(self, obj):
        """Display preview of extracted text"""
        if obj.extracted_text:
            preview = obj.extracted_text[:500] + "..." if len(obj.extracted_text) > 500 else obj.extracted_text
            return format_html('<pre style="white-space: pre-wrap; max-height: 200px; overflow-y: auto;">{}</pre>', preview)
        return "No text extracted"
    extracted_text_preview.short_description = "Extracted Text Preview"

    def ai_analysis_preview(self, obj):
        """Display preview of AI analysis"""
        if obj.ai_analysis:
            try:
                analysis = json.loads(obj.ai_analysis)
                formatted = json.dumps(analysis, indent=2)
                preview = formatted[:1000] + "..." if len(formatted) > 1000 else formatted
                return format_html('<pre style="white-space: pre-wrap; max-height: 200px; overflow-y: auto;">{}</pre>', preview)
            except json.JSONDecodeError:
                preview = obj.ai_analysis[:500] + "..." if len(obj.ai_analysis) > 500 else obj.ai_analysis
                return format_html('<pre style="white-space: pre-wrap; max-height: 200px; overflow-y: auto;">{}</pre>', preview)
        return "No AI analysis available"
    ai_analysis_preview.short_description = "AI Analysis Preview"

    def actions_column(self, obj):
        """Display action buttons"""
        actions = []

        if obj.processing_status in ['failed', 'completed']:
            reprocess_url = reverse('admin:fileprocessorapp_uploadedfile_reprocess', args=[obj.id])
            actions.append(f'<a href="{reprocess_url}" class="button">Reprocess</a>')

        if obj.processing_status == 'completed' and not obj.gtfs_generated:
            gtfs_url = reverse('admin:fileprocessorapp_uploadedfile_generate_gtfs', args=[obj.id])
            actions.append(f'<a href="{gtfs_url}" class="button">Generate GTFS</a>')

        if obj.gtfs_generated:
            gtfs_datasets = GTFSDataset.objects.filter(source_file=obj)
            for dataset in gtfs_datasets:
                view_url = reverse('admin:fileprocessorapp_gtfsdataset_change', args=[dataset.id])
                actions.append(f'<a href="{view_url}" class="button">View GTFS</a>')

        return mark_safe(' '.join(actions))
    actions_column.short_description = "Actions"

    def reprocess_files(self, request, queryset):
        """Admin action to reprocess selected files"""
        processor = FileProcessorService()
        success_count = 0

        for uploaded_file in queryset:
            uploaded_file.processing_status = 'pending'
            uploaded_file.processing_error = None
            uploaded_file.save()

            if processor.process_file(uploaded_file):
                success_count += 1

        self.message_user(
            request,
            f"Successfully reprocessed {success_count} out of {queryset.count()} files.",
            messages.SUCCESS
        )
    reprocess_files.short_description = "Reprocess selected files"

    def generate_gtfs_for_files(self, request, queryset):
        """Admin action to generate GTFS for selected files"""
        gtfs_generator = GTFSGeneratorService()
        success_count = 0

        for uploaded_file in queryset.filter(processing_status='completed'):
            try:
                gtfs_generator.generate_gtfs_from_file(uploaded_file)
                success_count += 1
            except Exception as e:
                self.message_user(
                    request,
                    f"Error generating GTFS for {uploaded_file.original_filename}: {e}",
                    messages.ERROR
                )

        self.message_user(
            request,
            f"Successfully generated GTFS for {success_count} files.",
            messages.SUCCESS
        )
    generate_gtfs_for_files.short_description = "Generate GTFS for selected files"

    def cleanup_failed_files(self, request, queryset):
        """Admin action to cleanup failed files"""
        failed_files = queryset.filter(processing_status='failed')
        count = failed_files.count()
        failed_files.delete()

        self.message_user(
            request,
            f"Deleted {count} failed files.",
            messages.SUCCESS
        )
    cleanup_failed_files.short_description = "Delete failed files"


@admin.register(GTFSDataset)
class GTFSDatasetAdmin(admin.ModelAdmin):
    list_display = [
        'id', 'dataset_name', 'source_file_link', 'creation_date',
        'is_valid', 'validation_date', 'download_link'
    ]

    list_filter = [
        'is_valid', 'creation_date', 'validation_date'
    ]

    search_fields = [
        'dataset_name', 'description', 'source_file__original_filename'
    ]

    readonly_fields = [
        'id', 'creation_date', 'validation_date', 'validation_report_display',
        'gtfs_files_info'
    ]

    fieldsets = (
        ('Dataset Information', {
            'fields': ('id', 'dataset_name', 'description', 'source_file', 'creation_date')
        }),
        ('GTFS Files', {
            'fields': ('gtfs_zip_file', 'gtfs_files_info'),
        }),
        ('Individual Files', {
            'fields': ('agency_file', 'routes_file', 'trips_file', 'stops_file',
                      'stop_times_file', 'calendar_file', 'calendar_dates_file'),
            'classes': ('collapse',)
        }),
        ('Optional Files', {
            'fields': ('fare_attributes_file', 'fare_rules_file', 'shapes_file',
                      'frequencies_file', 'transfers_file'),
            'classes': ('collapse',)
        }),
        ('Validation', {
            'fields': ('is_valid', 'validation_date', 'validation_report_display')
        }),
    )

    def source_file_link(self, obj):
        """Link to source file"""
        if obj.source_file:
            url = reverse('admin:fileprocessorapp_uploadedfile_change', args=[obj.source_file.id])
            return format_html('<a href="{}">{}</a>', url, obj.source_file.original_filename)
        return "No source file"
    source_file_link.short_description = "Source File"

    def download_link(self, obj):
        """Download link for GTFS ZIP"""
        if obj.gtfs_zip_file:
            return format_html(
                '<a href="{}" class="button" download>Download ZIP</a>',
                obj.gtfs_zip_file.url
            )
        return "No ZIP file"
    download_link.short_description = "Download"

    def validation_report_display(self, obj):
        """Display validation report"""
        if obj.validation_report:
            try:
                report = json.loads(obj.validation_report)
                formatted = json.dumps(report, indent=2)
                return format_html(
                    '<pre style="white-space: pre-wrap; max-height: 300px; overflow-y: auto;">{}</pre>',
                    formatted
                )
            except json.JSONDecodeError:
                return format_html('<pre>{}</pre>', obj.validation_report)
        return "No validation report"
    validation_report_display.short_description = "Validation Report"

    def gtfs_files_info(self, obj):
        """Display information about GTFS files"""
        file_fields = [
            ('agency_file', 'Agency'),
            ('routes_file', 'Routes'),
            ('trips_file', 'Trips'),
            ('stops_file', 'Stops'),
            ('stop_times_file', 'Stop Times'),
            ('calendar_file', 'Calendar'),
            ('calendar_dates_file', 'Calendar Dates'),
            ('fare_attributes_file', 'Fare Attributes'),
            ('fare_rules_file', 'Fare Rules'),
            ('shapes_file', 'Shapes'),
            ('frequencies_file', 'Frequencies'),
            ('transfers_file', 'Transfers'),
        ]

        info_lines = []
        for field_name, display_name in file_fields:
            file_field = getattr(obj, field_name)
            if file_field:
                try:
                    size = file_field.size
                    size_str = f"{size} bytes"
                    if size > 1024:
                        size_str = f"{size/1024:.1f} KB"
                    info_lines.append(f"✓ {display_name}: {size_str}")
                except:
                    info_lines.append(f"✓ {display_name}: Available")
            else:
                info_lines.append(f"✗ {display_name}: Not generated")

        return format_html('<pre>{}</pre>', '\n'.join(info_lines))
    gtfs_files_info.short_description = "GTFS Files Status"


@admin.register(ExtractedTransitData)
class ExtractedTransitDataAdmin(admin.ModelAdmin):
    list_display = [
        'id', 'source_file_link', 'data_type', 'confidence_score',
        'extraction_date', 'extraction_method', 'is_validated'
    ]

    list_filter = [
        'data_type', 'extraction_method', 'is_validated', 'extraction_date'
    ]

    search_fields = [
        'source_file__original_filename', 'data_type'
    ]

    readonly_fields = [
        'id', 'extraction_date', 'raw_data_display', 'structured_data_display'
    ]

    fieldsets = (
        ('Basic Information', {
            'fields': ('id', 'source_file', 'data_type', 'confidence_score',
                      'extraction_date', 'extraction_method')
        }),
        ('Extracted Data', {
            'fields': ('raw_data_display', 'structured_data_display'),
        }),
        ('Validation', {
            'fields': ('is_validated', 'validation_notes')
        }),
    )

    def source_file_link(self, obj):
        """Link to source file"""
        if obj.source_file:
            url = reverse('admin:fileprocessorapp_uploadedfile_change', args=[obj.source_file.id])
            return format_html('<a href="{}">{}</a>', url, obj.source_file.original_filename)
        return "No source file"
    source_file_link.short_description = "Source File"

    def raw_data_display(self, obj):
        """Display raw data"""
        if obj.raw_data:
            formatted = json.dumps(obj.raw_data, indent=2)
            preview = formatted[:1000] + "..." if len(formatted) > 1000 else formatted
            return format_html(
                '<pre style="white-space: pre-wrap; max-height: 300px; overflow-y: auto;">{}</pre>',
                preview
            )
        return "No raw data"
    raw_data_display.short_description = "Raw Data"

    def structured_data_display(self, obj):
        """Display structured data"""
        if obj.structured_data:
            formatted = json.dumps(obj.structured_data, indent=2)
            preview = formatted[:1000] + "..." if len(formatted) > 1000 else formatted
            return format_html(
                '<pre style="white-space: pre-wrap; max-height: 300px; overflow-y: auto;">{}</pre>',
                preview
            )
        return "No structured data"
    structured_data_display.short_description = "Structured Data"
