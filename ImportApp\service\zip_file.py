
class ZipFileReader():

def db_zip():
    with zipfile.ZipFile("myFiles.zip") as arch:
                        with (arch.open("bus_stop.csv", "r") as bus_stop,
                            arch.open("bus_route.csv", "r") as bus_route,
                            arch.open("bus_route_point.csv", "r") as bus_route_point,
                            arch.open("bus_schedule_details.csv","r") as bus_schedule_details,
                            arch.open("bus_service_type.csv", "r") as bus_service_type):
                            bus_stop = pd.read_csv(bus_stop)
                            # print(bus_stop)
                            bus_route = pd.read_csv(bus_route)
                            # print(bus_route)
                            bus_route_point = pd.read_csv(bus_route_point)
                            # print(bus_route_point)
                            bus_schedule_details = pd.read_csv(bus_schedule_details)
                            # print(bus_schedule_details)
                            bus_service_type = pd.read_csv(bus_service_type)
                            # print(bus_service_type)