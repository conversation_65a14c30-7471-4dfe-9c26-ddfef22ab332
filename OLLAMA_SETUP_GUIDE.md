# 🤖 Ollama Setup Guide for FileProcessorApp

This guide will help you set up Ollama for AI-powered transit data analysis in the FileProcessorApp.

## 🎯 What is Ollama?

Ollama allows you to run large language models locally on your machine, providing:
- **Privacy**: All data processing happens on your local machine
- **No API costs**: No external service fees
- **Offline functionality**: Works without internet connection
- **Fast processing**: Direct access to local AI models

## 🚀 Quick Setup (Recommended)

### Option 1: Automatic Setup
1. **Run the installer** (already downloaded):
   ```bash
   ./OllamaSetup.exe
   ```

2. **Run the setup script**:
   ```bash
   ./setup_ollama_simple.bat
   ```

### Option 2: Manual Setup

#### Step 1: Install Ollama
- The installer `OllamaSetup.exe` is already downloaded in your project directory
- Double-click to install, or run from command line:
  ```bash
  ./OllamaSetup.exe
  ```

#### Step 2: Start Ollama Service
```bash
ollama serve
```

#### Step 3: Download AI Models
Choose one of these models based on your needs:

**Lightweight (Recommended for testing):**
```bash
ollama pull phi
```
- Size: ~1.6GB
- Good for basic transit data analysis
- Fast processing

**Balanced (Recommended for production):**
```bash
ollama pull llama2
```
- Size: ~3.8GB
- Better accuracy for complex documents
- Good balance of speed and quality

**Advanced:**
```bash
ollama pull mistral
```
- Size: ~4.1GB
- High accuracy for complex analysis
- Slower but more thorough

#### Step 4: Test the Setup
```bash
ollama run phi "Analyze this transit data: Route 123 operates from Downtown to Airport"
```

## 🔧 Integration with FileProcessorApp

Once Ollama is running, FileProcessorApp will automatically:

1. **Detect Ollama**: Check if the service is running on `localhost:11434`
2. **Select Model**: Use the first available model (phi, llama2, mistral, etc.)
3. **Process Files**: Send extracted text to the AI for analysis
4. **Extract Data**: Identify routes, stops, schedules, fares, and agencies
5. **Generate GTFS**: Convert analyzed data into standard GTFS format

### Fallback Behavior
If Ollama is not available, FileProcessorApp will:
- Use rule-based keyword analysis
- Still extract basic transit information
- Generate GTFS files with available data
- Show a notice that AI analysis is unavailable

## 📊 Model Comparison

| Model | Size | Speed | Accuracy | Best For |
|-------|------|-------|----------|----------|
| phi | 1.6GB | Fast | Good | Testing, simple documents |
| llama2 | 3.8GB | Medium | Better | Production, mixed documents |
| mistral | 4.1GB | Slower | Best | Complex documents, high accuracy |

## 🛠️ Troubleshooting

### Common Issues

#### 1. "ollama command not found"
- **Solution**: Restart your terminal after installation
- **Alternative**: Add Ollama to your PATH manually

#### 2. "Connection refused" error
- **Solution**: Make sure Ollama service is running: `ollama serve`
- **Check**: Service should be accessible at `http://localhost:11434`

#### 3. Model download fails
- **Solution**: Check internet connection
- **Alternative**: Try a smaller model first (phi)
- **Retry**: `ollama pull <model-name>` again

#### 4. High memory usage
- **Solution**: Use a smaller model (phi instead of llama2)
- **Alternative**: Close other applications to free RAM

#### 5. Slow processing
- **Solution**: Use phi model for faster processing
- **Hardware**: Consider upgrading RAM for better performance

### Performance Tips

1. **RAM Requirements**:
   - phi: 2GB minimum, 4GB recommended
   - llama2: 4GB minimum, 8GB recommended
   - mistral: 4GB minimum, 8GB recommended

2. **Storage Requirements**:
   - Allow 5-10GB for models and cache
   - SSD recommended for faster loading

3. **CPU Considerations**:
   - Modern multi-core CPU recommended
   - GPU acceleration available with compatible hardware

## 🔍 Testing Your Setup

### 1. Basic Test
```bash
ollama list
```
Should show your downloaded models.

### 2. Model Test
```bash
ollama run phi "Hello, can you help analyze transit schedules?"
```

### 3. FileProcessorApp Test
1. Start Django server: `python manage.py runserver`
2. Go to: `http://localhost:8000/fileprocessor/upload/`
3. Upload a transit-related document
4. Check processing logs for AI analysis

## 📈 Usage in FileProcessorApp

### What the AI Analyzes
- **Routes**: Route numbers, names, descriptions
- **Stops**: Stop names, locations, IDs
- **Schedules**: Departure/arrival times, frequencies
- **Fares**: Pricing information, payment methods
- **Agencies**: Transit authority information

### AI Analysis Output
The AI provides:
- **Structured data**: Organized by transit data type
- **Confidence scores**: Quality assessment (0.0 to 1.0)
- **Extracted entities**: Ready for GTFS conversion
- **Validation notes**: Data quality indicators

### Example Analysis
Input: "Route 42 runs from Central Station to Airport every 15 minutes"

AI Output:
```json
{
  "routes": [{
    "route_id": "42",
    "route_name": "Central Station to Airport",
    "frequency": "15 minutes"
  }],
  "stops": [{
    "stop_name": "Central Station"
  }, {
    "stop_name": "Airport"
  }]
}
```

## 🔒 Privacy & Security

### Data Privacy
- **Local processing**: All data stays on your machine
- **No external calls**: No data sent to cloud services
- **Secure**: Transit data remains confidential

### Security Considerations
- Ollama runs on localhost only by default
- No external network access required for processing
- Models are downloaded once and cached locally

## 🚀 Advanced Configuration

### Custom Model Configuration
Edit `FileProcessorApp/service/ai_analyzer.py`:
```python
# Use specific model
analyzer = AIAnalyzer(model_name="your-preferred-model")
```

### Performance Tuning
```bash
# Adjust model parameters
ollama run phi --temperature 0.1 --num-predict 500 "your prompt"
```

### Multiple Models
You can install multiple models and switch between them:
```bash
ollama pull phi
ollama pull llama2
ollama pull mistral
```

## 📚 Additional Resources

- **Ollama Documentation**: https://ollama.ai/docs
- **Model Library**: https://ollama.ai/library
- **Community**: https://discord.gg/ollama
- **GitHub**: https://github.com/ollama/ollama

## ✅ Setup Checklist

- [ ] Ollama installed (`./OllamaSetup.exe`)
- [ ] Service running (`ollama serve`)
- [ ] Model downloaded (`ollama pull phi`)
- [ ] Test successful (`ollama run phi "test"`)
- [ ] FileProcessorApp detects AI (check upload page)
- [ ] Upload test file and verify AI analysis

---

## 🎉 You're Ready!

Once setup is complete, your FileProcessorApp will have AI-powered transit data extraction capabilities. Upload any transit-related document and watch as the AI intelligently identifies and structures the data for GTFS generation!

**Happy transit data processing! 🚌🤖**
