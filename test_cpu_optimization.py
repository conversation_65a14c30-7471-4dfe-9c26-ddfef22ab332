#!/usr/bin/env python
"""
Test script to verify CPU optimization for EC2 deployment
"""

import os
import sys
import django
import multiprocessing
import time
from pathlib import Path

# Add the project directory to Python path
BASE_DIR = Path(__file__).resolve().parent
sys.path.append(str(BASE_DIR))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'TransitCMS.settings')
django.setup()

from FileProcessorApp.service.ai_analyzer import AIAnalyzer


def test_cpu_configuration():
    """Test CPU configuration detection and optimization"""
    print("🔧 Testing CPU Configuration for EC2")
    print("=" * 50)
    
    # Test CPU detection
    cpu_count = multiprocessing.cpu_count()
    print(f"✅ Detected CPU cores: {cpu_count}")
    
    # Test memory detection
    try:
        import psutil
        memory = psutil.virtual_memory()
        memory_gb = memory.total / (1024**3)
        available_gb = memory.available / (1024**3)
        print(f"✅ Total memory: {memory_gb:.1f} GB")
        print(f"✅ Available memory: {available_gb:.1f} GB")
    except ImportError:
        print("⚠️  psutil not available for memory detection")
    
    # Test AI analyzer configuration
    print(f"\n🤖 Testing AI Analyzer CPU Configuration...")
    analyzer = AIAnalyzer()
    
    print(f"✅ CPU config generated:")
    for key, value in analyzer.cpu_config.items():
        print(f"   - {key}: {value}")
    
    # Verify CPU-only configuration
    assert analyzer.cpu_config["num_gpu"] == 0, "GPU should be disabled"
    assert analyzer.cpu_config["num_thread"] > 0, "Should use multiple threads"
    
    if cpu_count >= 4:
        expected_threads = cpu_count - 1
    else:
        expected_threads = cpu_count
    
    assert analyzer.cpu_config["num_thread"] == expected_threads, f"Expected {expected_threads} threads"
    
    print(f"✅ CPU optimization verified: {analyzer.cpu_config['num_thread']} threads, CPU-only mode")
    
    return True


def test_memory_scaling():
    """Test memory-based configuration scaling"""
    print(f"\n💾 Testing Memory-Based Scaling...")
    
    analyzer = AIAnalyzer()
    config = analyzer.cpu_config
    
    # Check context window scaling
    try:
        import psutil
        available_memory_gb = psutil.virtual_memory().available / (1024**3)
        
        if available_memory_gb < 4:
            expected_ctx = 1024
        elif available_memory_gb < 8:
            expected_ctx = 2048
        else:
            expected_ctx = 4096
        
        print(f"✅ Available memory: {available_memory_gb:.1f} GB")
        print(f"✅ Context window: {config['num_ctx']} (expected: {expected_ctx})")
        print(f"✅ Prediction limit: {config['num_predict']}")
        
        # Verify scaling is reasonable
        assert config['num_ctx'] in [1024, 2048, 4096], "Context window should be scaled appropriately"
        
    except ImportError:
        print("⚠️  psutil not available, using default memory settings")
        assert config['num_ctx'] == 2048, "Should use default context window"
    
    return True


def test_model_prioritization():
    """Test CPU-optimized model prioritization"""
    print(f"\n🎯 Testing Model Prioritization...")
    
    analyzer = AIAnalyzer()
    
    # Check if Ollama is available
    if analyzer.client is None:
        print("⚠️  Ollama not available, testing fallback behavior")
        
        # Test fallback analysis
        test_text = "Route 123 operates from Central Station to Airport Terminal every 15 minutes."
        result = analyzer._fallback_analysis(test_text)
        
        print("✅ Fallback analysis working")
        print(f"✅ Analysis length: {len(result)} characters")
        
        import json
        analysis = json.loads(result)
        assert "findings" in analysis, "Analysis should contain findings"
        
        return True
    
    else:
        print(f"✅ Ollama client available")
        print(f"✅ Selected model: {analyzer.model_name}")
        
        # Verify CPU-optimized model priority
        cpu_optimized_models = ["phi", "llama2", "mistral"]
        assert analyzer.model_name in cpu_optimized_models, f"Should use CPU-optimized model, got: {analyzer.model_name}"
        
        print(f"✅ Using CPU-optimized model: {analyzer.model_name}")
        
        return True


def test_performance_simulation():
    """Simulate processing performance"""
    print(f"\n⚡ Testing Performance Simulation...")
    
    analyzer = AIAnalyzer()
    
    # Test with sample transit text
    sample_texts = [
        "Route 42 runs from Downtown to Airport every 20 minutes.",
        "Bus stops: Central Station, University Campus, Shopping Mall, Airport Terminal.",
        "Service hours: 6:00 AM to 11:00 PM, Monday through Friday.",
        "Fare: $2.75 for adults, $1.50 for seniors and students."
    ]
    
    total_time = 0
    processed_count = 0
    
    for i, text in enumerate(sample_texts, 1):
        print(f"Processing sample {i}/{len(sample_texts)}...")
        
        start_time = time.time()
        
        if analyzer.client:
            # Test with actual AI if available
            try:
                result = analyzer.analyze_transit_data(text)
                processing_time = time.time() - start_time
                print(f"✅ AI analysis completed in {processing_time:.2f} seconds")
            except Exception as e:
                print(f"⚠️  AI analysis failed: {e}, using fallback")
                result = analyzer._fallback_analysis(text)
                processing_time = time.time() - start_time
                print(f"✅ Fallback analysis completed in {processing_time:.2f} seconds")
        else:
            # Use fallback analysis
            result = analyzer._fallback_analysis(text)
            processing_time = time.time() - start_time
            print(f"✅ Fallback analysis completed in {processing_time:.2f} seconds")
        
        total_time += processing_time
        processed_count += 1
        
        # Verify result
        assert len(result) > 0, "Analysis should return results"
    
    avg_time = total_time / processed_count
    print(f"\n📊 Performance Summary:")
    print(f"✅ Processed {processed_count} samples")
    print(f"✅ Total time: {total_time:.2f} seconds")
    print(f"✅ Average time per sample: {avg_time:.2f} seconds")
    
    # Performance expectations for EC2
    if avg_time < 5.0:
        print("🚀 Excellent performance for EC2 deployment")
    elif avg_time < 10.0:
        print("✅ Good performance for EC2 deployment")
    else:
        print("⚠️  Consider optimizing or using larger EC2 instance")
    
    return True


def main():
    """Run all CPU optimization tests"""
    print("🚀 CPU Optimization Test Suite for EC2 Deployment")
    print("=" * 60)
    
    tests = [
        ("CPU Configuration", test_cpu_configuration),
        ("Memory Scaling", test_memory_scaling),
        ("Model Prioritization", test_model_prioritization),
        ("Performance Simulation", test_performance_simulation),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 Running: {test_name}")
            result = test_func()
            results.append((test_name, result))
            print(f"✅ {test_name}: PASSED")
        except Exception as e:
            print(f"❌ {test_name}: FAILED - {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 CPU OPTIMIZATION TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All CPU optimization tests passed!")
        print("🚀 FileProcessorApp is ready for EC2 deployment with:")
        print("   - Multi-threaded CPU processing")
        print("   - Memory-adaptive configuration")
        print("   - CPU-optimized model selection")
        print("   - Performance monitoring")
        print("\n💡 Recommended EC2 instances:")
        print("   - Development: t3.medium (2 vCPUs, 4GB RAM)")
        print("   - Production: c5.large (2 vCPUs, 4GB RAM)")
        print("   - High Load: c5.xlarge (4 vCPUs, 8GB RAM)")
    else:
        print("\n⚠️  Some tests failed. Check the output above for details.")
        print("💡 The system will still work but may not be fully optimized for EC2.")
    
    return passed == total


if __name__ == "__main__":
    main()
