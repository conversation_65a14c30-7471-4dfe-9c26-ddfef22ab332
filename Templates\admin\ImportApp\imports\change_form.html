{% extends "admin/base.html" %}
{% load static %}
{% block content %}

<head>
    <!-- <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.0.0/dist/css/bootstrap.min.css" integrity="sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm" crossorigin="anonymous">
    <script src="https://code.jquery.com/jquery-3.2.1.slim.min.js" integrity="sha384-KJ3o2DKtIkvYIK3UENzmM7KCkRr/rE9/Qpg6aAZGJwFDMVNA/GpGFF93hXpG5KkN" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.12.9/dist/umd/popper.min.js" integrity="sha384-ApNbgh9B+Y1QKtv3Rn7W3mgPxhU9K/ScQsAP7hUibX39j7fakFPskvXusvfa0b4Q" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.0.0/dist/js/bootstrap.min.js" integrity="sha384-JZR6Spejh4U02d8jOt6vLEHfe/JQGiRRSQQxSfFWpi1MquVdAyjUar5+76PVCmYl" crossorigin="anonymous"></script> -->


    <style>
        /* .loader {
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid #fff;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin-top: -8rem;
        }

        .loader_div {
            background-color: #000;
            display: flex;
            padding: 0 !important;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
            position: absolute;
            opacity: 0.15;
            z-index: 3;
            margin: 0;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        } */

        /* Hide content initially */
        .hidden {
            display: none;
        }

        .progress {
            width: 100%;
            height: 30px;
            background-color: #0a0a0a;
            margin-bottom: 20px;
        }

        .progress-bar {
            height: 100%;
            background-color: #4caf50;
            text-align: center;
            line-height: 30px;
            color: rgb(250, 250, 250);
        }
    </style>
</head>


<!-- <div id="loader_div" class="loader_div">
    <div id="loader" class="loader">
    </div>
</div>  -->
<form action="." method="post" enctype="multipart/form-data">
    {% csrf_token %}
    <label for="city_name" class="form-group">City name: </label>
    {% if request.session.city_name %}

    <input id="city_name" type="text" class="form-control" name="city_name" value="{{ request.session.city_name }}"
        required readonly>
    {% endif %}
    <br><br>
    <label for="module" class="form-group">Module:</label>
    <select id="module" name="module" class="form-select" required>
        <option value="" disabled selected>Choose Module</option>
        <option value="Bus">Bus</option>
        <option value="Rail">Rail</option>
        <option value="Metro">Metro</option>
    </select><br><br>
    <label for="module_type" class="form-group">Module type: </label>
    <select id="module_type" name="module_type" class="form-select" required>
        <option value="" disabled selected>Choose Module Type</option>
    </select><br><br>

    <label for="service" class="form-group">Service: </label>
    <select id="service" name="service" class="form-select" required>
        <option value="Default" selected>Default Option</option>
    </select><br><br>
    <label for="File path" class="form-group">File path: </label>
    <input id="File path" type="File" class="form-control" name="file_path" required><br><br>


    <label for="user_agent" class="form-group" hidden>User agent: </label>
    <input id="user_agent" type="text" name="user_agent" class="form-control" value="{{user}}" required readonly
        hidden><br><br>
    <label for="User id" class="form-group" hidden>User id: </label>
    <input id="User id" type="number" name="user_id" class="form-control" value="{{user.id}}" required readonly
        hidden><br><br>
    <input type="submit" value="upload" onclick="startProcessing()">

    {{msg1}} &nbsp; {{msg}}

</form>

<div id="progressContainer" class="progress hidden">
    <div id="progressBar" class="progress-bar" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
        0%
    </div>
</div>

<!-- <div class="progress" role="progressbar" aria-label="Animated striped example" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100">
    <div class="progress-bar progress-bar-striped progress-bar-animated" style="width: 75%"></div>
  </div> -->



<script>

    document.addEventListener("DOMContentLoaded", function () {
        var firstSelect = document.getElementById("module");
        var secondSelect = document.getElementById("module_type");
        var thirdSelect = document.getElementById("service");

        var optionsMap = {
            'Bus': ['BusStop', 'BusRoute', 'BusRoutePoint', 'BusScheduleDetails', 'BusServiceType', 'BusFareVersion1', 'BusFareVersion2'],
            'Rail': ['RailStation', 'RailRoute', 'RailSchedule', 'RailSchedulePoint', 'RailFareDistancePoint', 'RailFare', 'RailLineMaster', 'RailCrossStation'],
            'Metro': ['MetroRoute', 'MetroTime', 'MetroFare'],
        };

        var conditionalServiceOptions = {
            'BusFareVersion2': ['FareChartMaster', 'RateMasterDetails', 'RateMasterTable', 'FareChart'],
        };

        firstSelect.addEventListener("change", function () {
            var selectedOption = firstSelect.value;
            var newOptions = optionsMap[selectedOption];

            // Clear existing options
            secondSelect.innerHTML = '';
            //thirdSelect.innerHTML = ''; // Clear the "service" dropdown

            // Add new options to the second dropdown
            newOptions.forEach(function (optionText) {
                var option = document.createElement("option");
                option.value = optionText;
                option.textContent = optionText;
                secondSelect.appendChild(option);
            });
        });

        secondSelect.addEventListener("change", function () {
            var selectedModuleType = secondSelect.value;

            // Clear the "service" dropdown
            thirdSelect.innerHTML = '';

            // Check if the selected module type is 'bus_fare-version2'
            if (selectedModuleType === 'BusFareVersion2') {
                // Show conditional service options
                conditionalServiceOptions['BusFareVersion2'].forEach(function (optionText) {
                    var option = document.createElement("option");
                    option.value = optionText;
                    option.textContent = optionText;
                    thirdSelect.appendChild(option);
                });
            } else {
                // Add a default option for other module types
                var defaultOption = document.createElement("option");
                defaultOption.value = 'default';
                defaultOption.textContent = 'Default Option';
                thirdSelect.appendChild(defaultOption);
            }
        });
    });

    // function startProcessing() {
    //     const progressElement = document.getElementById("progress");
    //     progressElement.innerHTML = "Processing started...";

    //     const eventSource = new EventSource("/start_processing");

    //     eventSource.onmessage = function (event) {
    //         const data = JSON.parse(event.data);
    //         const progress = data.progress;
    //         progressElement.innerHTML = `Progress: ${progress}%`;

    //         if (progress === 100) {
    //             eventSource.close();
    //             progressElement.innerHTML = "Processing complete!";
    //         }
    //     };

    //     eventSource.onerror = function () {
    //         eventSource.close();
    //         progressElement.innerHTML = "Error occurred during processing.";
    //     };
    // }


    function startUpload() {
        // Show the progress bar
        document.getElementById("progressContainer").classList.remove("hidden");

        // Submit the form
        document.getElementById("uploadForm").submit();
    }

    // Function to update the progress bar
    function updateProgressBar(progress) {
        const progressBar = document.getElementById("progressBar");
        progressBar.style.width = progress + "%";
        progressBar.innerText = progress + "%";
    }

    // Example usage: Call updateProgressBar(progress) from your backend code to update the progress bar
    // This is just a placeholder function, you need to implement it in your Django view
    // You can use AJAX to send the progress from the server to the client


    // document.addEventListener("DOMContentLoaded", () => {
    //     // Simulate an API request or any async operation
    //     setTimeout(() => {
    //         hideLoader();
    //         showContent();
    //     }, 3000); // Replace with your actual data loading logic and time

    //     function hideLoader() {
    //         const loader = document.getElementById("loader_div");
    //         loader.style.display = "none";
    //     }

    //     function showContent() {
    //         const content = document.getElementById("content");
    //         content.style.display = "block";
    //     }
    // });

//     function showLoaderOnClick(url) {
//       showLoader();
//       window.location=url;
//   }
// function showLoader(){
//       $('body').append('<div style="" id="loadingDiv"><div class="loader">Loading...</div></div>');
//   }
</script>

{% comment %} {{ block.super }} {% endcomment %}
{% endblock %}