"""TransitCMS URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.0/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from . import views
from django.contrib import admin
from django.conf import settings 
from django.conf.urls.static import static  
from django.contrib.auth import views as auth_views

from django.urls import path,include
from BusApp import views as B
from ImportApp import views as I
from MetroApp import views as M
from RailApp import views as R
from django.views.static import serve
from django.contrib.staticfiles.storage import staticfiles_storage 
from django.views.generic.base import RedirectView




admin.site.site_version = '0.0.3'
admin.site.site_header = "Universal Route Digitization"
admin.site.site_title = "Universal Route Digitization"
admin.site.index_title = "Welcome To Universal Route Digitization Portal"
admin.site.site_url = None

# admin.site.login = views.AdminLogin.as_view()

urlpatterns = [
    path('', views.index, name='index'),
    path('admin/', admin.site.urls),
    path('analysis/', views.analysis, name='analysis'),
    path('admin/logout/', auth_views.LogoutView.as_view(), name='logout'),
    path('admin/password_change/', auth_views.PasswordChangeView.as_view(), name='password_change'),
    path('admin/password_change_done/', auth_views.PasswordChangeDoneView.as_view(), name='password_change_done'),
    path('bus/',B.bus, name='bus'),
    path('bus/selectCity/<int:id>', B.selectCity, name="selectCity"),
    path('get_bus_route/selectCity/<int:id>', B.selectCity, name="selectCity"),
    path('metro/selectCity/<int:id>', M.selectCity, name="selectCity"),
    path('get_metro_route/selectCity/<int:id>', M.selectCity, name="selectCity"),
    path('rail/selectCity/<int:id>', R.selectCity, name="selectCity"),
    path('get_rail_route/selectCity/<int:id>', R.selectCity, name="selectCity"),
    path('metro/',M.metro, name='metro'),
    path('rail/',R.rail, name='rail'),
    # path('get_bus_route/<int:route_id>', B.get_bus_route, name='get_bus_route'),
    path('get_bus_route/', B.get_bus_route, name='get_bus_route'),
    path('get_metro_route/<int:metro_route_id>',M.get_metro_route, name='get_metro_route'),
    path('get_rail_route/<int:rail_route_id>',R.get_rail_route, name='get_rail_route'),
    path("select2/", include("django_select2.urls")),

    # File Processor App URLs
    path('fileprocessor/', include('FileProcessorApp.urls')),
]

# Serve media files during development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)


