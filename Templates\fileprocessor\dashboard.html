{% extends 'fileprocessor/base.html' %}

{% block page_title %}Dashboard{% endblock %}

{% block page_actions %}
    <a href="{% url 'fileprocessor:upload' %}" class="btn btn-primary">
        <i class="fas fa-upload"></i> Upload New File
    </a>
{% endblock %}

{% block content %}
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-white bg-primary">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ stats.total_files }}</h4>
                            <p class="card-text">Total Files</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-file fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-white bg-warning">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ stats.pending_files }}</h4>
                            <p class="card-text">Pending</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-white bg-success">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ stats.completed_files }}</h4>
                            <p class="card-text">Completed</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-white bg-info">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ stats.gtfs_datasets }}</h4>
                            <p class="card-text">GTFS Datasets</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-database fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filter -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-6">
                    <input type="text" class="form-control" name="search" placeholder="Search files..." value="{{ search_query }}">
                </div>
                <div class="col-md-3">
                    <select name="status" class="form-select">
                        <option value="">All Statuses</option>
                        {% for status_value, status_display in status_choices %}
                            <option value="{{ status_value }}" {% if status_filter == status_value %}selected{% endif %}>
                                {{ status_display }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-search"></i> Search
                    </button>
                    <a href="{% url 'fileprocessor:dashboard' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i> Clear
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Files Table -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">Uploaded Files</h5>
        </div>
        <div class="card-body">
            {% if page_obj %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Filename</th>
                                <th>Type</th>
                                <th>Size</th>
                                <th>Status</th>
                                <th>Upload Date</th>
                                <th>GTFS</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for file in page_obj %}
                                <tr>
                                    <td>
                                        <a href="{% url 'fileprocessor:file_detail' file.id %}">
                                            {{ file.original_filename }}
                                        </a>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ file.file_type|upper }}</span>
                                    </td>
                                    <td>{{ file.file_size|filesizeformat }}</td>
                                    <td>
                                        <span class="processing-status status-{{ file.processing_status }}">
                                            {{ file.get_processing_status_display }}
                                        </span>
                                    </td>
                                    <td>{{ file.upload_date|date:"M d, Y H:i" }}</td>
                                    <td>
                                        {% if file.gtfs_generated %}
                                            <i class="fas fa-check text-success"></i>
                                        {% else %}
                                            <i class="fas fa-times text-muted"></i>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{% url 'fileprocessor:file_detail' file.id %}" class="btn btn-outline-primary btn-sm">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            {% if file.processing_status == 'completed' and not file.gtfs_generated %}
                                                <a href="{% url 'fileprocessor:generate_gtfs' file.id %}" class="btn btn-outline-success btn-sm">
                                                    <i class="fas fa-cog"></i>
                                                </a>
                                            {% endif %}
                                            {% if file.processing_status in 'failed,completed' %}
                                                <a href="{% url 'fileprocessor:reprocess_file' file.id %}" class="btn btn-outline-warning btn-sm">
                                                    <i class="fas fa-redo"></i>
                                                </a>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if page_obj.has_other_pages %}
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">Previous</a>
                                </li>
                            {% endif %}
                            
                            {% for num in page_obj.paginator.page_range %}
                                {% if page_obj.number == num %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ num }}</span>
                                    </li>
                                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">{{ num }}</a>
                                    </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">Next</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                {% endif %}
            {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-file fa-3x text-muted mb-3"></i>
                    <h5>No files uploaded yet</h5>
                    <p class="text-muted">Upload your first file to get started with GTFS generation.</p>
                    <a href="{% url 'fileprocessor:upload' %}" class="btn btn-primary">
                        <i class="fas fa-upload"></i> Upload File
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
{% endblock %}

{% block extra_js %}
<script>
    // Auto-refresh processing status every 30 seconds
    setInterval(function() {
        const processingRows = document.querySelectorAll('.status-processing, .status-pending');
        if (processingRows.length > 0) {
            location.reload();
        }
    }, 30000);
</script>
{% endblock %}
