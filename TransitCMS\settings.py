"""
Django settings for TransitCMS project.

Generated by 'django-admin startproject' using Django 4.0.5.

For more information on this file, see
https://docs.djangoproject.com/en/4.0/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.0/ref/settings/
"""

from pathlib import Path
import os
from django.contrib.messages import constants as messages
import logging

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.0/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-_nb=^y)9v#x+#f@e#qe%7qd)r@w2a@pqyzk()e4s4iqsqokylq'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = False

ALLOWED_HOSTS = ['*']
# CSRF_TRUSTED_ORIGINS = ['https://transitcms.tummoc.in']
CSRF_TRUSTED_ORIGINS = ['https://urd-stage.tummoc.in']


# Application definition

INSTALLED_APPS = [
    'TransitCMS.apps.TransitCMSAdminConfig',  # replaces 'django.contrib.admin'
    # 'django.contrib.admin',
    'corsheaders',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django_admin_listfilter_dropdown',
    'CityApp',
    'BusApp',
    'MetroApp',
    'RailApp',
    'ImportApp',
    
]

MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django_session_timeout.middleware.SessionTimeoutMiddleware',
    'routers.router_middleware.RouterMiddleware',
    'whitenoise.middleware.WhiteNoiseMiddleware',

]

SESSION_EXPIRE_SECONDS = 250  # In seconds
SESSION_EXPIRE_AFTER_LAST_ACTIVITY = True
# SESSION_TIMEOUT_REDIRECT = 'https://transitcms.tummoc.in/admin/login/?next=/admin/' # redirection URL
SESSION_TIMEOUT_REDIRECT = 'https://urd-stage.tummoc.in'  # redirection URL

LOGIN_REDIRECT_URL = '/'  # URL redirecting after a successful authentication
ROOT_URLCONF = 'TransitCMS.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [os.path.join(BASE_DIR, 'Templates')],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'TransitCMS.wsgi.application'

# Database
# https://docs.djangoproject.com/en/4.0/ref/settings/#databases
DATABASE_ROUTERS = ['routers.db_routers.MasterRouter']
DATABASES = {


'default': {
        'ENGINE': 'django.db.backends.postgresql',
        # 'HOST': '*************',
        'HOST': '*************',
        'NAME': 'adapter_master',
        'USER': 'postgres',
        # 'PASSWORD': 'Post2023Gres_Tum',
        'PASSWORD': 'tummoC_20_20_bykerR',
    },
'master': {
        'ENGINE': 'django.db.backends.postgresql',
        'HOST': '*************',
        # 'HOST': '************',
        'NAME': 'adapter_bang',
        'USER': 'postgres',
        'PASSWORD': 'tummoC_20_20_bykerR',
        # 'PASSWORD': 'tummoC_20_20_bykerR',
    }


}

# Password validation
# https://docs.djangoproject.com/en/4.0/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Internationalization
# https://docs.djangoproject.com/en/4.0/topics/i18n/

LANGUAGE_CODE = 'en-us'

# TIME_ZONE = 'UTC'
TIME_ZONE = 'Asia/Kolkata'

USE_I18N = True

USE_TZ = False

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.0/howto/static-files/



# Default primary key field type
# https://docs.djangoproject.com/en/4.0/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

STATIC_URL = '/static/'
# Base url to serve media files  
MEDIA_URL = '/media/'  



if DEBUG: 
    STATICFILES_DIRS = [os.path.join(BASE_DIR, "static")]

else:
    STATIC_ROOT = os.path.join(BASE_DIR, "static")

# Path where media is stored  
MEDIA_ROOT = os.path.join(BASE_DIR, 'media') 

MESSAGE_TAGS = {
        messages.DEBUG: 'alert-secondary',
        messages.INFO: 'alert-info',
        messages.SUCCESS: 'alert-success',
        messages.WARNING: 'alert-warning',
        messages.ERROR: 'alert-danger',
 }