import django
from django.shortcuts import redirect
from django.shortcuts import render
from django.contrib.auth.views import LoginView    
from django.contrib.auth import login as auth_login
from django.contrib.auth.forms import AuthenticationForm
from django.http import HttpResponse, HttpResponseRedirect


def index(request):
    return redirect('/admin')

def analysis(request):
    return render(request, "analysis.html")

from django.urls import reverse

# LOGIN VIEW
class AdminLogin(LoginView):
    
    template_name: str  = 'LoginView_form.html'

    def form_valid(self, form):
        """Security check complete. Log the user in."""
        userId = form.get_user()
        auth_login(self.request, userId)
        return HttpResponseRedirect(reverse('analysis'))
    