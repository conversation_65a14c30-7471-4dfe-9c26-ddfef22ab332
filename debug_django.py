#!/usr/bin/env python
"""
Debug script to check Django startup issues
"""

import os
import sys
import traceback
from pathlib import Path

# Add the project directory to Python path
BASE_DIR = Path(__file__).resolve().parent
sys.path.append(str(BASE_DIR))

print("🔍 Django Startup Debug")
print("=" * 50)

try:
    print("1. Setting Django settings module...")
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'TransitCMS.settings')
    print("✅ Settings module set")
    
    print("\n2. Importing Django...")
    import django
    print(f"✅ Django imported successfully (version: {django.get_version()})")
    
    print("\n3. Setting up Django...")
    django.setup()
    print("✅ Django setup completed")
    
    print("\n4. Testing database connection...")
    from django.db import connection
    cursor = connection.cursor()
    cursor.execute("SELECT 1")
    result = cursor.fetchone()
    print(f"✅ Database connection working: {result}")
    
    print("\n5. Checking installed apps...")
    from django.conf import settings
    print(f"✅ Installed apps: {len(settings.INSTALLED_APPS)} apps")
    for app in settings.INSTALLED_APPS:
        print(f"   - {app}")
    
    print("\n6. Checking FileProcessorApp models...")
    from FileProcessorApp.models import UploadedFile, GTFSDataset, ExtractedTransitData
    print("✅ FileProcessorApp models imported successfully")
    
    print("\n7. Checking URL configuration...")
    from django.urls import reverse
    try:
        fileprocessor_url = reverse('fileprocessor:dashboard')
        print(f"✅ FileProcessorApp URLs working: {fileprocessor_url}")
    except Exception as e:
        print(f"⚠️  URL reverse failed: {e}")
    
    print("\n8. Testing Django management commands...")
    from django.core.management import execute_from_command_line
    print("✅ Management commands available")
    
    print("\n9. Checking for migration issues...")
    from django.core.management.commands.migrate import Command as MigrateCommand
    from django.db.migrations.executor import MigrationExecutor
    from django.db import DEFAULT_DB_ALIAS
    
    executor = MigrationExecutor(connection)
    plan = executor.migration_plan(executor.loader.graph.leaf_nodes())
    
    if plan:
        print(f"⚠️  Pending migrations found: {len(plan)} migrations")
        for migration, backwards in plan:
            print(f"   - {migration}")
    else:
        print("✅ No pending migrations")
    
    print("\n10. Testing server startup (dry run)...")
    from django.core.management.commands.runserver import Command as RunServerCommand
    print("✅ RunServer command available")
    
    print("\n" + "=" * 50)
    print("🎉 Django startup debug completed successfully!")
    print("\nTrying to start server manually...")
    
    # Try to start server
    from django.core.wsgi import get_wsgi_application
    application = get_wsgi_application()
    print("✅ WSGI application created successfully")
    
    print("\nStarting development server...")
    execute_from_command_line(['manage.py', 'runserver', '127.0.0.1:8000', '--verbosity=2'])
    
except Exception as e:
    print(f"\n❌ Error during Django startup: {e}")
    print("\nFull traceback:")
    traceback.print_exc()
    
    print("\n🔧 Troubleshooting suggestions:")
    print("1. Check database connection settings")
    print("2. Run migrations: python manage.py migrate")
    print("3. Check for syntax errors in models/views")
    print("4. Verify all dependencies are installed")
    print("5. Check file permissions")
