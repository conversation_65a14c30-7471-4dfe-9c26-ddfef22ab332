# from unicodedata import name
# from django.contrib import admin
# from django.urls import path
# from django import forms
# from django.contrib import messages
# from django.http import HttpResponse, HttpResponseRedirect
# from django.urls import reverse
# from django.shortcuts import render, redirect
# from django.db import connections
# import pandas as pd
# import openpyxl
# # from ImportApp.models import Imports
# # import dt
# from concurrent.futures import ThreadPoolExecutor, as_completed

# from ImportApp.service import bus_master_service, metro_master_service, rail_master_service
# from django import conf
# from CityApp.models import Cities
# from BusApp.models import *
# from MetroApp.models import *
# from RailApp.models import *
# # from ImportApp.models import Imports
# import csv
# from django.db import transaction
# from django.forms import ClearableFileInput
# import psutil
# from django.db.models import Q
# import os
# from tqdm import tqdm
# from time import sleep
# import threading
# from django.core.exceptions import ObjectDoesNotExist
# import logging
# import signal
# from django.contrib.auth.decorators import login_required, permission_required
# import zipfile
# import pandas as pd

# def handle_sigpipe(signum, frame):
#     pass

# signal.signal(signal.SIGFPE, handle_sigpipe)




# @login_required
# class ImportForm(forms.ModelForm):
#     class Meta():
#         model = Imports
#         fields = "__all__"
#         widgets = {
#             'file_path': ClearableFileInput(),
#         }


# # Register your models here.
# class ImportsAdmin(admin.ModelAdmin):
#     list_display = ('id', 'module', 'module_type', 'service', 'file_path', 'city_name', 'user_agent', 'user_id', 
#     'created_at'
#     )
#     readonly_fields = ('id', 
#     'created_at'
#     )
#     # readonly_fields = ('id', 'module', 'module_type','service', 'file_path','city_name','user_agent','user_id')
#     # search_fields = ('module', 'module_type','city_name','user_agent')
#     actions = ['delete_selected']
#     ordering = ('id',)
#     list_per_page = 10

#     def get_urls(self):
#         urls = super().get_urls()
#         new_uls = [path('imports/', self.add_view),
#                     path('selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),
#                     path('add/selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"), ]
        
#         return new_uls  + urls

    
#     def selectCity(self, request, id: int):
#         city = Cities.objects.filter(id=id).first()
#         db_name = city.db_name
#         city_name = city.name
#         request.session["db_name"] = db_name
#         request.session["city_name"] = city_name
#         conf.settings.DATABASES['master']['NAME'] = db_name
#         return redirect ('/admin/')


#     def select_city(self, obj):
#         return format_html('<option value="{{ city.id }}">',
#             reverse_lazy("admin:selectCity", args=[obj.id])
#         )

#     @transaction.atomic
#     def add_view(self, request):
#         user = request.session['username']
#         upload_file = []
#         city = Cities.objects.all()
#         city1 = Cities.objects.all()
#         if request.method == 'POST':
#             module = request.POST["module"]
#             moduleType = request.POST["module_type"]
#             service = request.POST["service"]
#             cities = request.POST["city_name"]
#             upload_file = request.FILES["file_path"]
#             form = ImportForm(request.POST, request.FILES)
#             print("cities: ", cities)
#             city2 = Cities.objects.filter(name=cities).first()
#             db_name2 = city2.db_name
#             print("db_name2 ", db_name2)
#             # conf.settings.DATABASES['master']['NAME'] = db_name2

#             try:
#                 form.is_valid()
#                 header = request.META['HTTP_USER_AGENT']
#                 print("header:", header)
#                 if module == "BUS" and upload_file.name.endswith(('.csv', '.txt')):
#                     print('module', module)
#                     if moduleType == "BusStop":
#                         df = pd.read_csv(upload_file)
#                         print("BusStop module: ", module, "moduleType: ", moduleType, "service: ", service,
#                                 "FileName ", upload_file)
#                         expected_columns = ['bus_stop_id',	'bus_stop_name','bus_stop_code', 'stop_direction','status','landmark','latitude_current','longitude_current','fare_stage','sub_stage','description','bmtc_status','route_status','stop_type_id','stop_group_id','toll_zone','toll_fee']
#                         # expected_columns = ['bus_stop_id','bus_stop_name','bus_stop_code','status','landmark','latitude_current','longitude_current','fare_stage','sub_stage','description','bmtc_status','route_status','stop_type_id','stop_group_id','toll_zone']
#                         if set(df.columns) == set(expected_columns):
                            
#                             # Delete existing BusStop records
#                             bus = BusStop.objects.all().delete()
#                             chunk_size = 10000
#                             num_count = psutil.cpu_count(logical=True)

#                             def chunk_generator():
#                                 for i in range(0, len(df), chunk_size):
#                                     yield df.iloc[i:i + chunk_size]

#                             def process_chunk(chunk):
#                                 instances = []
#                                 with transaction.atomic():
#                                     for i, row in chunk.iterrows():
#                                         try:
#                                             instance = BusStop(
#                                                 bus_stop_id=row['bus_stop_id'],
#                                                 bus_stop_name=row['bus_stop_name'],
#                                                 bus_stop_code=row['bus_stop_code'],
#                                                 # stop_direction=row['stop_direction'],
#                                                 status=row['status'],
#                                                 landmark=row['landmark'],
#                                                 latitude_current=row['latitude_current'],
#                                                 longitude_current=row['longitude_current'],
#                                                 fare_stage=row['fare_stage'],
#                                                 sub_stage=row['sub_stage'],
#                                                 description=row['description'],
#                                                 bmtc_status=row['bmtc_status'],
#                                                 route_status=row['route_status'],
#                                                 stop_type_id=row['stop_type_id'],
#                                                 stop_group_id=row['stop_group_id'],
#                                                 toll_zone=row['toll_zone'],
#                                                 # toll_fee = row['toll_fee']
#                                             )
#                                             instances.append(instance)
#                                         except Exception as e:
#                                             print(e)
#                                 BusStop.objects.bulk_create(instances, batch_size=10000, ignore_conflicts=True)

#                             try:
#                                 with ThreadPoolExecutor(max_workers=num_count) as executor:
#                                     futures = [executor.submit(process_chunk, chunk) for chunk in chunk_generator()]
#                                     for _ in tqdm(as_completed(futures), total=len(futures), desc="processing rows"):
#                                         running_threads = threading.active_count()
#                                         print(f"Number of running threads: {running_threads}")
#                                         sleep(0.1)
#                                 # print("hi")
#                                 # form.save()
#                                 msg1 = len(BusStop.objects.all())
#                                 # print(msg1)
#                                 data = {"site_title": admin.site.site_title, "site_version": admin.site.site_version,
#                                         "cities": city, "msg": "uploaded", "msg1":f'{msg1}rows','city1':city1}
#                                 return render(request, "admin/ImportApp/imports/change_form.html", data)
#                             except Exception as e:
#                                 print(e)
#                                 data = {"site_title": admin.site.site_title, "site_version": admin.site.site_version,
#                                         "cities": city, "msg": e,'city1':city1}
#                                 return render(request, "admin/ImportApp/imports/change_form.html", data)

#                         else:
#                             print("Columns in the CSV (or) TXT file do not match the expected columns.",expected_columns)
#                             e=f"uploaded file is not {moduleType}.csv file please check and upload."
#                             data = {"site_title": admin.site.site_title, "site_version": admin.site.site_version,
#                                     "cities": city, "msg": e,'city1':city1}
#                             return render(request, "admin/ImportApp/imports/change_form.html", data)

#                     elif moduleType == "BusRoute":
#                         # master_service.BusRoute(upload_file)
#                         df = pd.read_csv(upload_file)
#                         expected_columns = ['route_id','route_number','start_point_id','end_point_id','route_type_id','route_name','status','via','description','deleted_status','route_direction','effective_from','effective_till','route_string','bus_service_type_id','route_group','no_of_schedule', 'route_alias']
#                         if set(df.columns) == set(expected_columns):
#                             print("bus_route module: ", module, "moduleType: ", moduleType, "service: ", service,
#                                 "FileName ", upload_file)
#                             bus = BusRoute.objects.all()
#                             bus.delete()
#                             # print(bus)
#                             chunk_size = 10000
#                             num_count = psutil.cpu_count(logical=True)

#                             def chunk_generator():
#                                 for i in range(0, len(df), chunk_size):
#                                     yield df.iloc[i:i + chunk_size]

#                             def get_or_none(model, **kwargs):
#                                 try:
#                                     return model.objects.get(**kwargs)
#                                 except model.DoesNotExist:
#                                     return None

#                             def process_chunk(chunk):
#                                 instances = []
#                                 # print(instances)
#                                 with transaction.atomic():
#                                     for i, row in chunk.iterrows():
#                                         route_id=int(row['route_id'])
#                                         route_number = row['route_number']
#                                         start_point_id = row ['start_point_id']
#                                         end_point_id = row ['end_point_id']
#                                         route_type_id = int(row['route_type_id'])
#                                         route_name = str(row['route_name'])
#                                         status = row['status']
#                                         via = row['via']
#                                         description = row['description']
#                                         deleted_status = row['deleted_status']
#                                         route_direction = row['route_direction']
#                                         effective_from = row['effective_from']
#                                         effective_till = row['effective_till']
#                                         route_string = row['route_string']
#                                         bus_service_type_id = row['bus_service_type_id']
#                                         route_group = row['route_group']
#                                         no_of_schedule = row['no_of_schedule']
#                                         route_alias = row['route_alias']
#                                         try:
#                                             start_point_id = get_or_none(BusStop,bus_stop_id=start_point_id)
#                                             end_point_id = get_or_none(BusStop,bus_stop_id=end_point_id)
#                                             bus_service_type_id =get_or_none(BusServiceType,service_type_id=bus_service_type_id)
#                                             # start_point_id_instance = BusStop.objects.get(bus_stop_id=start_point_id)
#                                             # end_point_id_instance = BusStop.objects.get(bus_stop_id=end_point_id)
#                                             # bus_service_type_id_instance =BusServiceType.objects.get(service_type_id=bus_service_type_id)
#                                             instance = BusRoute(
#                                                 route_id=int(row['route_id']),
#                                                 route_number = row['route_number'],
#                                                 start_point_id = start_point_id,
#                                                 end_point_id = end_point_id,
#                                                 route_type_id = int(row['route_type_id']),
#                                                 route_name = str(row['route_name']),
#                                                 status = row['status'],
#                                                 via = row['via'],
#                                                 description = row['description'],
#                                                 deleted_status = row['deleted_status'],
#                                                 route_direction = row['route_direction'],
#                                                 effective_from = row['effective_from'],
#                                                 effective_till = row['effective_till'],
#                                                 route_string = row['route_string'],
#                                                 bus_service_type_id = bus_service_type_id,
#                                                 route_group = row['route_group'],
#                                                 no_of_schedule = row['no_of_schedule'],
#                                                 route_alias = row['route_alias']
#                                                 )

#                                             instances.append(instance)
#                                             print(instances)
#                                         except Exception as e:
#                                             print(e)
#                                     BusRoute.objects.bulk_create(instances,batch_size=10000, ignore_conflicts=True)
#                             try:
#                                 with ThreadPoolExecutor(max_workers=num_count) as executor:
#                                     # for chunk in chunk_generator():
#                                     #     executor.submit(process_chunk, chunk)
#                                     futures = [executor.submit(process_chunk, chunk) for chunk in chunk_generator()]
#                                     executor.shutdown(wait=True)
#                                     for _ in tqdm(as_completed(futures), total=len(futures), desc="processing rows"):
#                                         sleep(0.1)
#                                 form.save()
#                                 msg1 = len(BusRoute.objects.all())
#                                 data = {"site_title": admin.site.site_title, "site_version": admin.site.site_version,
#                                         "cities": city, "msg": "uploaded", "msg1":f'{msg1}rows','city1':city1}
#                                 return render(request, "admin/ImportApp/imports/change_form.html", data)
#                             except Exception as e:
#                                 print(e)
#                                 msg="BusStop(or)BusServiceType Data Is DoesNotExist"
#                                 data = {"site_title": admin.site.site_title, "site_version": admin.site.site_version,
#                                         "cities": city, "msg": e,'city1':city1}
#                                 return render(request, "admin/ImportApp/imports/change_form.html", data)
#                         else:
#                             print("Columns in the CSV (or) TXT file do not match the expected columns.",expected_columns)
#                             e=f"uploaded file is not {moduleType}.csv file please check and upload."
#                             data = {"site_title": admin.site.site_title, "site_version": admin.site.site_version,
#                                     "cities": city, "msg": e,'city1':city1}
#                             return render(request, "admin/ImportApp/imports/change_form.html", data)

#                     elif moduleType == "BusRoutePoint":
#                         # master_service.BusRoutePoint(upload_file)
#                         df = pd.read_csv(upload_file)
#                         expected_columns = ['route_points_id','route_id','bus_stop_id','route_order','point_status','fare_stage','sub_stage','deleted_status','travel_distance','travel_time']
#                         if set(df.columns) == set(expected_columns):
#                             print("bus_route_point module: ", module, "moduleType: ", moduleType, "service: ", service,
#                                 "FileName ", upload_file)
#                             instances = []
#                             bus = BusRoutePoint.objects.all()
#                             bus.delete()
#                             chunk_size = 10000
#                             num_count = psutil.cpu_count(logical=True)
#                             def chunk_generator():
#                                 for i in range(0, len(df), chunk_size):
#                                     yield df.iloc[i:i + chunk_size]
#                             def process_chunk(chunk):
#                                 instances = []
#                                 with transaction.atomic():
#                                     for i, row in chunk.iterrows():
#                                         route_points_id=row['route_points_id']
#                                         route_id = row['route_id']
#                                         bus_stop_id = int(row['bus_stop_id'])
#                                         route_order=row['route_order']
#                                         point_status=row['point_status']
#                                         fare_stage=row['fare_stage']
#                                         sub_stage=row['sub_stage']
#                                         deleted_status=row['deleted_status']
#                                         travel_distance=row['travel_distance']
#                                         travel_time=row['travel_time']
#                                         try:
#                                             route_id_instance = BusRoute.objects.get(route_id=route_id)
#                                             bus_stop_id_instance = BusStop.objects.get(bus_stop_id=bus_stop_id)
#                                             busroutepoint_instance = BusRoutePoint(
#                                                 route_points_id=row['route_points_id'],
#                                                 route_id=route_id_instance,
#                                                 bus_stop_id=bus_stop_id_instance,
#                                                 route_order=row['route_order'],
#                                                 point_status=row['point_status'],
#                                                 fare_stage=row['fare_stage'],
#                                                 sub_stage=row['sub_stage'],
#                                                 deleted_status=row['deleted_status'],
#                                                 travel_distance=row['travel_distance'],
#                                                 travel_time=row['travel_time']
#                                             )
#                                             instances.append(busroutepoint_instance)
#                                         except Exception as e:
#                                             print(e)
#                                     BusRoutePoint.objects.bulk_create(instances,batch_size=10000, ignore_conflicts=True)
#                             try:
#                                 with ThreadPoolExecutor(max_workers=num_count) as executor:
#                                     futures = [executor.submit(process_chunk, chunk) for chunk in chunk_generator()]
#                                     for _ in tqdm(as_completed(futures), total=len(futures), desc="Processing Rows"):
#                                         sleep(0.1)
#                                 form.save()
#                                 msg1 = len(BusRoutePoint.objects.all())
#                                 data = {"site_title": admin.site.site_title, "site_version": admin.site.site_version,
#                                         "cities": city, "msg": "uploaded", "msg1":f'{msg1}rows','city1':city1}
#                                 return render(request, "admin/ImportApp/imports/change_form.html", data)
#                             except Exception as e:
#                                 print(e)
#                                 data = {"site_title": admin.site.site_title, "site_version": admin.site.site_version,
#                                         "cities": city, "msg": e,'city1':city1}
#                                 return render(request, "admin/ImportApp/imports/change_form.html", data)
                        
#                         else:
#                             print("Columns in the CSV (or) TXT file do not match the expected columns.",expected_columns)
#                             e="uploaded file is not bus_route_point.csv file please check and upload."
#                             data = {"site_title": admin.site.site_title, "site_version": admin.site.site_version,
#                                     "cities": city, "msg": e,'city1':city1}
#                             return render(request, "admin/ImportApp/imports/change_form.html", data)

#                     elif moduleType == "BusScheduleDetails" and upload_file.name.endswith(('.csv', '.txt')):
#                         # master_service.BusSchedule(upload_file)
#                         df = pd.read_csv(upload_file)
#                         # expected_columns = ['schedule_details_id','schedule_number','route_number_id','trip_number','distance','start_time','end_time','running_time','shift_type_name','org_name','day_type','route_vehicle_map_id']
#                         expected_columns = ['schedule_details_id','schedule_number','route_number_id','trip_number','distance','start_time','end_time','running_time','shift_type_name','org_name','day_type']
#                         if set(df.columns) == set(expected_columns):
#                             bus = BusScheduleDetails.objects.all()
#                             bus.delete()
#                             chunk_size = 10000
#                             num_count = psutil.cpu_count(logical=True)

#                             def chunk_generator():
#                                 for i in range(0, len(df), chunk_size):
#                                     yield df.iloc[i:i + chunk_size]

#                             def process_chunk(chunk):
#                                 instances = []
#                                 with transaction.atomic():
#                                     for i, row in chunk.iterrows():
#                                         schedule_details_id=int(row['schedule_details_id'])
#                                         schedule_number = row['schedule_number']
#                                         route_number_id = int(row['route_number_id'])
#                                         trip_number = int(row['trip_number'])
#                                         distance = row['distance']
#                                         start_time = str(row['start_time'])
#                                         end_time = str(row['end_time'])
#                                         running_time = str(row['running_time'])
#                                         shift_type_name = row['shift_type_name']
#                                         org_name = row['org_name']
#                                         day_type = row['day_type']
#                                         # route_vehicle_map_id = int(row['route_vehicle_map_id'])
#                                         try:
#                                             route_number_id_instance = BusRoute.objects.get(route_id=route_number_id)
#                                             BusScheduleDetails_instance = BusScheduleDetails(
#                                             schedule_details_id=int(row['schedule_details_id']),
#                                             schedule_number = row['schedule_number'],
#                                             route_number_id = route_number_id_instance,
#                                             trip_number = int(row['trip_number']),
#                                             distance = row['distance'],
#                                             start_time = str(row['start_time']),
#                                             end_time = str(row['end_time']),
#                                             running_time = str(row['running_time']),
#                                             shift_type_name = row['shift_type_name'],
#                                             org_name = row['org_name'],
#                                             day_type = row['day_type'],
#                                             # route_vehicle_map_id = int(row['route_vehicle_map_id'])
#                                             )
#                                             # print(BusScheduleDetails_instance)
#                                             instances.append(BusScheduleDetails_instance)
#                                         except Exception as e:
#                                             print(e)
#                                     BusScheduleDetails.objects.bulk_create(instances,batch_size=10000, ignore_conflicts=True)
#                             try:
#                                 with ThreadPoolExecutor(max_workers=num_count) as executor:
#                                     futures = [executor.submit(process_chunk, chunk) for chunk in chunk_generator()]
#                                     for _ in tqdm(as_completed(futures), total=len(futures), desc="Processing Rows"):
#                                         sleep(0.1)
#                                 print("hi")
#                                 # form.save()
#                                 msg1 = len(BusScheduleDetails.objects.all())
#                                 data = {"site_title": admin.site.site_title, "site_version": admin.site.site_version,
#                                         "cities": city, "msg": "uploaded", "msg1":f'{msg1}rows','city1':city1}
#                                 return render(request, "admin/ImportApp/imports/change_form.html", data)
#                             except Exception as e:
#                                 print(e)
#                                 data = {"site_title": admin.site.site_title, "site_version": admin.site.site_version,
#                                         "cities": city, "msg": e,'city1':city1}
#                                 return render(request, "admin/ImportApp/imports/change_form.html", data)
#                         else:
#                             print("Columns in the CSV (or) TXT file do not match the expected columns.",expected_columns)
#                             e="uploaded file is not bus_schedule.csv file please check and upload."
#                             data = {"site_title": admin.site.site_title, "site_version": admin.site.site_version,
#                                     "cities": city, "msg": e,'city1':city1}
#                             return render(request, "admin/ImportApp/imports/change_form.html", data)
                            
#                     elif moduleType == "BusServiceType":
#                         df = pd.read_csv(upload_file)
#                         print("bus_service_type module: ", module, "moduleType: ", moduleType, "service: ", service,"FileName ", upload_file)
#                         expected_columns = ['service_type_id','service_type_name','abbreviation','status','deleted_status','updated_by','updated_date','service_type_code','sync_updated_date']
#                         if set(df.columns) == set(expected_columns):
#                             bus = BusServiceType.objects.all()
#                             bus.delete()
#                             chunk_size = 10000
#                             num_count = psutil.cpu_count(logical=True)

#                             def chunk_generator():
#                                 for i in range(0, len(df), chunk_size):
#                                     yield df.iloc[i:i + chunk_size]

#                             def process_chunk(chunk):
#                                 instances = []
#                                 with transaction.atomic():
#                                     for i, row in chunk.iterrows():
#                                         try:
#                                             instance = BusServiceType(
#                                                 service_type_id=row['service_type_id'],
#                                                 service_type_name=row['service_type_name'],
#                                                 abbreviation=row['abbreviation'],
#                                                 status=row['status'],
#                                                 deleted_status=row['deleted_status'],
#                                                 updated_by=row['updated_by'],
#                                                 updated_date=row['updated_date'],
#                                                 service_type_code=row['service_type_code'],
#                                                 sync_updated_date=row['sync_updated_date']
#                                             )
#                                             instances.append(instance)
#                                         except BusServiceType.DoesNotExist:
#                                             pass
#                                     BusServiceType.objects.bulk_create(instances,batch_size=10000, ignore_conflicts=True)
#                             try:
#                                 with ThreadPoolExecutor(max_workers=num_count) as executor:
#                                     futures = [executor.submit(process_chunk, chunk) for chunk in chunk_generator()]
#                                     for _ in tqdm(as_completed(futures), total=len(futures), desc="processing rows"):
#                                         sleep(0.1)
#                                 form.save()
#                                 msg1 = len(BusServiceType.objects.all())
#                                 data = {"site_title": admin.site.site_title, "site_version": admin.site.site_version,
#                                         "cities": city, "msg": "uploaded", "msg1":f'{msg1}rows','city1':city1}
#                                 return render(request, "admin/ImportApp/imports/change_form.html", data)
#                             except Exception as e:
#                                 print(e)
#                                 data = {"site_title": admin.site.site_title, "site_version": admin.site.site_version,
#                                         "cities": city, "msg": e,'city1':city1}
#                                 return render(request, "admin/ImportApp/imports/change_form.html", data)
#                         else:
#                             print("Columns in the CSV (or) TXT file do not match the expected columns.",expected_columns)
#                             e="uploaded file is not bus_service_type.csv please check and upload !!!."
#                             data = {"site_title": admin.site.site_title, "site_version": admin.site.site_version,
#                                     "cities": city, "msg": e,'city1':city1}
#                             return render(request, "admin/ImportApp/imports/change_form.html", data)

#                     elif moduleType == "BusFareVersion1":
#                         df = pd.read_csv(upload_file)
#                         print("bus_fare module: ", module, "moduleType: ", moduleType, "service: ", service,"FileName ", upload_file)
#                         expected_columns = ['fare_chart_id','route_id','start_point_id','end_point_id','number_of_kms','fare_amount','toll_fee','status']
#                         if set(df.columns) == set(expected_columns):
#                             # backup_queryset = BusFare.objects.all()  # Or filter for specific rows
#                             # backup_data = list(backup_queryset.values())
#                             bus = BusFare.objects.all().delete()
#                             chunk_size = 10000
#                             num_count = psutil.cpu_count(logical=True)

#                             def chunk_generator():
#                                 for i in range(0, len(df), chunk_size):
#                                     yield df.iloc[i:i + chunk_size]

#                             def process_chunk(chunk):
#                                 instances = []
#                                 with transaction.atomic():
#                                     for i, row in chunk.iterrows():
#                                         route_id = row['route_id']
#                                         start_point_id=row['start_point_id']
#                                         end_point_id=row['end_point_id']

#                                         try:
#                                             # route_id_instance = BusRoute.objects.get(route_id=route_id)
#                                             # # print(route_id_instance)
#                                             # start_point_instance = BusStop.objects.get(bus_stop_id=start_point_id)
#                                             # # print(start_point_instance)
#                                             # end_point_instance = BusStop.objects.get(bus_stop_id=end_point_id)
#                                             # print(end_point_instance)
#                                             instance = BusFare(
#                                                 fare_chart_id=row['fare_chart_id'],
#                                                 route_id = row['route_id'],
#                                                 # route_id = route_id_instance,
#                                                 start_point_id=row['start_point_id'],
#                                                 # start_point_id=start_point_instance,
#                                                 end_point_id=row['end_point_id'],
#                                                 # end_point_id=end_point_instance,
#                                                 number_of_kms=row['number_of_kms'],
#                                                 fare_amount=row['fare_amount'],
#                                                 toll_fee=row['toll_fee'],
#                                                 status=row['status']
#                                             )
#                                             instances.append(instance)
#                                         except Exception as e:
#                                             # # If error occurs, restore from backup
#                                             # for backup_object in BusFareBackup.objects.all():
#                                             #     BusFare.objects.create(**backup_object.__dict__)
#                                             raise e
#                                     BusFare.objects.bulk_create(instances,batch_size=10000, ignore_conflicts=True)
#                             try:
#                                 with ThreadPoolExecutor(max_workers=num_count) as executor:
#                                     futures = [executor.submit(process_chunk, chunk) for chunk in chunk_generator()]
#                                     for _ in tqdm(as_completed(futures), total=len(futures), desc="processing rows"):
#                                         sleep(0.1)
#                                 # print("hi")
#                                 # form.save()
#                                 msg1 = len(BusFare.objects.all())
#                                 data = {"site_title": admin.site.site_title, "site_version": admin.site.site_version,
#                                         "cities": city, "msg": "uploaded", "msg1":f'{msg1}rows','city1':city1}
#                                 return render(request, "admin/ImportApp/imports/change_form.html", data)
#                             except Exception as e:
#                                 # If error occurs, restore from backup
#                                 # for backup_object in BusFareBackup.objects.all():
#                                 #     BusFare.objects.create(**backup_object.__dict__)
#                                 raise e
#                                 data = {"site_title": admin.site.site_title, "site_version": admin.site.site_version,
#                                         "cities": city, "msg": e,'city1':city1}
#                                 return render(request, "admin/ImportApp/imports/change_form.html", data)
#                         else:
#                             print("Columns in the CSV (or) TXT file do not match the expected columns.",expected_columns)
#                             e="uploaded file is not bus_fare.csv  please check and upload!!!."
#                             data = {"site_title": admin.site.site_title, "site_version": admin.site.site_version,
#                                     "cities": city, "msg": e,'city1':city1}
#                             return render(request, "admin/ImportApp/imports/change_form.html", data)

#                     elif moduleType == "BusFareVersion2" and service == 'FareChartMaster':
#                         print('module:', module)
#                         df = pd.read_csv(upload_file)
#                         print("BusFare module: ", module, "moduleType: ", moduleType, "service: ", service,"FileName ", upload_file)
#                         expected_columns = ['farechart_master_id','route_id','service_type_id','passenger_type_id','rate_master_id','route_fare_map_id','farechart_name','schedule_service','percentage','deleted_status','ceiling_fare','nignt_service','flexi_fare']
#                         if set(df.columns) == set(expected_columns):
#                             fares = FareChartMaster.objects.all()
#                             fares.delete()
#                             chunk_size = 10000  # Adjust this to your preferred chunk size
#                             num_count = psutil.cpu_count(logical=True)

#                             def chunk_generator():
#                                 for i in range(0, len(df), chunk_size):
#                                     yield df.iloc[i:i + chunk_size]

#                             def process_chunk(chunk):
#                                 instances = []
#                                 # Use transaction.atomic() to wrap the bulk_create operation in an atomic transaction
#                                 with transaction.atomic():
#                                     for i, row in chunk.iterrows():
#                                         try:
#                                             instance =FareChartMaster(
#                                                 farechart_master_id=row['farechart_master_id'],
#                                                 route_id=row['route_id'],
#                                                 service_type_id=row['service_type_id'],
#                                                 passenger_type_id=row['passenger_type_id'],
#                                                 rate_master_id=row['rate_master_id'],
#                                                 route_fare_map_id=row['route_fare_map_id'],
#                                                 farechart_name=row['farechart_name'],
#                                                 schedule_service=row['schedule_service'],
#                                                 percentage=row['percentage'],
#                                                 deleted_status=row['deleted_status'],
#                                                 ceiling_fare=row['ceiling_fare'],
#                                                 nignt_service=row['nignt_service'],
#                                                 flexi_fare=row['flexi_fare']
#                                             )
#                                             instances.append(instance)
#                                         except Exception as e:
#                                             print(e)
#                                             # print(BusRoute.DoesNotExist)
#                                     FareChartMaster.objects.bulk_create(instances,batch_size=10000, ignore_conflicts=True)
#                             try:
#                                 with ThreadPoolExecutor(max_workers=num_count) as executor:
#                                     futures = [executor.submit(process_chunk, chunk) for chunk in chunk_generator()]
#                                     for _ in tqdm(as_completed(futures), total=len(futures), desc="processing rows"):
#                                         sleep(0.1)
#                                 print("hi")
#                                 form.save()
#                                 msg1 = len(FareChartMaster.objects.all())
#                                 data = {"site_title": admin.site.site_title, "site_version": admin.site.site_version,
#                                         "cities": city, "msg": "uploaded", "msg1":f'{msg1}rows','city1':city1}
#                                 return render(request, "admin/ImportApp/imports/change_form.html", data)
#                             except Exception as e:
#                                 print(e)
#                                 data = {"site_title": admin.site.site_title, "site_version": admin.site.site_version,
#                                         "cities": city, "msg": e,'city1':city1}
#                                 return render(request, "admin/ImportApp/imports/change_form.html", data)
#                         else:
#                             print("Columns in the CSV (or) TXT file do not match the expected columns.",expected_columns)
#                             e=f"uploaded file is not {moduleType}.csv file please check and upload."
#                             data = {"site_title": admin.site.site_title, "site_version": admin.site.site_version,
#                                     "cities": city, "msg": e,'city1':city1}
#                             return render(request, "admin/ImportApp/imports/change_form.html", data)

#                     elif moduleType == "BusFareVersion2" and service == 'RateMasterDetails':
#                         df = pd.read_csv(upload_file)
#                         expected_columns = ['rate_master_details_id','rate_master_id','service_type_id','stage_no','adult','children','senior_citizen','luggage','happy_hour1','happy_hour2','deleted_status','created_by','created_date','updated_by','updated_date','sync_updated_date']
#                         if set(df.columns) == set(expected_columns):
#                             bus = RateMasterDetails.objects.all()
#                             bus.delete()
#                             chunk_size = 10000  # Adjust this to your preferred chunk size
#                             num_count = psutil.cpu_count(logical=True)

#                             def chunk_generator():
#                                 for i in range(0, len(df), chunk_size):
#                                     yield df.iloc[i:i + chunk_size]

#                             def process_chunk(chunk):
#                                 instances = []
#                                 # Use transaction.atomic() to wrap the bulk_create operation in an atomic transaction
#                                 with transaction.atomic():
#                                     for i, row in chunk.iterrows():
#                                         try:
#                                             instance = RateMasterDetails(
#                                             rate_master_details_id=row['rate_master_details_id'],
#                                             rate_master_id=row['rate_master_id'],
#                                             service_type_id=row['service_type_id'],
#                                             stage_no=row['stage_no'],
#                                             adult=row['adult'],
#                                             children=row['children'],
#                                             senior_citizen=row['senior_citizen'],
#                                             luggage=row['luggage'],
#                                             happy_hour1=row['happy_hour1'],
#                                             happy_hour2=row['happy_hour2'],
#                                             deleted_status=row['deleted_status'],
#                                             created_by=row['created_by'],
#                                             created_date=str(row['created_date']),
#                                             updated_by=row['updated_by'],
#                                             updated_date=row['updated_date'],
#                                             sync_updated_date=row['sync_updated_date']
#                                             )
#                                             instances.append(instance)
#                                         except Exception as e:
#                                             print(e)
#                                     RateMasterDetails.objects.bulk_create(instances,batch_size=10000, ignore_conflicts=True)
#                             try:
#                                 with ThreadPoolExecutor(max_workers=num_count) as executor:
#                                     futures = [executor.submit(process_chunk, chunk) for chunk in chunk_generator()]
#                                     for _ in tqdm(as_completed(futures), total=len(futures), desc="processing rows"):
#                                         sleep(0.1)
#                                 form.save()
#                                 print()
#                                 msg1 = len(RateMasterDetails.objects.all())
#                                 data = {"site_title": admin.site.site_title, "site_version": admin.site.site_version,
#                                         "cities": city, "msg": "uploaded", "msg1":f'{msg1}rows','city1':city1}
#                                 return render(request, "admin/ImportApp/imports/change_form.html", data)
#                             except Exception as e:
#                                 print(e)
#                                 data = {"site_title": admin.site.site_title, "site_version": admin.site.site_version,
#                                         "cities": city, "msg": e,'city1':city1}
#                                 return render(request, "admin/ImportApp/imports/change_form.html", data)
#                         else:
#                             print("Columns in the CSV (or) TXT file do not match the expected columns.",expected_columns)
#                             e=f"uploaded file is not Rate_Master_Details.csv . please check and upload!!!."
#                             data = {"site_title": admin.site.site_title, "site_version": admin.site.site_version,
#                                     "cities": city, "msg": e,'city1':city1}
#                             return render(request, "admin/ImportApp/imports/change_form.html", data)

#                     elif moduleType == "BusFareVersion2" and service == 'RateMasterTable':
#                         df = pd.read_csv(upload_file)
#                         expected_columns = ['rate_master_id','parent_rate_master','version_number','version_number_service_stype','service_type_id','effective_start_date','effective_end_date','status','deleted_status','created_by','created_date','updated_by','updated_date','sync_updated_date']
#                         if set(df.columns) == set(expected_columns):
#                             bus = RateMasterTable.objects.all()
#                             bus.delete()
#                             chunk_size = 10000  # Adjust this to your preferred chunk size
#                             num_count = psutil.cpu_count(logical=True)

#                             def chunk_generator():
#                                 for i in range(0, len(df), chunk_size):
#                                     yield df.iloc[i:i + chunk_size]

#                             def process_chunk(chunk):
#                                 instances = []
#                                 # Use transaction.atomic() to wrap the bulk_create operation in an atomic transaction
#                                 with transaction.atomic():
#                                     for i, row in chunk.iterrows():
#                                         try:
#                                             instance = RateMasterTable(
#                                             rate_master_id=row['rate_master_id'],
#                                             parent_rate_master=row['parent_rate_master'],
#                                             version_number=row['version_number'],
#                                             version_number_service_stype=str(row['version_number_service_stype']),
#                                             service_type_id=row['service_type_id'],
#                                             effective_start_date=row['effective_start_date'],
#                                             effective_end_date=row['effective_end_date'],
#                                             status=row['status'],
#                                             deleted_status=row['deleted_status'],
#                                             created_by=row['created_by'],
#                                             created_date=row['created_date'],
#                                             updated_by=row['updated_by'],
#                                             updated_date=row['updated_date'],
#                                             sync_updated_date=row['sync_updated_date']
#                                             )
#                                             instances.append(instance)
#                                         except Exception as e:
#                                             print(e)
#                                     RateMasterTable.objects.bulk_create(instances,batch_size=10000, ignore_conflicts=True)
#                             try:
#                                 with ThreadPoolExecutor(max_workers=num_count) as executor:
#                                     futures = [executor.submit(process_chunk, chunk) for chunk in chunk_generator()]
#                                     for _ in tqdm(as_completed(futures), total=len(futures), desc="processing rows"):
#                                         sleep(0.1)
#                                 form.save()
#                                 msg1 = len(RateMasterTable.objects.all())
#                                 data = {"site_title": admin.site.site_title, "site_version": admin.site.site_version,
#                                         "cities": city, "msg": "uploaded", "msg1":f'{msg1}rows','city1':city1}
#                                 return render(request, "admin/ImportApp/imports/change_form.html", data)
#                             except Exception as e:
#                                 print(e)
#                                 data = {"site_title": admin.site.site_title, "site_version": admin.site.site_version,
#                                         "cities": city, "msg": e,'city1':city1}
#                                 return render(request, "admin/ImportApp/imports/change_form.html", data)
#                         else:
#                             print("Columns in the CSV (or) TXT file do not match the expected columns.",expected_columns)
#                             e="uploaded file is not Rate_Master_Table.csv  please check and upload !!!."
#                             data = {"site_title": admin.site.site_title, "site_version": admin.site.site_version,
#                                     "cities": city, "msg": e,'city1':city1}
#                             return render(request, "admin/ImportApp/imports/change_form.html", data)

#                     elif moduleType == "BusFareVersion2" and service == 'FareChart':
#                         df = pd.read_csv(upload_file)
#                         expected_columns = ['fare_chart_id','farechart_master_id','route_id','service_type_id','passenger_type_id','schedule_type_id','start_point_id','end_point_id','number_of_kms','fare_amount','toll_fee']
#                         if set(df.columns) == set(expected_columns):
#                             bus = BusFare2.objects.all().delete()
#                             chunk_size = 10000  # Adjust this to your preferred chunk size
#                             num_count = psutil.cpu_count(logical=True)

#                             def chunk_generator():
#                                 for i in range(0, len(df), chunk_size):
#                                     yield df.iloc[i:i + chunk_size]

#                             def process_chunk(chunk):
#                                 instances = []
#                                 # Use transaction.atomic() to wrap the bulk_create operation in an atomic transaction
#                                 with transaction.atomic():
#                                     for i, row in chunk.iterrows():
#                                         route_id = row['route_id']
#                                         farechart_master_id = row['farechart_master_id']
#                                         start_point_id=row['start_point_id']
#                                         end_point_id=row['end_point_id']
#                                         try:
#                                             # route_id_instance = BusRoute.objects.get(route_id=route_id)
#                                             # farechart_master_id_instance = FareChartMaster.objects.get(farechart_master_id=farechart_master_id)
#                                             # start_point_id_instance = BusStop.objects.get(bus_stop_id=start_point_id)
#                                             # end_point_id_instance = BusStop.objects.get(bus_stop_id=end_point_id)
#                                             bus_routes = BusRoute.objects.filter(route_id=route_id).prefetch_related('route_id')
#                                             fare_chart_masters = FareChartMaster.objects.filter(farechart_master_id=farechart_master_id).prefetch_related('farechart_master_id')  # Add related fields as needed
#                                             start_points = BusStop.objects.filter(bus_stop_id=start_point_id).prefetch_related('bus_stop_id')  # Add related fields as needed
#                                             end_points = BusStop.objects.filter(bus_stop_id=end_point_id).prefetch_related('bus_stop_id')  # Add related fields as needed
#                                             instance = BusFare2(
#                                                 fare_chart_id=row['fare_chart_id'],
#                                                 # farechart_master_id=farechart_master_id_instance,
#                                                 farechart_master_id = row['farechart_master_id'],
#                                                 # route_id=route_id_instance,
#                                                 route_id = row['route_id'],
#                                                 service_type_id=row['service_type_id'],
#                                                 passenger_type_id=row['passenger_type_id'],
#                                                 schedule_type_id=row['schedule_type_id'],
#                                                 # start_point_id=start_point_id_instance,
#                                                 start_point_id=row['start_point_id'],
#                                                 # end_point_id=end_point_id_instance,
#                                                 end_point_id=row['end_point_id'],
#                                                 number_of_kms=row['number_of_kms'],
#                                                 fare_amount=row['fare_amount'],
#                                                 toll_fee=row['toll_fee']
#                                             )
#                                             instances.append(instance)
#                                         except Exception as e:
#                                             transaction.rollback()
#                                             print(e)
#                                     BusFare2.objects.bulk_create(instances,batch_size=10000, ignore_conflicts=True)
#                             # # Use ThreadPoolExecutor to parallelize the processing and insertion of chunks
#                             try:
#                                 with ThreadPoolExecutor(max_workers=num_count) as executor:
#                                     # for chunk in chunk_generator():
#                                     #     executor.submit(process_chunk, chunk) 
#                                     futures = [executor.submit(process_chunk, chunk) for chunk in chunk_generator()]
#                                     for _ in tqdm(as_completed(futures), total=len(futures), desc="processing rows"):
#                                         sleep(0.1)
#                                 print("hi")
#                                 # form.save()
#                                 msg1 = len(BusFare2.objects.all())
#                                 data = {"site_title": admin.site.site_title, "site_version": admin.site.site_version,
#                                         "cities": city, "msg": "uploaded", "msg1":f'{msg1}rows','city1':city1}
#                                 return render(request, "admin/ImportApp/imports/change_form.html", data)
#                             except Exception as e:
#                                 print(e)
#                                 data = {"site_title": admin.site.site_title, "site_version": admin.site.site_version,"cities": city, "msg": e,'city1':city1}
#                                 return render(request, "admin/ImportApp/imports/change_form.html", data)
#                         else:
#                             print("Columns in the CSV (or) TXT file do not match the expected columns.",expected_columns)
#                             e="uploaded file is not Fare_chart.csv . please check and upload !!!."
#                             data = {"site_title": admin.site.site_title, "site_version": admin.site.site_version,
#                                     "cities": city, "msg": e,'city1':city1}
#                             return render(request, "admin/ImportApp/imports/change_form.html", data)

#                     elif moduleType == "BusRouteVehicleMap":
#                         df = pd.read_csv(upload_file)
#                         expected_columns = ['id','route_id','route_number','vehicle_number','device_imei','vstc_id']
#                         if set(df.columns) == set(expected_columns):
#                             print("BusRouteVehicleMap module: ", module, "moduleType: ", moduleType, "service: ",
#                                 service, "FileName ", upload_file)
#                             bus = RouteVehicleMap.objects.all()
#                             bus.delete()
#                             chunk_size = 10000  # Adjust this to your preferred chunk size
#                             num_count = psutil.cpu_count(logical=True)

#                             def chunk_generator():
#                                 for i in range(0, len(df), chunk_size):
#                                     yield df.iloc[i:i + chunk_size]

#                             def process_chunk(chunk):
#                                 instances = []
#                                 # Use transaction.atomic() to wrap the bulk_create operation in an atomic transaction
#                                 with transaction.atomic():
#                                     for i, row in chunk.iterrows():
#                                         try:
#                                             instance = RouteVehicleMap(
#                                                     id=row['id'],
#                                                     route_id=row['route_id'],
#                                                     route_number=row['route_number'],
#                                                     vehicle_number=row['vehicle_number'],
#                                                     device_imei=row['device_imei'],
#                                                     vstc_id=row['vstc_id'],)
#                                             instances.append(instance)
#                                         except Exception as e:
#                                                         print(e)
#                                     RouteVehicleMap.objects.bulk_create(instances,batch_size=10000, ignore_conflicts=True)
#                             try:
#                                 with ThreadPoolExecutor(max_workers=num_count) as executor:
#                                     for chunk in chunk_generator():
#                                         executor.submit(process_chunk, chunk)   
#                                     # futures = [executor.submit(process_chunk, chunk) for chunk in chunk_generator()]
#                                     # for _ in tqdm(as_completed(futures), total=len(futures), desc="processing rows"):
#                                     #     sleep(0.1)
#                                 print("hi")
#                                 form.save()
#                                 msg1 = len(RouteVehicleMap.objects.all())
#                                 data = {"site_title": admin.site.site_title, "site_version": admin.site.site_version,
#                                         "cities": city, "msg": "uploaded", "msg1":f'{msg1}rows','city1':city1}
#                                 return render(request, "admin/ImportApp/imports/change_form.html", data)
#                             except Exception as e:
#                                 print(e)
#                                 data = {"site_title": admin.site.site_title, "site_version": admin.site.site_version,
#                                         "cities": city, "msg": e,'city1':city1}
#                                 return render(request, "admin/ImportApp/imports/change_form.html", data)
#                         else:
#                             print("Columns in the CSV (or) TXT file do not match the expected columns.",expected_columns)
#                             e="uploaded file is not bus_route_vehicle_map.csv.  please check and upload !!!."
#                             data = {"site_title": admin.site.site_title, "site_version": admin.site.site_version,
#                                     "cities": city, "msg": e,'city1':city1}
#                             return render(request, "admin/ImportApp/imports/change_form.html", data)
                        
#                     else:
#                         messages.warning(request, 'The wrong module or module type or file was uploaded')
#                         return HttpResponseRedirect(request.path_info)

#                 elif module == "RAIL" and upload_file.name.endswith('.csv'):
#                     df = pd.read_csv(upload_file)

#                     if service != "":
#                         if moduleType == "RailStation":
#                             expected_columns = ['StationId','Station','Code','Lat','Long','Status']
#                             if set(df.columns) == set(expected_columns):
#                                 print("RailStation module: ", module, "moduleType: ", moduleType, "service: ", service,
#                                     "FileName ", upload_file)
#                                 rail = RailStation.objects.all()
#                                 rail.delete()
#                                 chunk_size = 10000  # Adjust this to your preferred chunk size
#                                 num_count = psutil.cpu_count(logical=True)

#                                 def chunk_generator():
#                                     for i in range(0, len(df), chunk_size):
#                                         yield df.iloc[i:i + chunk_size]

#                                 def process_chunk(chunk):
#                                     instances = []
#                                     # Use transaction.atomic() to wrap the bulk_create operation in an atomic transaction
#                                     with transaction.atomic():
#                                         for i, row in chunk.iterrows():
#                                             try:
#                                                 instance =RailStation(
#                                                         id=row['StationId'],
#                                                         name=row['Station'],
#                                                         code=row['Code'],
#                                                         latitude=row['Lat'],
#                                                         longitude=row['Long'],
#                                                         status=row['Status'],

#                                                     )
#                                                 instances.append(instance)
#                                             except Exception as e:
#                                                                 print(e)
#                                         RailStation.objects.bulk_create(instances,batch_size=10000, ignore_conflicts=True)

#                                 try:
#                                     with ThreadPoolExecutor(max_workers=num_count) as executor:
#                                         futures = [executor.submit(process_chunk, chunk) for chunk in chunk_generator()]
#                                         for _ in tqdm(as_completed(futures), total=len(futures), desc="processing rows"):
#                                             sleep(0.1)
#                                     print("hi")
#                                     form.save()
#                                     msg1 = len(RailStation.objects.all())
#                                     data = {"site_title": admin.site.site_title,
#                                             "site_version": admin.site.site_version,
#                                             "cities": city, "msg": "uploaded", "msg1":f'{msg1}rows','city1':city1}
#                                     return render(request, "admin/ImportApp/imports/change_form.html", data)
#                                 except Exception as e:
#                                     print(e)
#                                     data = {"site_title": admin.site.site_title,"site_version": admin.site.site_version,"cities": city, "msg": e,'city1':city1}
#                                     return render(request, "admin/ImportApp/imports/change_form.html", data)
#                             else:
#                                 print("Columns in the CSV (or) TXT file do not match the expected columns.",expected_columns)
#                                 e="uploaded file is not rail_station.csv  please check and upload !!!."
#                                 data = {"site_title": admin.site.site_title, "site_version": admin.site.site_version,
#                                         "cities": city, "msg": e,'city1':city1}
#                                 return render(request, "admin/ImportApp/imports/change_form.html", data)

#                         elif moduleType == "RailRoute":
#                             expected_columns = ['RouteId','Name','Line','Status','Route_Direction','IsMainRoute','Description']
#                             if set(df.columns) == set(expected_columns):
#                                 print("RailRoute module: ", module, "moduleType: ", moduleType, "service: ", service,
#                                     "FileName ", upload_file)
#                                 rail = RailRoute.objects.all()
#                                 rail.delete()
#                                 chunk_size = 10000  # Adjust this to your preferred chunk size
#                                 num_count = psutil.cpu_count(logical=True)

#                                 def chunk_generator():
#                                     for i in range(0, len(df), chunk_size):
#                                         yield df.iloc[i:i + chunk_size]

#                                 def process_chunk(chunk):
#                                     instances = []
#                                     # Use transaction.atomic() to wrap the bulk_create operation in an atomic transaction
#                                     with transaction.atomic():
#                                         for i, row in chunk.iterrows():
#                                             try:
#                                                 instance = RailRoute(
#                                                         id=row['RouteId'],
#                                                         name=row['Name'],
#                                                         line=row['Line'],
#                                                         status=row['Status'],
#                                                         direction=row['Route_Direction'],
#                                                         is_main_route=row['IsMainRoute'],
#                                                         description=row['Description']

#                                                     )
#                                                 instances.append(instance)
#                                             except Exception as e:
#                                                                 print(e)
#                                         RailRoute.objects.bulk_create(instances,batch_size=10000, ignore_conflicts=True)
#                                 try:
#                                     with ThreadPoolExecutor(max_workers=num_count) as executor:
#                                         futures = [executor.submit(process_chunk, chunk) for chunk in chunk_generator()]
#                                         for _ in tqdm(as_completed(futures), total=len(futures), desc="processing rows"):
#                                             sleep(0.1)
#                                     form.save()
#                                     msg1 = len(RailRoute.objects.all())
#                                     data = {"site_title": admin.site.site_title,"site_version": admin.site.site_version,"cities": city, "msg": "uploaded", "msg1":f'{msg1}rows','city1':city1}
#                                     return render(request, "admin/ImportApp/imports/change_form.html", data)
#                                 except Exception as e:
#                                     print(e)
#                                     data = {"site_title": admin.site.site_title,"site_version": admin.site.site_version,"cities": city, "msg": e,'city1':city1}
#                                     return render(request, "admin/ImportApp/imports/change_form.html", data)
#                             else:
#                                 print("Columns in the CSV (or) TXT file do not match the expected columns.",expected_columns)
#                                 e="uploaded file is not rail_route.csv . please check and upload !!!."
#                                 data = {"site_title": admin.site.site_title, "site_version": admin.site.site_version,"cities": city, "msg": e,'city1':city1}
#                                 return render(request, "admin/ImportApp/imports/change_form.html", data)
#                         elif moduleType == "RailSchedule":
#                             expected_columns = ['ScheduleId','RouteId','StartTime','EndTime','TrainNo','DayType','TrainType','Status']
#                             if set(df.columns) == set(expected_columns):
#                                 print(" RailSchedule module: ", module, "moduleType: ", moduleType, "service: ", service,
#                                     "FileName ", upload_file)
#                                 instances = []
#                                 rail = RailSchedule.objects.all()
#                                 print("hi")
#                                 rail.delete()
#                                 chunk_size = 10000  # Adjust this to your preferred chunk size
#                                 num_count = psutil.cpu_count(logical=True)

#                                 def chunk_generator():
#                                     for i in range(0, len(df), chunk_size):
#                                         yield df.iloc[i:i + chunk_size]

#                                 def process_chunk(chunk):
#                                     instances = []
#                                     # Use transaction.atomic() to wrap the bulk_create operation in an atomic transaction
#                                     with transaction.atomic():
#                                         for i, row in chunk.iterrows():

#                                             route_id = int(row['RouteId'])
#                                             try:
#                                                 route_id_instance = RailRoute.objects.get(id=route_id)
#                                                 instance = RailSchedule(
#                                                 id = int(row['ScheduleId']),
#                                                 route_id=route_id_instance,
#                                                 start_time=row['StartTime'],
#                                                 end_time = row['EndTime'],
#                                                 train_no = row['TrainNo'],
#                                                 day_type = str(row['DayType']),
#                                                 train_type = row['TrainType'],
#                                                 status = row['Status'])

#                                                 instances.append(instance)
#                                             except Exception as e:
#                                                                 print(e)
#                                         RailSchedule.objects.bulk_create(instances,batch_size=10000, ignore_conflicts=True)
#                                 try:
#                                     with ThreadPoolExecutor(max_workers=num_count) as executor:
#                                         futures = [executor.submit(process_chunk, chunk) for chunk in chunk_generator()]
#                                         for _ in tqdm(as_completed(futures), total=len(futures), desc="processing rows"):
#                                             sleep(0.1)
#                                     print("hi")
#                                     form.save()
#                                     msg1 = len(RailSchedule.objects.all())
#                                     data = {"site_title": admin.site.site_title,
#                                             "site_version": admin.site.site_version,
#                                             "cities": city, "msg": "uploaded", "msg1":f'{msg1}rows','city1':city1}
#                                     return render(request, "admin/ImportApp/imports/change_form.html", data)
#                                 except Exception as e:
#                                     print(e)
#                                     data = {"site_title": admin.site.site_title,"site_version": admin.site.site_version,"cities": city, "msg": e,'city1':city1}
#                                     return render(request, "admin/ImportApp/imports/change_form.html", data)
#                             else:
#                                 print("Columns in the CSV (or) TXT file do not match the expected columns.",expected_columns)
#                                 e="uploaded file is not rail_schedule.csv . please check and upload !!!."
#                                 data = {"site_title": admin.site.site_title, "site_version": admin.site.site_version,"cities": city, "msg": e,'city1':city1}
#                                 return render(request, "admin/ImportApp/imports/change_form.html", data)

#                         elif moduleType == "RailSchedulePoint":
#                             expected_columns = ['id','RouteId','StationId','schedule_id','seq','start_time','status','platform_no']
#                             if set(df.columns) == set(expected_columns):
#                                 print("RailSchedulePoint module: ", module, "moduleType: ", moduleType, "service: ",service, "FileName ", upload_file)
#                                 rail = RailSchedulePoint.objects.all()
#                                 print("hi")
#                                 rail.delete()
#                                 chunk_size = 10000  # Adjust this to your preferred chunk size
#                                 num_count = psutil.cpu_count(logical=True)

#                                 def chunk_generator():
#                                     for i in range(0, len(df), chunk_size):
#                                         yield df.iloc[i:i + chunk_size]

#                                 def process_chunk(chunk):
#                                     instances = []
#                                     with transaction.atomic():
#                                         for i, row in chunk.iterrows():
#                                             route_id = row['RouteId']
#                                             station_id = str(row['StationId'])
#                                             try:
#                                                 route_id_instance = RailRoute.objects.get(id=route_id)
#                                                 station_id_instance = RailStation.objects.get(id=station_id)
#                                                 RailSchedulePoint_instance = RailSchedulePoint(
#                                                 id=row['id'],
#                                                 route_id=route_id_instance,
#                                                 station_id=station_id_instance,
#                                                 schedule_id=row['schedule_id'],
#                                                 seq=row['seq'],
#                                                 start_time=row['start_time'],
#                                                 status=row['status'],
#                                                 platform_no=row['platform_no'])
#                                                 instances.append(RailSchedulePoint_instance)
#                                             except (RailStation.DoesNotExist):
#                                                 print(RailStation.DoesNotExist)
#                                         RailSchedulePoint.objects.bulk_create(instances,batch_size=10000, ignore_conflicts=True)
#                                 try:
#                                     with ThreadPoolExecutor(max_workers=num_count) as executor:
#                                         futures = [executor.submit(process_chunk, chunk) for chunk in chunk_generator()]
#                                         for _ in tqdm(as_completed(futures), total=len(futures), desc="processing rows"):
#                                             sleep(0.1)
#                                     print("hi")
#                                     form.save()
#                                     msg1=len(RailStation.objects.all())
#                                     data = {"site_title": admin.site.site_title,
#                                             "site_version": admin.site.site_version,
#                                             "cities": city, "msg": "uploaded", "msg1":f'{msg1}rows','city1':city1}
#                                     return render(request, "admin/ImportApp/imports/change_form.html", data)
#                                 except Exception as e:
#                                     print(e)
#                                     data = {"site_title": admin.site.site_title,"site_version": admin.site.site_version,"cities": city, "msg": e,'city1':city1}
#                                     return render(request, "admin/ImportApp/imports/change_form.html", data)
#                             else:
#                                 print("Columns in the CSV (or) TXT file do not match the expected columns.",expected_columns)
#                                 e="uploaded file is not rail_schedule_point.csv.  please check and upload!!!."
#                                 data = {"site_title": admin.site.site_title, "site_version": admin.site.site_version,"cities": city, "msg": e,'city1':city1}
#                                 return render(request, "admin/ImportApp/imports/change_form.html", data)

#                         elif moduleType == "RailFareDistancePoint":
#                             expected_columns = ['route_id','schedule_id','seq','start_time','distance','status','service']
#                             if set(df.columns) == set(expected_columns):
#                                 print("RailFareDistancePoint module: ", module, "moduleType: ", moduleType, "service: ",service, "FileName ", upload_file)
#                                 instances = []
#                                 rail = RailFareDistancePoint.objects.all()
#                                 print("hi")
#                                 rail.delete()
#                                 chunk_size = 10000  # Adjust this to your preferred chunk size
#                                 num_count = psutil.cpu_count(logical=True)

#                                 def chunk_generator():
#                                     for i in range(0, len(df), chunk_size):
#                                         yield df.iloc[i:i + chunk_size]

#                                 def process_chunk(chunk):
#                                     instances = []
#                                     with transaction.atomic():
#                                         for i, row in chunk.iterrows():
#                                             # route_id = row['route_id']
#                                             # schedule_id = row['schedule_id']
#                                             try:
#                                                 # route_id_instance = RailRoute.objects.get(id=route_id)
#                                                 # schedule_id_instance = RailSchedulePoint.objects.get(id=schedule_id)
#                                                 RailFareDistancePoint_instance = RailFareDistancePoint(
#                                                 rail_fare_distance_point_id=row['rail_fare_distance_point_id'],
#                                                 route_id = row['route_id'],
#                                                 schedule_id = row['schedule_id'],
#                                                 seq = row['seq'],
#                                                 start_time = str(row['start_time']),
#                                                 distance = row['distance'],
#                                                 status = row['status'],
#                                                 service = row['service'])
#                                                 instances.append(RailFareDistancePoint_instance)
#                                             except Exception as e:
#                                                 print(e)
#                                         RailFareDistancePoint.objects.bulk_create(instances,batch_size=10000, ignore_conflicts=True)
#                                 try:
#                                     with ThreadPoolExecutor(max_workers=num_count) as executor:
#                                         futures = [executor.submit(process_chunk, chunk) for chunk in chunk_generator()]
#                                         for _ in tqdm(as_completed(futures), total=len(futures), desc="processing rows"):
#                                             sleep(0.1)
#                                     print("hi")
#                                     form.save()
#                                     msg1=len(RailFareDistancePoint.objects.all())
#                                     data = {"site_title": admin.site.site_title,"site_version": admin.site.site_version,"cities": city, "msg": "uploaded", "msg1":f'{msg1}rows','city1':city1}
#                                     return render(request, "admin/ImportApp/imports/change_form.html", data)
#                                 except Exception as e:
#                                     print(e)
#                                     data = {"site_title": admin.site.site_title,"site_version": admin.site.site_version,"cities": city, "msg": e,'city1':city1}
#                                     return render(request, "admin/ImportApp/imports/change_form.html", data)
#                             else:
#                                 print("Columns in the CSV (or) TXT file do not match the expected columns.",expected_columns)
#                                 e="uploaded file is not rail_fare_distance_point.csv. please check and upload!!!."
#                                 data = {"site_title": admin.site.site_title, "site_version": admin.site.site_version,"cities": city, "msg": e,'city1':city1}
#                                 return render(request, "admin/ImportApp/imports/change_form.html", data)
#                         elif moduleType == "RailFare":
#                             expected_columns = ['rail_fare_id','from_distance','to_distance','adult_fare','child_fare']
#                             if set(df.columns) == set(expected_columns):
#                                 print("RailFare module: ", module, "moduleType: ", moduleType, "service: ",service, "FileName ", upload_file)
#                                 instances = []
#                                 rail = RailFare.objects.all()
#                                 print("hi")
#                                 rail.delete()
#                                 chunk_size = 10000  # Adjust this to your preferred chunk size
#                                 num_count = psutil.cpu_count(logical=True)

#                                 def chunk_generator():
#                                     for i in range(0, len(df), chunk_size):
#                                         yield df.iloc[i:i + chunk_size]

#                                 def process_chunk(chunk):
#                                     instances = []
#                                     with transaction.atomic():
#                                         for i, row in chunk.iterrows():
#                                             try:
#                                                 RailFare_instance = RailFare(
#                                                 rail_fare_id = row['rail_fare_id'],
#                                                 from_distance = row['from_distance'],
#                                                 to_distance = row['to_distance'],
#                                                 adult_fare = row['adult_fare'],
#                                                 child_fare = row['child_fare'])
#                                                 instances.append(RailFare_instance)
#                                             except Exception as e:
#                                                 print(e)
#                                         RailFare.objects.bulk_create(instances,batch_size=10000, ignore_conflicts=True)
#                                     try:
#                                         with ThreadPoolExecutor(max_workers=num_count) as executor:
#                                             futures = [executor.submit(process_chunk, chunk) for chunk in chunk_generator()]
#                                             for _ in tqdm(as_completed(futures), total=len(futures), desc="processing rows"):
#                                                 sleep(0.1)
#                                         print("hi")
#                                         form.save()
#                                         msg1=len(RailFare.objects.all())
#                                         data = {"site_title": admin.site.site_title,"site_version": admin.site.site_version,"cities": city, "msg": "uploaded", "msg1":f'{msg1}rows','city1':city1}
#                                         return render(request, "admin/ImportApp/imports/change_form.html", data)
#                                     except Exception as e:
#                                         print(e)
#                                         data = {"site_title": admin.site.site_title,"site_version": admin.site.site_version,"cities": city, "msg": e,'city1':city1}
#                                         return render(request, "admin/ImportApp/imports/change_form.html", data)
#                             else:
#                                 print("Columns in the CSV (or) TXT file do not match the expected columns.",expected_columns)
#                                 e="uploaded file is not rail_fare.csv . please check and upload !!!."
#                                 data = {"site_title": admin.site.site_title, "site_version": admin.site.site_version,"cities": city, "msg": e,'city1':city1}
#                                 return render(request, "admin/ImportApp/imports/change_form.html", data)        
#                         elif moduleType == "RailStationCross":
#                             expected_columns = ['line','station_id','is_cross']
#                             if set(df.columns) == set(expected_columns):
#                                 print("RailStationCross module: ", module, "moduleType: ", moduleType, "service: ",service, "FileName ", upload_file)
#                                 instances = []
#                                 rail = RailStationCross.objects.all()
#                                 print("hi")
#                                 rail.delete()
#                                 chunk_size = 10000  # Adjust this to your preferred chunk size
#                                 num_count = psutil.cpu_count(logical=True)

#                                 def chunk_generator():
#                                     for i in range(0, len(df), chunk_size):
#                                         yield df.iloc[i:i + chunk_size]

#                                 def process_chunk(chunk):
#                                     instances = []
#                                     with transaction.atomic():
#                                         for i, row in chunk.iterrows():
#                                             station_id = row['station_id']
#                                             try:
#                                                 station_id_instance = RailStation.objects.get(id=station_id)
#                                                 RailCross_instance = RailStationCross(
#                                                 # id = row['id'],
#                                                 line = row['line'],
#                                                 station_id = station_id_instance,
#                                                 is_cross = row['is_cross'])
#                                                 instances.append(RailCross_instance)
#                                             except Exception as e:
#                                                 print(e)
#                                         RailStationCross.objects.bulk_create(instances,batch_size=10000, ignore_conflicts=True)
#                                 try:
#                                     with ThreadPoolExecutor(max_workers=num_count) as executor:
#                                         for chunk in chunk_generator():
#                                             executor.submit(process_chunk, chunk)
#                                     print("hi")
#                                     form.save()
#                                     msg1=len(RailStationCross.objects.all())
#                                     data = {"site_title": admin.site.site_title,"site_version": admin.site.site_version,"cities": city, "msg": "uploaded", "msg1":f'{msg1}rows','city1':city1}
#                                     return render(request, "admin/ImportApp/imports/change_form.html", data)
#                                 except Exception as e:
#                                     print(e)
#                                     data = {"site_title": admin.site.site_title,"site_version": admin.site.site_version,"cities": city, "msg": e,'city1':city1}
#                                     return render(request, "admin/ImportApp/imports/change_form.html", data)
#                             else:
#                                 print("Columns in the CSV (or) TXT file do not match the expected columns.",expected_columns)
#                                 e="uploaded file is not rail_cross_station.csv  please check and upload !!!."
#                                 data = {"site_title": admin.site.site_title, "site_version": admin.site.site_version,"cities": city, "msg": e,'city1':city1}
#                                 return render(request, "admin/ImportApp/imports/change_form.html", data)

#                         elif moduleType == "RailLineMaster":
#                             expected_columns = ['id','line','station_id','order']
#                             if set(df.columns) == set(expected_columns):
#                                 print("RailLineMaster module: ", module, "moduleType: ", moduleType, "service: ",service, "FileName ", upload_file)
#                                 instances = []
#                                 rail = RailLineMaster.objects.all()
#                                 rail.delete()
#                                 chunk_size = 10000  # Adjust this to your preferred chunk size
#                                 num_count = psutil.cpu_count(logical=True)

#                                 def chunk_generator():
#                                     for i in range(0, len(df), chunk_size):
#                                         yield df.iloc[i:i + chunk_size]

#                                 def process_chunk(chunk):
#                                     instances = []
#                                     with transaction.atomic():
#                                         for i, row in chunk.iterrows(): 
#                                             try:
#                                                 # station_id_instance = RailStation.objects.get(id=station_id)
#                                                 RailLine_instance = RailLineMaster(
#                                                 id = row['id'],
#                                                 line = row['line'],
#                                                 station_id = row['station_id'],
#                                                 order = row['order'])
#                                                 instances.append(RailLine_instance)
#                                             except Exception as e:
#                                                 print(e)
#                                         RailLineMaster.objects.bulk_create(instances,batch_size=10000, ignore_conflicts=True)
#                                 try:
#                                     with ThreadPoolExecutor(max_workers=num_count) as executor:
#                                         futures = [executor.submit(process_chunk, chunk) for chunk in chunk_generator()]
#                                         for _ in tqdm(as_completed(futures), total=len(futures), desc="processing rows"):
#                                             sleep(0.1)
#                                     form.save()
#                                     msg1=len(RailLineMaster.objects.all())
#                                     data = {"site_title": admin.site.site_title,"site_version": admin.site.site_version,"cities": city, "msg": "uploaded", "msg1":f'{msg1}rows','city1':city1}
#                                     return render(request, "admin/ImportApp/imports/change_form.html", data)
#                                 except Exception as e:
#                                     print(e)
#                                     data = {"site_title": admin.site.site_title,"site_version": admin.site.site_version,"cities": city, "msg": e,'city1':city1}
#                                     return render(request, "admin/ImportApp/imports/change_form.html", data)
#                             else:
#                                 print("Columns in the CSV (or) TXT file do not match the expected columns.",expected_columns)
#                                 e="uploaded file is not rail_line_master.csv.  please check and upload !!!."
#                                 data = {"site_title": admin.site.site_title, "site_version": admin.site.site_version,"cities": city, "msg": e,'city1':city1}
#                                 return render(request, "admin/ImportApp/imports/change_form.html", data)

#                         else:
#                             messages.warning(request, 'The wrong module or module type or file was uploaded')
#                             return HttpResponseRedirect(request.path_info)
#                     else:
#                         messages.warning(request, 'Service type must be included for rail')
#                         return HttpResponseRedirect(request.path_info)

#                 elif module == "METRO" and upload_file.name.endswith('.xlsx'):
#                     print(module, upload_file)

#                     if moduleType == "MetroRoute":
#                         print(moduleType)
#                         workbook = openpyxl.load_workbook(upload_file)
#                         # master_service.MetroTime(upload_file)
#                         print("MetroRoute module: ", module, "moduleType: ", moduleType, "service: ", service,"FileName ", upload_file)
#                         objs = MetroRoute.objects.all()
#                         objs.delete()

#                         # Get the list of sheet names in the workbook
#                         sheet_names = workbook.sheetnames

#                         # Set to store encountered rows
#                         encountered_rows = set()

#                         # List to store data for the DataFrame
#                         data = []

#                         # Loop through each sheet name
#                         for sheet_name in sheet_names:
#                             # Get the specific sheet
#                             sheet = workbook[sheet_name]

#                             # Get the values from the first row and the first four columns
#                             first_row_values = sheet.iter_rows(min_row=1, max_row=1, values_only=True).__next__()[:4]

#                             # Convert the values to a tuple and check if it's a duplicate
#                             row_tuple = tuple(first_row_values)
#                             # round_tuple = tuple(second_row_values)
#                             if row_tuple not in encountered_rows:
#                                 # Append the row values to the data list
#                                 data.append(first_row_values)

#                                 # Add the row tuple to the encountered set
#                                 encountered_rows.add(row_tuple)

#                         df = pd.DataFrame(data,columns=['Route_name', 'Route_color_code', 'Metro_operator', 'op_logo_url'])
                        

#                         df_records = df.to_dict('records')

#                         objs = [
#                             MetroRoute(
#                                 route_name=col['Route_name'],
#                                 route_color_code=col['Route_color_code'],
#                                 metro_operator=col['Metro_operator'],
#                                 op_logo_url=col['op_logo_url']

#                             )
#                             for col in df_records
#                         ]

#                         try:
#                             MetroRoute.objects.bulk_create(objs)
#                             # Close the workbook when done
#                             workbook.close()
#                             print("hi")
#                             form.save()
#                             msg1 = len(MetroRoute.objects.all())
#                             data = {"site_title": admin.site.site_title, "site_version": admin.site.site_version,"cities": city, "msg": "uploaded", "msg1":f'{msg1}rows','city1':city1}
#                             return render(request, "admin/ImportApp/imports/change_form.html", data)
#                         except Exception as e:
#                             print(e)
#                             data = {"site_title": admin.site.site_title, "site_version": admin.site.site_version,"cities": city, "msg": e,'city1':city1}
#                             return render(request, "admin/ImportApp/imports/change_form.html", data)

#                     elif moduleType == "MetroTime":
#                         print("MetroTime module: ", module, "moduleType: ", moduleType, "service: ", service,"FileName ", upload_file)
#                         s_instances = []
#                         t_instance = []
#                         r_instances = []
#                         day_types = {"Monday-Friday": 1, "Monday-Saturday": 2, "Saturday-Sunday": 3,
#                                      "Sunday-Holiday": 4, "Saturday": 5, "Sunday": 6, "Holiday": 7, "Daily": 8,
#                                      "Saturday-Sunday-Holiday": 9, "Monday-Friday-1-3-Saturday": 10,
#                                      "Holiday-2-4-Saturday": 11}
#                         met = MetroRoute.objects.all()
#                         met.delete()
#                         sta = Station.objects.all()
#                         sta.delete()
#                         list1 = Times.objects.all()
#                         list1.delete()
#                         f = pd.ExcelFile(upload_file)
#                         final_station = pd.DataFrame()
#                         time_station = pd.DataFrame()
#                         route_data = pd.DataFrame()
#                         existing_routes = set()
                        
#                         for sheet in f.sheet_names:
#                             # Parse data from the current worksheet as a Pandas DataFrame
#                             df = f.parse(sheet, header=None)

#                             # Extract specific cell data
#                             route_name = df.iloc[0, 0]
#                             time = df.iloc[2:].copy()
#                             print(time)
#                             Day_type = df.iloc[1, 0]
#                             direction = df.iloc[1, 1]
#                             for slot in time.columns.values[2:]:
#                                 slot_df = pd.DataFrame(
#                                     {"time": time[slot].tolist(), "slot": slot, "direction": direction,
#                                      "Day_type": day_types[Day_type],
#                                      "station_name": time[0].tolist(), "route_name": route_name})
#                                 time_station = pd.concat([time_station, slot_df], ignore_index=True)
#                             time_station = time_station.dropna()
#                             time_station.to_excel('file223.xlsx')
#                             if route_name in existing_routes:
#                                 continue  # Skip uploading if route_name exists
#                             else:
#                                 existing_routes.add(route_name)
#                                 route_color_code = df.iloc[0, 1]
#                                 op_logo_url = df.iloc[0, 2]
#                                 metro_operator = df.iloc[0, 3]
#                                 route = pd.DataFrame([[route_name, route_color_code, op_logo_url, metro_operator]],columns=['route_name', 'route_color_code', 'op_logo_url','metro_operator'])
#                                 route_data = pd.concat([route_data, route], ignore_index=True)
#                                 station = df.iloc[2:, 0:2].copy()
#                                 station.reset_index(drop=True, inplace=True)
#                                 station['route_name'] = route_name
#                                 station['seq'] = station.index + 1
#                                 station[["lat", "lon"]] = station[1].str.split(",", expand=True).apply(lambda x: x)
#                                 station['station_name'] = station[0]
#                                 station.drop(columns=[0, 1], inplace=True, axis=1)
#                                 final_station = pd.concat([final_station, station], ignore_index=True)

#                         with transaction.atomic():
#                             for i, row in route_data.iterrows():
#                                 route_name = row['route_name']
#                                 route_color_code = row['route_color_code']
#                                 metro_operator = row['metro_operator']
#                                 op_logo_url = row['op_logo_url']
#                                 route_instance = MetroRoute(route_name=route_name, route_color_code=route_color_code,metro_operator=metro_operator, op_logo_url=op_logo_url)
#                                 r_instances.append(route_instance)
#                         MetroRoute.objects.bulk_create(r_instances)
#                         with transaction.atomic():
#                             for i, row in final_station.iterrows():
#                                 route_name = row["route_name"]
#                                 station_name = row["station_name"]
#                                 lat = row["lat"]
#                                 lon = row["lon"]
#                                 seq = row["seq"]
#                                 route_id_instance = MetroRoute.objects.get(route_name=route_name)
#                                 station_instance = Station(station_name=station_name,
#                                 latitude=lat,
#                                 longitude=lon,
#                                 seq=seq,
#                                 metro_route_id=route_id_instance.metro_route_id)
#                                 s_instances.append(station_instance)
#                         Station.objects.bulk_create(s_instances)
#                         print(time_station)
#                         time_station['slot'].fillna(slot, inplace=True)
#                         chunk_size = 10000  # Adjust this to your preferred chunk size
#                         num_count = psutil.cpu_count(logical=True)

#                         def chunk_generator():
#                             for i in range(0, len(time_station), chunk_size):
#                                 yield df.iloc[i:i + chunk_size]

#                         def process_chunk(chunk):
#                             t_instance=[]
#                             with transaction.atomic():
#                                 for i, row in time_station.iterrows():
#                                     route_name = row['route_name']
#                                     # print(route_name)
#                                     time = row['time']
#                                     print(time)
#                                     slot = row['slot']
#                                     direction = row['direction']
#                                     Day_type = row['Day_type']
#                                     station_name = row["station_name"]
#                                     route_id_instance = MetroRoute.objects.get(route_name=route_name)

#                                     station = Station.objects.get(station_name=station_name,
#                                                                     metro_route_id=route_id_instance.metro_route_id)
#                                     time_instance = Times(diparture_time=time, slot_identity=slot, direction=direction,
#                                                             day_type=Day_type, station_id=station.station_id)
#                                     t_instance.append(time_instance)

#                                 Times.objects.bulk_create(t_instancebatch_size=10000, ignore_conflicts=True)
#                         try:
                            
#                             with ThreadPoolExecutor(max_workers=num_count) as executor:
#                                 futures = [executor.submit(process_chunk, chunk) for chunk in chunk_generator()]
#                                 for _ in tqdm(as_completed(futures), total=len(futures), desc="processing rows"):
#                                     sleep(0.1)
#                             print("times uploaded")
#                             msg1 = len(MetroTime.objects.all())
#                             data = {"site_title": admin.site.site_title, "site_version": admin.site.site_version,
#                                     "cities": city, "msg": "uploaded", "msg1":f'{msg1}rows','city1':city1}
#                             return render(request, "admin/ImportApp/imports/change_form.html", data)
#                         except Exception as e:
                            
#                             data = {"site_title": admin.site.site_title, "site_version": admin.site.site_version,
#                                     "cities": city, "msg": e,'city1':city1}
#                             return render(request, "admin/ImportApp/imports/change_form.html", data)

#                     elif moduleType == "MetroFare":
#                         print("MetroFare module: ", module, "moduleType: ", moduleType, "service: ", service,
#                               "FileName ", upload_file)
#                         f = pd.ExcelFile(upload_file)
#                         df = f.parse()
#                         fare_sheet = pd.DataFrame(df)  # this is fare from fare.xlsx
#                         station_name = fare_sheet.iloc[0:, 0].tolist()

#                         allStation = []

#                         for station in station_name:

#                             for obj in Station.objects.filter(station_name=station).values():
#                                 allStation.append(obj)
#                         stations = pd.DataFrame(allStation)
#                         stations_fare_format = pd.DataFrame()  # Station all get formant this contain name and id
#                         fare_format = pd.DataFrame()  # this is for metro fare format based names
#                         for i, row in stations.iterrows():
#                             single_fare = pd.DataFrame({
#                                 "fare_amount": 0,
#                                 "station_id_from": row['station_id'],
#                                 "station_name_from": row['station_name'],
#                                 "station_id_to": stations['station_id'].to_list(),
#                                 "station_name_to": stations['station_name'].to_list()
#                             })
#                             stations_fare_format = pd.concat([stations_fare_format, single_fare], ignore_index=True)
#                         stations_fare_format = stations_fare_format[
#                             stations_fare_format['station_id_from'] != stations_fare_format['station_id_to']]

#                         for i, row in stations.iterrows():
#                             single_fare2 = pd.DataFrame({
#                                 # "station_id_from":row['station_id'],
#                                 "station_name_from": row['station_name'],
#                                 "station_name_to": fare_sheet['Fare_chart'].to_list(),
#                                 "fare_amount1": fare_sheet[row['station_name']].to_list()
#                             })
#                             # stations_fare_format2=pd.concat([stations_fare_format2,single_fare2],ignore_index=True)\
#                             fare_format = pd.concat([fare_format, single_fare2], ignore_index=True)
#                         fare_format = fare_format[fare_format['station_name_from'] != fare_format['station_name_to']]
#                         stations_fare_format['fare_amount'] = \
#                         stations_fare_format.merge(fare_format, on=["station_name_from", "station_name_to"])[
#                             'fare_amount1']

#                         objs = MetroFare.objects.all()
#                         objs.delete()
#                         df_records = df.to_dict('records')
#                         chunk_size = 10000  # Adjust this to your preferred chunk size
#                         num_count = psutil.cpu_count(logical=True)

#                         def chunk_generator():
#                             for i in range(0, len(time_station), chunk_size):
#                                 yield df.iloc[i:i + chunk_size]

#                         def process_chunk(chunk):
#                             instance = []
#                             with transaction.atomic():
#                                 for i, row in stations_fare_format.iterrows():
#                                     fare_amount = row['fare_amount']
#                                     station_id_from = row['station_id_from']
#                                     station_id_to = row['station_id_to']

#                                     station_from_instance = Station.objects.get(station_id=station_id_from)
#                                     station_to_instance = Station.objects.get(station_id=station_id_to)
#                                     fare_instance = MetroFare(amount=fare_amount, station_id_from=station_from_instance,
#                                                             station_id_to=station_to_instance)
#                                     instance.append(fare_instance)
#                                 MetroFare.objects.bulk_create(instance)
#                             try:
#                                 with ThreadPoolExecutor(max_workers=num_count) as executor:
#                                     futures = [executor.submit(process_chunk, chunk) for chunk in chunk_generator()]
#                                     for _ in tqdm(as_completed(futures), total=len(futures), desc="processing rows"):
#                                         sleep(0.1)
#                                 form.save()
#                                 msg1=len(MetroFare.objects.all())
#                                 data = {"site_title": admin.site.site_title, "site_version": admin.site.site_version,
#                                         "cities": city, "msg": "uploaded", "msg1":f'{msg1}rows','city1':city1}
#                                 return render(request, "admin/ImportApp/imports/change_form.html", data)
#                             except Exception as e:
                                
#                                 data = {"site_title": admin.site.site_title, "site_version": admin.site.site_version,
#                                         "cities": city, "msg": e,'city1':city1}
#                                 return render(request, "admin/ImportApp/imports/change_form.html", data)
#                     else:
#                         data = {"site_title": admin.site.site_title, "site_version": admin.site.site_version,
#                                 "cities": city, "msg": "data is same as existing",'city1':city1}
#                         return render(request, "admin/ImportApp/imports/change_form.html", data)
#                 else:
#                     messages.warning(request, 'The wrong module or module type or file was uploaded')
#                     return HttpResponseRedirect(request.path_info)

#             except Exception as e:
                
#                 print(e)
#                 messages.warning(request, 'Invalid form type')
#                 return HttpResponseRedirect(request.path_info)
#         else:
#             form = ImportForm()

#         db_name = request.session["db_name"]
#         print("db_name ", db_name)
#         city1 = Cities.objects.all()
#         # conf.settings.DATABASES['master']['NAME'] = db_name
#         data = {"site_title": admin.site.site_title, "site_version": admin.site.site_version, "cities": city, 'city1':city1}
#         return render(request, "admin/ImportApp/imports/change_form.html", data)

# admin.site.register(Imports, ImportsAdmin)

