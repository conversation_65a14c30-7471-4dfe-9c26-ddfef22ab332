#!/usr/bin/env python
"""
Test script to create a sample file and test the file processing functionality
"""

import os
import sys
import django
from pathlib import Path
import tempfile

# Add the project directory to Python path
BASE_DIR = Path(__file__).resolve().parent
sys.path.append(str(BASE_DIR))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'TransitCMS.settings')
django.setup()

from FileProcessorApp.service.file_processor import FileProcessorService
from FileProcessorApp.service.ai_analyzer import AIAnalyzer
from FileProcessorApp.service.gtfs_generator import GTFSGeneratorService


def create_sample_transit_csv():
    """Create a sample CSV file with transit data"""
    csv_content = """route_id,route_name,route_type,agency_name
1,Main Street Line,Bus,City Transit
2,Airport Express,Bus,City Transit
3,Downtown Circulator,Bus,City Transit
4,University Shuttle,Bus,City Transit
5,Night Express,Bus,City Transit"""
    
    # Create temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
        f.write(csv_content)
        return f.name


def create_sample_transit_text():
    """Create a sample text file with transit information"""
    text_content = """
CITY TRANSIT SYSTEM SCHEDULE

Route 123: Downtown Express
- Operates Monday through Friday
- Service hours: 6:00 AM to 10:00 PM
- Frequency: Every 15 minutes during peak hours

STOPS:
1. Central Station (Main Terminal)
   Location: 123 Main Street
   Coordinates: 40.7128, -74.0060

2. Business District
   Location: 456 Commerce Ave
   Coordinates: 40.7150, -74.0040

3. University Campus
   Location: 789 College Blvd
   Coordinates: 40.7200, -74.0020

FARE INFORMATION:
- Regular fare: $2.50
- Senior/Student: $1.25
- Day pass: $8.00

AGENCY INFORMATION:
City Transit Authority
Phone: (*************
Website: www.citytransit.gov
"""
    
    # Create temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
        f.write(text_content)
        return f.name


def test_csv_processing():
    """Test CSV file processing"""
    print("📊 Testing CSV Processing...")
    
    csv_file = create_sample_transit_csv()
    
    try:
        processor = FileProcessorService()
        
        # Test CSV processing
        extracted_text = processor._process_csv(csv_file)
        print(f"✅ CSV processed successfully: {len(extracted_text)} characters extracted")
        
        # Test AI analysis
        analyzer = AIAnalyzer()
        analysis = analyzer._fallback_analysis(extracted_text)
        print(f"✅ Analysis completed: {len(analysis)} characters")
        
        # Test GTFS generation
        gtfs_gen = GTFSGeneratorService()
        entities = gtfs_gen._fallback_gtfs_extraction(None, [])
        print(f"✅ GTFS entities created: {list(entities.keys())}")
        
        return True
        
    except Exception as e:
        print(f"❌ CSV processing error: {e}")
        return False
        
    finally:
        # Clean up
        if os.path.exists(csv_file):
            os.unlink(csv_file)


def test_text_processing():
    """Test text file processing"""
    print("\n📄 Testing Text Processing...")
    
    text_file = create_sample_transit_text()
    
    try:
        processor = FileProcessorService()
        
        # Test text processing
        extracted_text = processor._process_text(text_file)
        print(f"✅ Text processed successfully: {len(extracted_text)} characters extracted")
        
        # Test rule-based analysis
        analysis = processor._rule_based_analysis(extracted_text)
        print(f"✅ Rule-based analysis completed")
        
        # Check if transit keywords were found
        import json
        analysis_data = json.loads(analysis)
        findings = analysis_data.get('findings', [])
        print(f"✅ Found {len(findings)} categories of transit data")
        
        for finding in findings:
            category = finding.get('category')
            confidence = finding.get('confidence', 0)
            keywords = finding.get('keywords_found', [])
            print(f"   - {category}: {confidence:.2f} confidence, keywords: {keywords}")
        
        return True
        
    except Exception as e:
        print(f"❌ Text processing error: {e}")
        return False
        
    finally:
        # Clean up
        if os.path.exists(text_file):
            os.unlink(text_file)


def test_gtfs_file_generation():
    """Test GTFS file generation"""
    print("\n🚌 Testing GTFS File Generation...")
    
    try:
        gtfs_gen = GTFSGeneratorService()
        
        # Create sample entities
        entities = {
            "agencies": [{
                "agency_id": "1",
                "agency_name": "Test Transit",
                "agency_url": "http://test.com",
                "agency_timezone": "America/New_York"
            }],
            "routes": [{
                "route_id": "1",
                "agency_id": "1",
                "route_short_name": "1",
                "route_long_name": "Test Route",
                "route_type": "3"
            }],
            "stops": [{
                "stop_id": "1",
                "stop_name": "Test Stop",
                "stop_lat": "40.7128",
                "stop_lon": "-74.0060"
            }]
        }
        
        # Test individual file generation
        agency_content = gtfs_gen._generate_agency_file(entities, None)
        print(f"✅ Agency file generated: {len(agency_content)} characters")
        
        routes_content = gtfs_gen._generate_routes_file(entities, None)
        print(f"✅ Routes file generated: {len(routes_content)} characters")
        
        stops_content = gtfs_gen._generate_stops_file(entities, None)
        print(f"✅ Stops file generated: {len(stops_content)} characters")
        
        # Test CSV content creation
        test_data = [{"id": "1", "name": "Test"}]
        csv_content = gtfs_gen._create_csv_content(["id", "name"], test_data)
        print(f"✅ CSV content creation working: {len(csv_content)} characters")
        
        # Test ZIP creation
        files_content = {
            "agency.txt": agency_content,
            "routes.txt": routes_content,
            "stops.txt": stops_content
        }
        zip_content = gtfs_gen._create_gtfs_zip(files_content)
        print(f"✅ ZIP file created: {len(zip_content)} bytes")
        
        return True
        
    except Exception as e:
        print(f"❌ GTFS generation error: {e}")
        return False


def test_ai_analyzer_methods():
    """Test AI analyzer methods in detail"""
    print("\n🤖 Testing AI Analyzer Methods...")
    
    try:
        analyzer = AIAnalyzer()
        
        # Test with transit-rich text
        transit_text = """
        Route 42 operates from Downtown Station to Airport Terminal.
        Stops include: Central Plaza, University Campus, Shopping Mall, Airport.
        Service runs every 20 minutes from 5:00 AM to 11:00 PM.
        Fare: $2.75 for adults, $1.50 for seniors.
        Operated by Metro Transit Authority.
        """
        
        # Test fallback analysis
        analysis = analyzer._fallback_analysis(transit_text)
        print(f"✅ Fallback analysis completed: {len(analysis)} characters")
        
        # Parse and display results
        import json
        analysis_data = json.loads(analysis)
        findings = analysis_data.get('findings', [])
        
        print(f"✅ Analysis found {len(findings)} categories:")
        for finding in findings:
            category = finding.get('category')
            confidence = finding.get('confidence', 0)
            keywords = finding.get('keywords_found', [])
            print(f"   - {category}: {confidence:.2f} confidence")
            print(f"     Keywords: {', '.join(keywords)}")
        
        # Test GTFS extraction
        gtfs_entities = analyzer._extract_gtfs_fallback(transit_text)
        print(f"✅ GTFS extraction completed: {list(gtfs_entities.keys())}")
        
        return True
        
    except Exception as e:
        print(f"❌ AI analyzer error: {e}")
        return False


def main():
    """Run comprehensive file processing tests"""
    print("🚀 Starting Comprehensive File Processing Tests\n")
    
    tests = [
        ("CSV Processing", test_csv_processing),
        ("Text Processing", test_text_processing),
        ("GTFS File Generation", test_gtfs_file_generation),
        ("AI Analyzer Methods", test_ai_analyzer_methods),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "="*60)
    print("📋 COMPREHENSIVE TEST SUMMARY")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All comprehensive tests passed!")
        print("📁 FileProcessorApp is fully functional and ready for production use!")
        print("\n📝 Next steps:")
        print("1. Run Django migrations: python manage.py makemigrations FileProcessorApp")
        print("2. Apply migrations: python manage.py migrate")
        print("3. Start Django server: python manage.py runserver")
        print("4. Visit /fileprocessor/ to start uploading files")
        print("5. Optional: Install Ollama for AI-powered analysis")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
    
    return passed == total


if __name__ == "__main__":
    main()
