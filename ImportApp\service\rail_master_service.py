from django.db import transaction
from RailApp.models import *


def rail_station_process_chunk(chunk):
    instances = []
    # Use transaction.atomic() to wrap the bulk_create operation in an atomic transaction
    with transaction.atomic():
        for i, row in chunk.iterrows():
            try:
                instance =RailStation(
                        id=row['StationId'],
                        name=row['Station'],
                        code=row['Code'],
                        latitude=row['Lat'],
                        longitude=row['Long'],
                        status=row['Status'],

                    )
                instances.append(instance)
            except Exception as e:
                                print(e)
        RailStation.objects.bulk_create(instances,batch_size=10000, ignore_conflicts=True)

    
def rail_route_process_chunk(chunk):
    instances = []
    # Use transaction.atomic() to wrap the bulk_create operation in an atomic transaction
    with transaction.atomic():
        for i, row in chunk.iterrows():
            try:
                instance = RailRoute(
                        id=row['RouteId'],
                        name=row['Name'],
                        line=row['Line'],
                        status=row['Status'],
                        direction=row['Route_Direction'],
                        is_main_route=row['IsMainRoute'],
                        description=row['Description']

                    )
                instances.append(instance)
            except Exception as e:
                                print(e)
        RailRoute.objects.bulk_create(instances,batch_size=10000, ignore_conflicts=True)


def rail_schedule_process_chunk(chunk):
    instances = []
    # Use transaction.atomic() to wrap the bulk_create operation in an atomic transaction
    with transaction.atomic():
        for i, row in chunk.iterrows():

            route_id = int(row['RouteId'])
            try:
                route_id_instance = RailRoute.objects.get(id=route_id)
                instance = RailSchedule(
                id = int(row['ScheduleId']),
                route_id=route_id_instance,
                start_time=row['StartTime'],
                end_time = row['EndTime'],
                train_no = row['TrainNo'],
                day_type = str(row['DayType']),
                train_type = row['TrainType'],
                status = row['Status'])

                instances.append(instance)
            except Exception as e:
                print(e)
        RailSchedule.objects.bulk_create(instances,batch_size=10000, ignore_conflicts=True)


def rail_schedule_point_process_chunk(chunk):
    instances = []
    with transaction.atomic():
        for i, row in chunk.iterrows():
            route_id = row['RouteId']
            station_id = str(row['StationId'])
            try:
                route_id_instance = RailRoute.objects.get(id=route_id)
                station_id_instance = RailStation.objects.get(id=station_id)
                RailSchedulePoint_instance = RailSchedulePoint(
                id=row['id'],
                route_id=route_id_instance,
                station_id=station_id_instance,
                schedule_id=row['schedule_id'],
                seq=row['seq'],
                start_time=row['start_time'],
                status=row['status'],
                platform_no=row['platform_no'])
                instances.append(RailSchedulePoint_instance)
            except (RailStation.DoesNotExist):
                print(RailStation.DoesNotExist)
        RailSchedulePoint.objects.bulk_create(instances,batch_size=10000, ignore_conflicts=True)



def rail_fare_distance_point_process_chunk(chunk):
    instances = []
    with transaction.atomic():
        for i, row in chunk.iterrows():
            # route_id = row['route_id']
            # schedule_id = row['schedule_id']
            try:
                # route_id_instance = RailRoute.objects.get(id=route_id)
                # schedule_id_instance = RailSchedulePoint.objects.get(id=schedule_id)
                RailFareDistancePoint_instance = RailFareDistancePoint(
                rail_fare_distance_point_id=row['rail_fare_distance_point_id'],
                route_id = row['route_id'],
                schedule_id = row['schedule_id'],
                seq = row['seq'],
                start_time = str(row['start_time']),
                distance = row['distance'],
                status = row['status'],
                service = row['service'])
                instances.append(RailFareDistancePoint_instance)
            except Exception as e:
                print(e)
        RailFareDistancePoint.objects.bulk_create(instances,batch_size=10000, ignore_conflicts=True)


def rail_fare_process_chunk(chunk):
    instances = []
    with transaction.atomic():
        for i, row in chunk.iterrows():
            try:
                RailFare_instance = RailFare(
                rail_fare_id = row['rail_fare_id'],
                from_distance = row['from_distance'],
                to_distance = row['to_distance'],
                adult_fare = row['adult_fare'],
                child_fare = row['child_fare'])
                instances.append(RailFare_instance)
            except Exception as e:
                print(e)
        RailFare.objects.bulk_create(instances,batch_size=10000, ignore_conflicts=True)


def rail_line_master_process_chunk(chunk):
    instances = []
    with transaction.atomic():
        for i, row in chunk.iterrows():
            try:
                # station_id_instance = RailStation.objects.get(id=station_id)
                RailLine_instance = RailLineMaster(
                id = row['id'],
                line = row['line'],
                station_id = row['station_id'],
                order = row['order'])
                instances.append(RailLine_instance)
            except Exception as e:
                print(e)
        RailLineMaster.objects.bulk_create(instances,batch_size=10000, ignore_conflicts=True)