
{% extends "admin/base.html" %}
{% load static %}
{% block content %}

<style>
    .loader {
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid #fff;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin-top: -8rem;
        }

        .loader_div {
            background-color: #000;
            display: flex;
            padding: 0 !important;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
            position: absolute;
            opacity: 0.15;
            z-index: 3;
            margin: 0;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        /* Hide content initially */
        .hidden {
            display: none;
        }
    
</style>

<div id="loader_div" class="loader_div">
    <div id="loader" class="loader">
    </div>
</div>

<script>
      document.addEventListener("DOMContentLoaded", () => {
        // Simulate an API request or any async operation
        setTimeout(() => {
            hideLoader();
            showContent();
        }, 3000); // Replace with your actual data loading logic and time

        function hideLoader() {
            const loader = document.getElementById("loader_div");
            loader.style.display = "none";
        }

        function showContent() {
            const content = document.getElementById("content");
            content.style.display = "block";
        }
    });
</script>

{% comment %} {{ block.super }} {% endcomment %}
{% endblock %}