# Generated by Django 4.0.6 on 2022-08-18 10:06

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='BusRoute',
            fields=[
                ('route_id', models.IntegerField(primary_key=True, serialize=False)),
                ('route_number', models.CharField(blank=True, max_length=250, null=True)),
                ('route_type_id', models.IntegerField(blank=True, null=True)),
                ('route_name', models.Char<PERSON>ield(blank=True, max_length=250, null=True)),
                ('status', models.Char<PERSON>ield(blank=True, max_length=250, null=True)),
                ('via', models.Char<PERSON>ield(blank=True, max_length=250, null=True)),
                ('description', models.CharField(blank=True, max_length=250, null=True)),
                ('deleted_status', models.IntegerField(blank=True, null=True)),
                ('route_direction', models.Char<PERSON><PERSON>(blank=True, max_length=50, null=True)),
                ('effective_from', models.<PERSON>r<PERSON><PERSON>(max_length=50)),
                ('effective_till', models.Char<PERSON><PERSON>(blank=True, max_length=50, null=True)),
                ('route_string', models.TextField(blank=True, null=True)),
                ('route_group', models.CharField(max_length=50)),
            ],
            options={
                'verbose_name_plural': 'Bus Route',
                'db_table': 'bus_route',
                'permissions': (('can_add_bus_route', 'Can add bus route'), ('can_change_bus_route', 'Can change bus route'), ('can_delete_bus_route', 'Can delete bus route'), ('can_view_bus_route', 'Can view bus route')),
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='BusRoutePoint',
            fields=[
                ('route_points_id', models.IntegerField(primary_key=True, serialize=False)),
                ('route_order', models.IntegerField(blank=True, null=True)),
                ('point_status', models.CharField(blank=True, max_length=20, null=True)),
                ('fare_stage', models.CharField(blank=True, max_length=5, null=True)),
                ('sub_stage', models.CharField(blank=True, max_length=5, null=True)),
                ('deleted_status', models.SmallIntegerField(blank=True, null=True)),
                ('travel_distance', models.IntegerField(blank=True, null=True)),
                ('travel_time', models.TimeField(blank=True, null=True)),
            ],
            options={
                'verbose_name_plural': 'Bus Route Point',
                'db_table': 'bus_route_point',
                'permissions': (('can_add_bus_route_point', 'Can add bus route point'), ('can_change_bus_route_point', 'Can change bus route point'), ('can_delete_bus_route_point', 'Can delete bus route point'), ('can_view_bus_route_point', 'Can view bus route point')),
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='BusScheduleDetails',
            fields=[
                ('schedule_details_id', models.AutoField(primary_key=True, serialize=False)),
                ('schedule_number', models.CharField(max_length=50)),
                ('trip_number', models.IntegerField()),
                ('distance', models.FloatField()),
                ('start_time', models.TimeField()),
                ('end_time', models.TimeField()),
                ('running_time', models.TimeField()),
                ('shift_type_name', models.CharField(max_length=50)),
                ('org_name', models.CharField(max_length=50)),
            ],
            options={
                'verbose_name_plural': 'Bus Schedule Details',
                'db_table': 'bus_schedule_details',
                'permissions': (('can_add_bus_schedule_details', 'Can add bus schedule details'), ('can_change_bus_schedule_details', 'Can change bus schedule details'), ('can_delete_bus_schedule_details', 'Can delete bus schedule details'), ('can_view_bus_schedule_details', 'Can view bus schedule details')),
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='BusServiceType',
            fields=[
                ('service_type_id', models.AutoField(primary_key=True, serialize=False)),
                ('service_type_name', models.CharField(blank=True, max_length=50, null=True)),
                ('abbreviation', models.CharField(blank=True, max_length=50, null=True)),
                ('status', models.CharField(blank=True, max_length=50, null=True)),
                ('deleted_status', models.IntegerField()),
                ('updated_by', models.IntegerField()),
                ('updated_date', models.DateTimeField(blank=True, null=True)),
                ('service_type_code', models.CharField(blank=True, max_length=10, null=True)),
                ('sync_updated_date', models.DateTimeField()),
            ],
            options={
                'verbose_name_plural': 'Bus Service Type',
                'db_table': 'bus_service_type',
                'permissions': (('can_add_bus_service_type', 'Can add bus service type'), ('can_change_bus_service_type', 'Can change bus service type'), ('can_delete_bus_service_type', 'Can delete bus service type'), ('can_view_bus_service_type', 'Can view bus service type')),
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='BusStop',
            fields=[
                ('bus_stop_id', models.BigIntegerField(primary_key=True, serialize=False)),
                ('bus_stop_name', models.CharField(blank=True, max_length=100, null=True)),
                ('bus_stop_code', models.CharField(blank=True, max_length=100, null=True)),
                ('status', models.CharField(blank=True, max_length=100, null=True)),
                ('landmark', models.CharField(blank=True, max_length=45, null=True)),
                ('latitude_current', models.DecimalField(blank=True, decimal_places=8, max_digits=12, null=True)),
                ('longitude_current', models.DecimalField(blank=True, decimal_places=8, max_digits=12, null=True)),
                ('fare_stage', models.CharField(blank=True, max_length=5, null=True)),
                ('sub_stage', models.CharField(max_length=5)),
                ('description', models.CharField(blank=True, max_length=100, null=True)),
                ('bmtc_status', models.CharField(blank=True, max_length=10, null=True)),
                ('route_status', models.CharField(max_length=1)),
                ('stop_type_id', models.IntegerField(blank=True, null=True)),
                ('stop_group_id', models.BigIntegerField(blank=True, null=True)),
                ('toll_zone', models.CharField(blank=True, max_length=1, null=True)),
            ],
            options={
                'verbose_name_plural': 'Bus Stop',
                'db_table': 'bus_stop',
                'permissions': (('can_add_bus_stop', 'Can add bus stop'), ('can_change_bus_stop', 'Can change bus stop'), ('can_delete_bus_stop', 'Can delete bus stop'), ('can_view_bus_stop', 'Can view bus stop')),
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='RouteVehicleMap',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('route_id', models.IntegerField()),
                ('route_number', models.CharField(max_length=250)),
                ('vehicle_number', models.CharField(max_length=50)),
                ('device_imei', models.CharField(max_length=15)),
                ('vstc_id', models.CharField(max_length=250)),
            ],
            options={
                'verbose_name_plural': 'Route Vehicle Map',
                'db_table': 'route_vehicle_map',
                'permissions': (('can_add_route_vehicle_map', 'Can add route vehicle map'), ('can_change_route_vehicle_map', 'Can change route vehicle map'), ('can_delete_route_vehicle_map', 'Can delete route vehicle map'), ('can_view_route_vehicle_map', 'Can view route vehicle map')),
                'managed': False,
            },
        ),
    ]
