from django.db import transaction
from BusApp.models import *




def bus_stop(chunk):
    instances = []
    with transaction.atomic():
        for i, row in chunk.iterrows():
            try:
                instance = BusStop(
                    bus_stop_id=row['bus_stop_id'],
                    bus_stop_name=row['bus_stop_name'],
                    bus_stop_code=row['bus_stop_code'],
                    # stop_direction=row['stop_direction'],
                    status=row['status'],
                    landmark=row['landmark'],
                    latitude_current=row['latitude_current'],
                    longitude_current=row['longitude_current'],
                    fare_stage=row['fare_stage'],
                    sub_stage=row['sub_stage'],
                    description=row['description'],
                    bmtc_status=row['bmtc_status'],
                    route_status=row['route_status'],
                    stop_type_id=row['stop_type_id'],
                    stop_group_id=row['stop_group_id'],
                    toll_zone=row['toll_zone'],
                    # toll_fee = row['toll_fee']
                )
                instances.append(instance)
            except Exception as e:
                print(e)
    BusStop.objects.bulk_create(instances, batch_size=10000, ignore_conflicts=True)


def bus_route(chunk):
    instances = []
    # print(instances)
    with transaction.atomic():
        for i, row in chunk.iterrows():
            route_id=int(row['route_id'])
            route_number = row['route_number']
            start_point_id = row ['start_point_id']
            end_point_id = row ['end_point_id']
            route_type_id = int(row['route_type_id'])
            route_name = str(row['route_name'])
            status = row['status']
            via = row['via']
            description = row['description']
            deleted_status = row['deleted_status']
            route_direction = row['route_direction']
            effective_from = row['effective_from']
            effective_till = row['effective_till']
            route_string = row['route_string']
            bus_service_type_id = row['bus_service_type_id']
            route_group = row['route_group']
            no_of_schedule = row['no_of_schedule']
            route_alias = row['route_alias']
            try:
                start_point_id = get_or_none(BusStop,bus_stop_id=start_point_id)
                end_point_id = get_or_none(BusStop,bus_stop_id=end_point_id)
                bus_service_type_id =get_or_none(BusServiceType,service_type_id=bus_service_type_id)
                # start_point_id_instance = BusStop.objects.get(bus_stop_id=start_point_id)
                # end_point_id_instance = BusStop.objects.get(bus_stop_id=end_point_id)
                # bus_service_type_id_instance =BusServiceType.objects.get(service_type_id=bus_service_type_id)
                instance = BusRoute(
                    route_id=int(row['route_id']),
                    route_number = row['route_number'],
                    start_point_id = start_point_id,
                    end_point_id = end_point_id,
                    route_type_id = int(row['route_type_id']),
                    route_name = str(row['route_name']),
                    status = row['status'],
                    via = row['via'],
                    description = row['description'],
                    deleted_status = row['deleted_status'],
                    route_direction = row['route_direction'],
                    effective_from = row['effective_from'],
                    effective_till = row['effective_till'],
                    route_string = row['route_string'],
                    bus_service_type_id = bus_service_type_id,
                    route_group = row['route_group'],
                    no_of_schedule = row['no_of_schedule'],
                    route_alias = row['route_alias']
                    )

                instances.append(instance)
                print(instances)
            except Exception as e:
                print(e)
        BusRoute.objects.bulk_create(instances,batch_size=10000, ignore_conflicts=True)



def bus_route_point(chunk):
    instances = []
    with transaction.atomic():
        for i, row in chunk.iterrows():
            route_points_id=row['route_points_id']
            route_id = row['route_id']
            bus_stop_id = int(row['bus_stop_id'])
            route_order=row['route_order']
            point_status=row['point_status']
            fare_stage=row['fare_stage']
            sub_stage=row['sub_stage']
            deleted_status=row['deleted_status']
            travel_distance=row['travel_distance']
            travel_time=row['travel_time']
            try:
                route_id_instance = BusRoute.objects.get(route_id=route_id)
                bus_stop_id_instance = BusStop.objects.get(bus_stop_id=bus_stop_id)
                busroutepoint_instance = BusRoutePoint(
                    route_points_id=row['route_points_id'],
                    route_id=route_id_instance,
                    bus_stop_id=bus_stop_id_instance,
                    route_order=row['route_order'],
                    point_status=row['point_status'],
                    fare_stage=row['fare_stage'],
                    sub_stage=row['sub_stage'],
                    deleted_status=row['deleted_status'],
                    travel_distance=row['travel_distance'],
                    travel_time=row['travel_time']
                )
                instances.append(busroutepoint_instance)
            except Exception as e:
                print(e)
        print("working")
        # BusRoutePoint.objects.bulk_create(instances,batch_size=10000, ignore_conflicts=True)


def bus_schedule_details(chunk):
    instances = []
    with transaction.atomic():
        for i, row in chunk.iterrows():
            schedule_details_id=int(row['schedule_details_id'])
            schedule_number = row['schedule_number']
            route_number_id = int(row['route_number_id'])
            trip_number = int(row['trip_number'])
            distance = row['distance']
            start_time = str(row['start_time'])
            end_time = str(row['end_time'])
            running_time = str(row['running_time'])
            shift_type_name = row['shift_type_name']
            org_name = row['org_name']
            day_type = row['day_type']
            # route_vehicle_map_id = int(row['route_vehicle_map_id'])
            try:
                route_number_id_instance = BusRoute.objects.get(route_id=route_number_id)
                BusScheduleDetails_instance = BusScheduleDetails(
                schedule_details_id=int(row['schedule_details_id']),
                schedule_number = row['schedule_number'],
                route_number_id = route_number_id_instance,
                trip_number = int(row['trip_number']),
                distance = row['distance'],
                start_time = str(row['start_time']),
                end_time = str(row['end_time']),
                running_time = str(row['running_time']),
                shift_type_name = row['shift_type_name'],
                org_name = row['org_name'],
                day_type = row['day_type'],
                # route_vehicle_map_id = int(row['route_vehicle_map_id'])
                )
                # print(BusScheduleDetails_instance)
                instances.append(BusScheduleDetails_instance)
            except Exception as e:
                print(e)
        BusScheduleDetails.objects.bulk_create(instances,batch_size=10000, ignore_conflicts=True)


def bus_fare(chunk):
    instances = []
    with transaction.atomic():
        for i, row in chunk.iterrows():
            route_id = row['route_id']
            start_point_id=row['start_point_id']
            end_point_id=row['end_point_id']

            try:
                # route_id_instance = BusRoute.objects.get(route_id=route_id)
                # # print(route_id_instance)
                # start_point_instance = BusStop.objects.get(bus_stop_id=start_point_id)
                # # print(start_point_instance)
                # end_point_instance = BusStop.objects.get(bus_stop_id=end_point_id)
                # print(end_point_instance)
                instance = BusFare(
                    fare_chart_id=row['fare_chart_id'],
                    route_id = row['route_id'],
                    # route_id = route_id_instance,
                    start_point_id=row['start_point_id'],
                    # start_point_id=start_point_instance,
                    end_point_id=row['end_point_id'],
                    # end_point_id=end_point_instance,
                    number_of_kms=row['number_of_kms'],
                    fare_amount=row['fare_amount'],
                    toll_fee=row['toll_fee'],
                    status=row['status']
                )
                instances.append(instance)
            except Exception as e:
                # # If error occurs, restore from backup
                # for backup_object in BusFareBackup.objects.all():
                #     BusFare.objects.create(**backup_object.__dict__)
                raise e
        BusFare.objects.bulk_create(instances,batch_size=10000, ignore_conflicts=True)


def Fare_chart_master(chunk):
    instances = []
    # Use transaction.atomic() to wrap the bulk_create operation in an atomic transaction
    with transaction.atomic():
        for i, row in chunk.iterrows():
            try:
                instance =FareChartMaster(
                    farechart_master_id=row['farechart_master_id'],
                    route_id=row['route_id'],
                    service_type_id=row['service_type_id'],
                    passenger_type_id=row['passenger_type_id'],
                    rate_master_id=row['rate_master_id'],
                    route_fare_map_id=row['route_fare_map_id'],
                    farechart_name=row['farechart_name'],
                    schedule_service=row['schedule_service'],
                    percentage=row['percentage'],
                    deleted_status=row['deleted_status'],
                    ceiling_fare=row['ceiling_fare'],
                    nignt_service=row['nignt_service'],
                    flexi_fare=row['flexi_fare']
                )
                instances.append(instance)
            except Exception as e:
                print(e)
                # print(BusRoute.DoesNotExist)
        FareChartMaster.objects.bulk_create(instances,batch_size=10000, ignore_conflicts=True)


def rate_master_details(chunk):
    instances = []
    # Use transaction.atomic() to wrap the bulk_create operation in an atomic transaction
    with transaction.atomic():
        for i, row in chunk.iterrows():
            try:
                instance = RateMasterDetails(
                rate_master_details_id=row['rate_master_details_id'],
                rate_master_id=row['rate_master_id'],
                service_type_id=row['service_type_id'],
                stage_no=row['stage_no'],
                adult=row['adult'],
                children=row['children'],
                senior_citizen=row['senior_citizen'],
                luggage=row['luggage'],
                happy_hour1=row['happy_hour1'],
                happy_hour2=row['happy_hour2'],
                deleted_status=row['deleted_status'],
                created_by=row['created_by'],
                created_date=str(row['created_date']),
                updated_by=row['updated_by'],
                updated_date=row['updated_date'],
                sync_updated_date=row['sync_updated_date']
                )
                instances.append(instance)
            except Exception as e:
                print(e)
        RateMasterDetails.objects.bulk_create(instances,batch_size=10000, ignore_conflicts=True)


def rate_master_table(chunk):
    instances = []
    # Use transaction.atomic() to wrap the bulk_create operation in an atomic transaction
    with transaction.atomic():
        for i, row in chunk.iterrows():
            try:
                instance = RateMasterTable(
                rate_master_id=row['rate_master_id'],
                parent_rate_master=row['parent_rate_master'],
                version_number=row['version_number'],
                version_number_service_stype=str(row['version_number_service_stype']),
                service_type_id=row['service_type_id'],
                effective_start_date=row['effective_start_date'],
                effective_end_date=row['effective_end_date'],
                status=row['status'],
                deleted_status=row['deleted_status'],
                created_by=row['created_by'],
                created_date=row['created_date'],
                updated_by=row['updated_by'],
                updated_date=row['updated_date'],
                sync_updated_date=row['sync_updated_date']
                )
                instances.append(instance)
            except Exception as e:
                print(e)
        RateMasterTable.objects.bulk_create(instances,batch_size=10000, ignore_conflicts=True)


def bus_fare2(chunk):
    instances = []
    # Use transaction.atomic() to wrap the bulk_create operation in an atomic transaction
    with transaction.atomic():
        for i, row in chunk.iterrows():
            # route_id = row['route_id']
            # farechart_master_id = row['farechart_master_id']
            # start_point_id=row['start_point_id']
            # end_point_id=row['end_point_id']
            try:
                # route_id_instance = BusRoute.objects.get(route_id=route_id)
                # farechart_master_id_instance = FareChartMaster.objects.get(farechart_master_id=farechart_master_id)
                # start_point_id_instance = BusStop.objects.get(bus_stop_id=start_point_id)
                # end_point_id_instance = BusStop.objects.get(bus_stop_id=end_point_id)
                instance = BusFare2(
                    fare_chart_id=row['fare_chart_id'],
                    # farechart_master_id=farechart_master_id_instance,
                    farechart_master_id = row['farechart_master_id'],
                    # route_id=route_id_instance,
                    route_id = row['route_id'],
                    service_type_id=row['service_type_id'],
                    passenger_type_id=row['passenger_type_id'],
                    schedule_type_id=row['schedule_type_id'],
                    # start_point_id=start_point_id_instance,
                    start_point_id=row['start_point_id'],
                    # end_point_id=end_point_id_instance,
                    end_point_id=row['end_point_id'],
                    number_of_kms=row['number_of_kms'],
                    fare_amount=row['fare_amount'],
                    toll_fee=row['toll_fee']
                )
                instances.append(instance)
            except Exception as e:
                transaction.rollback()
                print(e)
        BusFare2.objects.bulk_create(instances,batch_size=10000, ignore_conflicts=True)

def route_vehicle_map(chunk):
    instances = []
    # Use transaction.atomic() to wrap the bulk_create operation in an atomic transaction
    with transaction.atomic():
        for i, row in chunk.iterrows():
            try:
                instance = RouteVehicleMap(
                        id=row['id'],
                        route_id=row['route_id'],
                        route_number=row['route_number'],
                        vehicle_number=row['vehicle_number'],
                        device_imei=row['device_imei'],
                        vstc_id=row['vstc_id'],)
                instances.append(instance)
            except Exception as e:
                print(e)
        RouteVehicleMap.objects.bulk_create(instances,batch_size=10000, ignore_conflicts=True)



def process_chunk(chunk):
        instances = []
        # Use transaction.atomic() to wrap the bulk_create operation in an atomic transaction
        with transaction.atomic():
            for i, row in chunk.iterrows():
                # route_id = row['route_id']
                # farechart_master_id = row['farechart_master_id']
                # start_point_id=row['start_point_id']
                # end_point_id=row['end_point_id']
                try:
                    # route_id_instance = BusRoute.objects.get(route_id=route_id)
                    # farechart_master_id_instance = FareChartMaster.objects.get(farechart_master_id=farechart_master_id)
                    # start_point_id_instance = BusStop.objects.get(bus_stop_id=start_point_id)
                    # end_point_id_instance = BusStop.objects.get(bus_stop_id=end_point_id)
                    instance = BusFare2(
                        fare_chart_id=row['fare_chart_id'],
                        # farechart_master_id=farechart_master_id_instance,
                        farechart_master_id = row['farechart_master_id'],
                        # route_id=route_id_instance,
                        route_id = row['route_id'],
                        service_type_id=row['service_type_id'],
                        passenger_type_id=row['passenger_type_id'],
                        schedule_type_id=row['schedule_type_id'],
                        # start_point_id=start_point_id_instance,
                        start_point_id=row['start_point_id'],
                        # end_point_id=end_point_id_instance,
                        end_point_id=row['end_point_id'],
                        number_of_kms=row['number_of_kms'],
                        fare_amount=row['fare_amount'],
                        toll_fee=row['toll_fee']
                    )
                    instances.append(instance)
                except Exception as e:
                    transaction.rollback()
                    print(e)
            BusFare2.objects.bulk_create(instances,batch_size=10000, ignore_conflicts=True)




