# Generated by Django 4.0.6 on 2022-07-20 07:40

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('RailApp', '0001_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='railroute',
            options={'managed': False, 'permissions': (('can_add_rail_route', 'Can add rail route'), ('can_change_rail_route', 'Can change rail route'), ('can_delete_rail_route', 'Can delete rail route'), ('can_view_rail_route', 'Can view rail route'))},
        ),
        migrations.AlterModelOptions(
            name='railschedule',
            options={'managed': False, 'permissions': (('can_add_rail_schedule', 'Can add rail schedule'), ('can_change_rail_schedule', 'Can change rail schedule'), ('can_delete_rail_schedule', 'Can delete rail schedule'), ('can_view_rail_schedule', 'Can view rail schedule'))},
        ),
        migrations.AlterModelOptions(
            name='railschedulepoint',
            options={'managed': False, 'permissions': (('can_add_rail_schedule_point', 'Can add rail schedule point'), ('can_change_rail_schedule_point', 'Can change rail schedule point'), ('can_delete_rail_schedule_point', 'Can delete rail schedule point'), ('can_view_rail_schedule_point', 'Can view rail schedule point'))},
        ),
        migrations.AlterModelOptions(
            name='railstation',
            options={'managed': False, 'permissions': (('can_add_rail_station', 'Can add rail station'), ('can_change_rail_station', 'Can change rail station'), ('can_delete_rail_station', 'Can delete rail station'), ('can_view_rail_station', 'Can view rail station'))},
        ),
    ]
