# Generated by Django 4.0.6 on 2022-08-18 13:09

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='MetroFare',
            fields=[
                ('metro_fare_id', models.BigAutoField(primary_key=True, serialize=False)),
                ('amount', models.FloatField(blank=True, null=True)),
            ],
            options={
                'verbose_name_plural': 'Metro Fare',
                'db_table': 'metro_fare',
                'permissions': (('can_add_metro_fare', 'Can add metro fare'), ('can_change_metro_fare', 'Can change metro fare'), ('can_delete_metro_fare', 'Can delete metro fare'), ('can_view_metro_fare', 'Can view metro fare')),
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='MetroRoute',
            fields=[
                ('metro_route_id', models.BigAutoField(primary_key=True, serialize=False)),
                ('route_name', models.CharField(blank=True, max_length=100, null=True)),
            ],
            options={
                'verbose_name_plural': 'Metro Route',
                'db_table': 'metro_route',
                'permissions': (('can_add_metro_route', 'Can add metro route'), ('can_change_metro_route', 'Can change metro route'), ('can_delete_metro_route', 'Can delete metro route'), ('can_view_metro_route', 'Can view metro route')),
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='Station',
            fields=[
                ('station_id', models.BigAutoField(primary_key=True, serialize=False)),
                ('station_name', models.CharField(blank=True, max_length=150, null=True)),
                ('latitude', models.FloatField(blank=True, null=True)),
                ('longitude', models.FloatField(blank=True, null=True)),
                ('seq', models.IntegerField(blank=True, null=True)),
                ('is_cross', models.TextField()),
            ],
            options={
                'verbose_name_plural': 'Station',
                'db_table': 'station',
                'permissions': (('can_add_station', 'Can add station'), ('can_change_station', 'Can change station'), ('can_delete_station', 'Can delete station'), ('can_view_station', 'Can view station')),
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='Times',
            fields=[
                ('times_id', models.BigAutoField(primary_key=True, serialize=False)),
                ('diparture_time', models.TimeField(blank=True, null=True)),
                ('slot_identity', models.BigIntegerField(blank=True, null=True)),
                ('direction', models.CharField(blank=True, max_length=5, null=True)),
                ('day_type', models.IntegerField(blank=True, null=True)),
            ],
            options={
                'verbose_name_plural': 'Time',
                'db_table': 'times',
                'permissions': (('can_add_times', 'Can add times'), ('can_change_times', 'Can change times'), ('can_delete_times', 'Can delete times'), ('can_view_times', 'Can view times')),
                'managed': False,
            },
        ),
    ]
