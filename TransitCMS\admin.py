from django.contrib import admin
from CityApp.models import Cities
from django.urls import reverse_lazy
from django.utils.html import format_html
import django.conf as conf
from django.shortcuts import redirect
from django.urls import path

class TransitCMSAdmin(admin.AdminSite):
	site_version = ''

	"""Return a dictionary of variables to put in the template context for
		*every* page in the admin site."""
	def each_context(self, request):
		extra_context = super(TransitCMSAdmin, self).each_context(request)
		city1 = Cities.objects.exclude(id=8).all()
		extra_context['city1'] = city1
		extra_context['site_version'] = self.site_version
	
		if "db_name" in request.session.keys():
			db_name = request.session["db_name"]
			obj = Cities.objects.get(db_name = db_name)	
			request.session["city_name"] = obj.name
		return extra_context
	
	def selectCity(self, request, id: int):
		city = Cities.objects.filter(id=id).first()
		db_name = city.db_name
		city_name = city.name
		request.session["db_name"] = db_name
		request.session["city_name"] = city_name
		conf.settings.DATABASES['master']['NAME'] = db_name
		return redirect ('/admin/')
        # return redirect ('/admin/' % (city._meta.app_label, city._meta.model_name))

	def get_urls(self):
		urls = super().get_urls()
		my_urls = [
			path('selectCity/<int:id>', self.admin_view(self.selectCity), name="selectCity"),
			path('auth/group/selectCity/<int:id>', self.admin_view(self.selectCity), name="selectCity"),
			path('auth/group/add/selectCity/<int:id>', self.admin_view(self.selectCity), name="selectCity"),
			path('auth/user/selectCity/<int:id>', self.admin_view(self.selectCity), name="selectCity"),
			path('auth/user/add/selectCity/<int:id>/change/', self.admin_view(self.selectCity), name="selectCity"),



			
		]
		return my_urls + urls

	def select_city(self, obj):
		return format_html('<option value="{{ city.id }}">',
			reverse_lazy("admin:selectCity", args=[obj.id])
		)
