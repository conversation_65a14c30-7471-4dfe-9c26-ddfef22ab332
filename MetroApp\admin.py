from django.contrib import admin
import MetroApp.models
from django_admin_listfilter_dropdown.filters import DropdownFilter, ChoiceDropdownFilter, RelatedDropdownFilter
from django.urls import reverse
from django.utils.html import format_html
from django import forms
from django.contrib import messages
from django.http import HttpResponseRedirect
from django.urls import path
from CityApp.models import Cities
from django import conf
from django.shortcuts import render, redirect

# Register your models here.

class MetroFareAdmin(admin.ModelAdmin):
    list_display = ('metro_fare_id', 'amount', 'get_station_id_from', 'get_station_id_to')
    # readonly_fields = ('metro_fare_id', 'station_id_from', 'station_id_to')
    readonly_fields = ('metro_fare_id',)
    search_fields = ('station_id_from__station_name','station_id_to__station_name')
    ordering = ('metro_fare_id',)
    list_per_page = 10
    # list_filter = (
    #     ('station_id_from', RelatedDropdownFilter),
    #     ('station_id_to', RelatedDropdownFilter),
    # )

    def get_station_id_from(self, obj):
        name = obj.station_id_from.station_name
        idObj = obj.station_id_from.station_id
        url = reverse('admin:%s_%s_change' % (obj._meta.app_label,  obj.station_id_from._meta.model_name), args=[idObj])
        return format_html(u'<a href="%s">%s</a>' % (url,  name))
    get_station_id_from.short_description = 'Start Station Name'
   

    def get_station_id_to(self, obj):
        name = obj.station_id_to.station_name
        idObj = obj.station_id_to.station_id
        url = reverse('admin:%s_%s_change' % (obj._meta.app_label,  obj.station_id_to._meta.model_name), args=[idObj])
        return format_html(u'<a href="%s">%s</a>' % (url,  name))
    get_station_id_to.short_description = 'End Station Name'

    def add_view(self, request, extra_context=None):
        extra_context = {'title': 'Metro Fare'}
        try:
            return super(MetroFareAdmin, self).add_view(request, extra_context=extra_context)
        except Exception as e:
            print(e)
            messages.warning(request, 'Invalid Data')
            previous_url = request.META.get('HTTP_REFERER', reverse('admin:index'))
            return HttpResponseRedirect(previous_url)

    def changelist_view(self, request, extra_context=None):
        extra_context = {'title': 'Metro Fare' }
        try:
            return super(MetroFareAdmin, self).changelist_view(request, extra_context=extra_context)
        except Exception as e:
            message = e
            messages.add_message(request, messages.INFO,message)
            previous_url = request.META.get('HTTP_REFERER', reverse('admin:index'))
            return HttpResponseRedirect(previous_url)

    def selectCity(self, request, id: int):
        city = Cities.objects.filter(id=id).first()
        print(city.name)
        db_name = city.db_name
        city_name = city.name
        request.session["db_name"] = db_name
        request.session["city_name"] = city_name

        conf.settings.DATABASES['master']['NAME'] = db_name
        return redirect ('/admin/')


    def select_city(self, obj):
        return format_html('<option value="{{ city.id }}">',
            reverse_lazy("admin:selectCity", args=[obj.id])
            )
    
    def get_urls(self):
        my_urls = [
            path('selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),
            path('add/selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),
            path('change/selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),


        ]
        return my_urls + super().get_urls()
        
admin.site.register(MetroApp.models.MetroFare,MetroFareAdmin)

class MetroRouteAdmin(admin.ModelAdmin):
    list_display = ('metro_route_id', 'route_name', 'route_color_code', 'metro_operator', 'op_logo_url')
    # readonly_fields = ('metro_route_id', 'route_name')
    readonly_fields = ('metro_route_id',)
    search_fields = ('metro_route_id', 'route_name')
    ordering = ('metro_route_id',)
    list_per_page = 10
    list_filter = (
        ('metro_route_id', DropdownFilter),
        ('route_name', DropdownFilter),
    )

    def add_view(self, request, extra_context=None):
        extra_context = {'title': 'Metro Route'}
        try:
            return super(MetroRouteAdmin, self).add_view(request, extra_context=extra_context)
        except Exception as e:
            print(e)
            messages.warning(request, 'Invalid Data')
            previous_url = request.META.get('HTTP_REFERER', reverse('admin:index'))
            return HttpResponseRedirect(previous_url)

    def changelist_view(self, request, extra_context=None):
        extra_context = {'title': 'Metro Route' }
        try:
            return super(MetroRouteAdmin, self).changelist_view(request, extra_context=extra_context)
        except Exception as e:
            message = e
            messages.add_message(request, messages.INFO,message)
            previous_url = request.META.get('HTTP_REFERER', reverse('admin:index'))
            return HttpResponseRedirect(previous_url)
    
    def selectCity(self, request, id: int):
        city = Cities.objects.filter(id=id).first()
        print(city.name)
        db_name = city.db_name
        city_name = city.name
        request.session["db_name"] = db_name
        request.session["city_name"] = city_name

        conf.settings.DATABASES['master']['NAME'] = db_name
        return redirect ('/admin/')


    def select_city(self, obj):
        return format_html('<option value="{{ city.id }}">',
            reverse_lazy("admin:selectCity", args=[obj.id])
            )
    
    def get_urls(self):
        my_urls = [
            path('selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),
            path('add/selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),
            path('change/selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),


        ]
        return my_urls + super().get_urls()
        
admin.site.register(MetroApp.models.MetroRoute,MetroRouteAdmin)

class StationAdmin(admin.ModelAdmin):
    list_display = ('station_id', 'station_name', 'latitude', 'longitude', 'seq','metro_route_id', 'is_cross', 'cross_alias')
    readonly_fields = ('station_id', 'seq')
    search_fields = ('station_id','station_name', 'latitude', 'longitude')
    ordering = ('station_id',)
    list_per_page = 10
    list_filter = (
        ('station_id', DropdownFilter),
        ('station_name', DropdownFilter),
        # ('metro_route_id', RelatedDropdownFilter),
        ('is_cross', DropdownFilter),
    )

    def get_metro_route_id(self, obj):
        name = obj.metro_route.route_name
        idObj = obj.metro_route.metro_route_id
        print(idObj,name)
        url = reverse('admin:%s_%s_change' % (obj._meta.app_label,  obj.metro_route._meta.model_name), args=[idObj])
        return format_html(u'<a href="%s">%s</a>' % (url,  name))
    get_metro_route_id.short_description = 'Route Name'

    def add_view(self, request, extra_context=None):
        extra_context = {'title': 'Station'}
        try:
            return super(StationAdmin, self).add_view(request, extra_context=extra_context)
        except Exception as e:
            print(e)
            messages.warning(request, 'Invalid Data')
            previous_url = request.META.get('HTTP_REFERER', reverse('admin:index'))
            return HttpResponseRedirect(previous_url) 

    def changelist_view(self, request, extra_context=None):
        extra_context = {'title': 'Metro Stations' }
        try:
            return super(StationAdmin, self).changelist_view(request, extra_context=extra_context)
        except Exception as e:
            message = e
            messages.add_message(request, messages.INFO,message)
            previous_url = request.META.get('HTTP_REFERER', reverse('admin:index'))
            return HttpResponseRedirect(previous_url)
    
    def selectCity(self, request, id: int):
        city = Cities.objects.filter(id=id).first()
        print(city.name)
        db_name = city.db_name
        city_name = city.name
        request.session["db_name"] = db_name
        request.session["city_name"] = city_name

        conf.settings.DATABASES['master']['NAME'] = db_name
        return redirect ('/admin/')


    def select_city(self, obj):
        return format_html('<option value="{{ city.id }}">',
            reverse_lazy("admin:selectCity", args=[obj.id])
            )
    
    def get_urls(self):
        my_urls = [
            path('selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),
            path('add/selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),
            path('change/selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),


        ]
        return my_urls + super().get_urls()
    
    # def get_seq(self, request,extra_context=None):
    #     route_id=self.route_id
    #     # stations = super(StationAdmin, self).get_seq(request, extra_context=extra_context)
    #     file = StationAdmin.objects.get(route_id=route_id)
    #     print(file.count())
    #     # seq = super(StationAdmin, self).get_seq(request, extra_context=extra_context)
    #     # if seq is None:
    #     return super(StationAdmin, self).get_seq(request, extra_context=extra_context)
   


admin.site.register(MetroApp.models.Station,StationAdmin)

class TimesAdmin(admin.ModelAdmin):
    list_display = ('times_id', 'diparture_time', 'slot_identity', 'direction', 'day_type',   'station_id')
    readonly_fields = ('times_id','slot_identity')
    search_fields = ('station_id__station_name','day_type', 'diparture_time', 'station_id')
    ordering = ('times_id',)
    list_per_page = 10
    list_filter = (
        ('direction', DropdownFilter),
        ('day_type', DropdownFilter),
        # ('station_id', RelatedDropdownFilter),
    )

    def get_station_id(self, obj):
        name = obj.station.station_name
        idObj = obj.station.station_id
        url = reverse('admin:%s_%s_change' % (obj._meta.app_label,  obj.station._meta.model_name), args=[idObj])
        return format_html(u'<a href="%s">%s</a>' % (url,  name))
    get_station_id.short_description = 'Station Name'

    def add_view(self, request, extra_context=None):
        extra_context = {'title': 'Metro Time'}
        try:
            return super(TimesAdmin, self).add_view(request, extra_context=extra_context)
        except Exception as e:
            print(e)
            messages.warning(request, 'Invalid Data')
            previous_url = request.META.get('HTTP_REFERER', reverse('admin:index'))
            return HttpResponseRedirect(previous_url)

    def changelist_view(self, request, extra_context=None):
        extra_context = {'title': 'Metro Times' }
        try:
            return super(TimesAdmin, self).changelist_view(request, extra_context=extra_context)
        except Exception as e:
            message = e
            messages.add_message(request, messages.INFO,message)
            previous_url = request.META.get('HTTP_REFERER', reverse('admin:index'))
            return HttpResponseRedirect(previous_url)

    def selectCity(self, request, id: int):
        city = Cities.objects.filter(id=id).first()
        print(city.name)
        db_name = city.db_name
        city_name = city.name
        request.session["db_name"] = db_name
        request.session["city_name"] = city_name

        conf.settings.DATABASES['master']['NAME'] = db_name
        return redirect ('/admin/')


    def select_city(self, obj):
        return format_html('<option value="{{ city.id }}">',
            reverse_lazy("admin:selectCity", args=[obj.id])
            )
    
    def get_urls(self):
        my_urls = [
            path('selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),
            path('add/selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),
            path('change/selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),


        ]
        return my_urls + super().get_urls()
        
admin.site.register(MetroApp.models.Times,TimesAdmin)

