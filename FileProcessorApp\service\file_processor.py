import os
import logging
import json
from datetime import datetime
from typing import Dict, List, Any, Optional
from django.utils import timezone
from django.conf import settings

# File processing imports (will be conditionally imported)
try:
    import PyPDF2
    import pdfplumber
except ImportError:
    PyPDF2 = None
    pdfplumber = None

try:
    from PIL import Image
    import pytesseract
except ImportError:
    Image = None
    pytesseract = None

try:
    import pandas as pd
except ImportError:
    pd = None

try:
    from docx import Document
except ImportError:
    Document = None

from ..models import UploadedFile, ExtractedTransitData

logger = logging.getLogger(__name__)


class FileProcessorService:
    """Service for processing uploaded files and extracting transit data"""
    
    def __init__(self):
        self.supported_processors = {
            'pdf': self._process_pdf,
            'csv': self._process_csv,
            'xlsx': self._process_excel,
            'xls': self._process_excel,
            'jpg': self._process_image,
            'jpeg': self._process_image,
            'png': self._process_image,
            'tiff': self._process_image,
            'txt': self._process_text,
            'docx': self._process_docx,
            'doc': self._process_docx,
        }
    
    def process_file(self, uploaded_file: UploadedFile) -> bool:
        """
        Main method to process an uploaded file
        
        Args:
            uploaded_file: UploadedFile instance to process
            
        Returns:
            bool: True if processing was successful, False otherwise
        """
        try:
            # Update status to processing
            uploaded_file.processing_status = 'processing'
            uploaded_file.processing_started_at = timezone.now()
            uploaded_file.save()
            
            logger.info(f"Starting processing of file: {uploaded_file.original_filename}")
            
            # Get the appropriate processor
            file_extension = uploaded_file.file_extension
            processor = self.supported_processors.get(file_extension)
            
            if not processor:
                raise ValueError(f"No processor available for file type: {file_extension}")
            
            # Process the file
            extracted_text = processor(uploaded_file.file.path)
            
            # Store extracted text
            uploaded_file.extracted_text = extracted_text
            
            # Analyze with AI (if available)
            ai_analysis = self._analyze_with_ai(extracted_text, uploaded_file)
            uploaded_file.ai_analysis = ai_analysis
            
            # Extract structured transit data
            self._extract_transit_data(uploaded_file, extracted_text, ai_analysis)
            
            # Update status to completed
            uploaded_file.processing_status = 'completed'
            uploaded_file.processing_completed_at = timezone.now()
            uploaded_file.save()
            
            logger.info(f"Successfully processed file: {uploaded_file.original_filename}")
            return True
            
        except Exception as e:
            logger.error(f"Error processing file {uploaded_file.original_filename}: {str(e)}")
            
            # Update status to failed
            uploaded_file.processing_status = 'failed'
            uploaded_file.processing_error = str(e)
            uploaded_file.processing_completed_at = timezone.now()
            uploaded_file.save()
            
            return False
    
    def _process_pdf(self, file_path: str) -> str:
        """Extract text from PDF files"""
        text = ""
        
        try:
            # Try pdfplumber first (better for tables and complex layouts)
            if pdfplumber:
                with pdfplumber.open(file_path) as pdf:
                    for page in pdf.pages:
                        page_text = page.extract_text()
                        if page_text:
                            text += page_text + "\n"
                        
                        # Extract tables if present
                        tables = page.extract_tables()
                        for table in tables:
                            for row in table:
                                if row:
                                    text += "\t".join([cell or "" for cell in row]) + "\n"
            
            # Fallback to PyPDF2 if pdfplumber fails or is not available
            elif PyPDF2:
                with open(file_path, 'rb') as file:
                    pdf_reader = PyPDF2.PdfReader(file)
                    for page in pdf_reader.pages:
                        text += page.extract_text() + "\n"
            
            else:
                raise ImportError("No PDF processing library available")
                
        except Exception as e:
            logger.warning(f"Error processing PDF with primary method: {e}")
            # Try alternative method
            if PyPDF2 and pdfplumber:
                try:
                    with open(file_path, 'rb') as file:
                        pdf_reader = PyPDF2.PdfReader(file)
                        for page in pdf_reader.pages:
                            text += page.extract_text() + "\n"
                except Exception as e2:
                    logger.error(f"Error with fallback PDF processing: {e2}")
                    raise e2
            else:
                raise e
        
        return text.strip()
    
    def _process_csv(self, file_path: str) -> str:
        """Extract text from CSV files"""
        if not pd:
            raise ImportError("pandas is required for CSV processing")
        
        try:
            # Try different encodings
            encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']
            df = None
            
            for encoding in encodings:
                try:
                    df = pd.read_csv(file_path, encoding=encoding)
                    break
                except UnicodeDecodeError:
                    continue
            
            if df is None:
                raise ValueError("Could not read CSV file with any supported encoding")
            
            # Convert to text representation
            text = f"CSV File with {len(df)} rows and {len(df.columns)} columns\n\n"
            text += f"Columns: {', '.join(df.columns.tolist())}\n\n"
            text += df.to_string(max_rows=100)  # Limit to first 100 rows
            
            return text
            
        except Exception as e:
            logger.error(f"Error processing CSV file: {e}")
            raise e
    
    def _process_excel(self, file_path: str) -> str:
        """Extract text from Excel files"""
        if not pd:
            raise ImportError("pandas is required for Excel processing")
        
        try:
            # Read all sheets
            excel_file = pd.ExcelFile(file_path)
            text = f"Excel file with {len(excel_file.sheet_names)} sheets\n\n"
            
            for sheet_name in excel_file.sheet_names:
                df = pd.read_excel(file_path, sheet_name=sheet_name)
                text += f"Sheet: {sheet_name}\n"
                text += f"Rows: {len(df)}, Columns: {len(df.columns)}\n"
                text += f"Columns: {', '.join(df.columns.tolist())}\n"
                text += df.to_string(max_rows=50) + "\n\n"
            
            return text
            
        except Exception as e:
            logger.error(f"Error processing Excel file: {e}")
            raise e

    def _process_image(self, file_path: str) -> str:
        """Extract text from image files using OCR"""
        if not Image or not pytesseract:
            raise ImportError("PIL and pytesseract are required for image processing")

        try:
            # Open and process image
            image = Image.open(file_path)

            # Convert to RGB if necessary
            if image.mode != 'RGB':
                image = image.convert('RGB')

            # Extract text using OCR
            text = pytesseract.image_to_string(image)

            return text.strip()

        except Exception as e:
            logger.error(f"Error processing image file: {e}")
            raise e

    def _process_text(self, file_path: str) -> str:
        """Extract text from plain text files"""
        try:
            # Try different encodings
            encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']

            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as file:
                        return file.read()
                except UnicodeDecodeError:
                    continue

            raise ValueError("Could not read text file with any supported encoding")

        except Exception as e:
            logger.error(f"Error processing text file: {e}")
            raise e

    def _process_docx(self, file_path: str) -> str:
        """Extract text from Word documents"""
        if not Document:
            raise ImportError("python-docx is required for Word document processing")

        try:
            doc = Document(file_path)
            text = ""

            # Extract text from paragraphs
            for paragraph in doc.paragraphs:
                text += paragraph.text + "\n"

            # Extract text from tables
            for table in doc.tables:
                for row in table.rows:
                    row_text = []
                    for cell in row.cells:
                        row_text.append(cell.text.strip())
                    text += "\t".join(row_text) + "\n"

            return text.strip()

        except Exception as e:
            logger.error(f"Error processing Word document: {e}")
            raise e

    def _analyze_with_ai(self, extracted_text: str, uploaded_file: UploadedFile) -> str:
        """Analyze extracted text with AI to identify transit-related information"""
        try:
            # Try to use Ollama for local AI analysis
            from .ai_analyzer import AIAnalyzer
            analyzer = AIAnalyzer()
            return analyzer.analyze_transit_data(extracted_text)
        except ImportError:
            logger.warning("AI analyzer not available, using rule-based analysis")
            return self._rule_based_analysis(extracted_text)
        except Exception as e:
            logger.error(f"Error in AI analysis: {e}")
            return self._rule_based_analysis(extracted_text)

    def _rule_based_analysis(self, text: str) -> str:
        """Fallback rule-based analysis for transit data identification"""
        analysis = {
            "analysis_type": "rule_based",
            "timestamp": datetime.now().isoformat(),
            "findings": []
        }

        # Keywords for different transit data types
        keywords = {
            "routes": ["route", "line", "service", "bus", "train", "metro", "subway"],
            "stops": ["stop", "station", "terminal", "depot", "platform"],
            "schedules": ["schedule", "timetable", "departure", "arrival", "frequency"],
            "fares": ["fare", "price", "cost", "ticket", "payment"],
            "agencies": ["agency", "operator", "company", "authority", "transport"]
        }

        text_lower = text.lower()

        for category, words in keywords.items():
            found_keywords = [word for word in words if word in text_lower]
            if found_keywords:
                analysis["findings"].append({
                    "category": category,
                    "keywords_found": found_keywords,
                    "confidence": len(found_keywords) / len(words)
                })

        return json.dumps(analysis, indent=2)

    def _extract_transit_data(self, uploaded_file: UploadedFile, extracted_text: str, ai_analysis: str) -> None:
        """Extract and store structured transit data"""
        try:
            # Parse AI analysis
            analysis_data = json.loads(ai_analysis) if ai_analysis else {}

            # Extract different types of transit data based on analysis
            for finding in analysis_data.get("findings", []):
                category = finding.get("category")
                confidence = finding.get("confidence", 0.0)

                if confidence > 0.3:  # Only store data with reasonable confidence
                    # Create structured data entry
                    structured_data = self._create_structured_data(category, extracted_text, finding)

                    ExtractedTransitData.objects.create(
                        source_file=uploaded_file,
                        data_type=self._map_category_to_data_type(category),
                        confidence_score=confidence,
                        raw_data=finding,
                        structured_data=structured_data,
                        extraction_method="ai_analysis"
                    )

        except Exception as e:
            logger.error(f"Error extracting transit data: {e}")

    def _map_category_to_data_type(self, category: str) -> str:
        """Map analysis category to data type"""
        mapping = {
            "routes": "route",
            "stops": "stop",
            "schedules": "schedule",
            "fares": "fare",
            "agencies": "agency"
        }
        return mapping.get(category, "other")

    def _create_structured_data(self, category: str, text: str, finding: Dict) -> Dict:
        """Create structured data from extracted information"""
        # This is a simplified version - in a real implementation,
        # this would use more sophisticated parsing
        return {
            "category": category,
            "confidence": finding.get("confidence", 0.0),
            "keywords": finding.get("keywords_found", []),
            "text_sample": text[:500] + "..." if len(text) > 500 else text,
            "extraction_timestamp": datetime.now().isoformat()
        }
