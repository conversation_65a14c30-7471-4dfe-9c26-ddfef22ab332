
from django.db import models
from django.db import transaction,IntegrityError

# from bitfield import BitField




route_direction = (('Up','Up'),('Down', 'Down'))
route = (('local','local'),('airport','airport'))

class RailRoute(models.Model):
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=255)
    line = models.CharField(max_length=255, blank=True, null=True)
    status = models.CharField(max_length=50,choices=(('Active','Active'),('Inactive','Inactive')))
    service = models.CharField(max_length=50, blank=True, null=True,default='local', choices=route)
    direction = models.CharField(max_length=50, choices=route_direction)
    is_main_route = models.TextField(max_length=1,default='0',choices=(('0','0'),('1','1')))  # This field type is a guess.
    description = models.TextField(blank=True, null=True)

    class Meta: 
        managed = False
        db_table = 'rail_route'
        verbose_name_plural = 'Rail Route'
        # permissions = (
        #     ('can_add_rail_route', 'Can add rail route'),
        #     ('can_change_rail_route', 'Can change rail route'),
        #     ('can_delete_rail_route', 'Can delete rail route'),
        #     ('can_view_rail_route', 'Can view rail route'),
        # )

    def save(self, *args, **kwargs):
        if not self.id:
            last_instance = RailRoute.objects.last()
            self.id = last_instance.id + 1 if last_instance else 1
        super().save(*args, **kwargs)

    def __str__(self):
        return self.name
    

    

class RailSchedule(models.Model):
    id = models.BigAutoField(primary_key=True)
    # route_id = models.BigIntegerField()
    route_id = models.ForeignKey(RailRoute, related_name='railRouteId1', on_delete=models.CASCADE, blank=True, null=True, db_column='route_id')
    start_time = models.TimeField()
    end_time = models.TimeField()
    train_no = models.CharField(max_length=15)
    day_type = models.CharField(max_length=50, choices=(('Daily', 'Daily'),('Monday-Saturday','Monday-Saturday'),('Holiday-Sunday','Holiday-Sunday')))
    train_type = models.CharField(max_length=50,choices=(('Semi', 'Semi'),('Fast','Fast')))
    status = models.CharField(max_length=50,choices=(('Active', 'Active'),('Inactive','Inactive')))
    service = models.CharField(max_length=50, blank=True, null=True, default='local', choices=(('local', 'local'),('airport','airport')))

    class Meta:
        managed = False
        db_table = 'rail_schedule'
        verbose_name_plural = 'Rail Schedule'
        # permissions = (
        #     ('can_add_rail_schedule', 'Can add rail schedule'),
        #     ('can_change_rail_schedule', 'Can change rail schedule'),
        #     ('can_delete_rail_schedule', 'Can delete rail schedule'),
        #     ('can_view_rail_schedule', 'Can view rail schedule'),
        # )

    def save(self, *args, **kwargs):
        if not self.id:
            last_instance = RailSchedule.objects.last()
            self.id = last_instance.id + 1 if last_instance else 1
        super().save(*args, **kwargs)

class RailStation(models.Model):
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=255)
    latitude = models.FloatField()
    longitude = models.FloatField()
    status = models.CharField(max_length=50,choices=(('Active', 'Active'),('Inactive','Inactive')))
    service = models.CharField(max_length=50, blank=True, null=True,default='local', choices=(('local','local'),('airport','airport')))
    code = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'rail_station'
        verbose_name_plural = 'Rail Station'
        # permissions = (
        #     ('can_add_rail_station', 'Can add rail station'),
        #     ('can_change_rail_station', 'Can change rail station'),
        #     ('can_delete_rail_station', 'Can delete rail station'),
        #     ('can_view_rail_station', 'Can view rail station'),
        # )
    def save(self, *args, **kwargs):
        if not self.id:
            last_instance = RailStation.objects.last()
            self.id = last_instance.id + 1 if last_instance else 1
        super().save(*args, **kwargs)
        
    def __str__(self):
        return self.name

class RailSchedulePoint(models.Model):
    id = models.BigAutoField(primary_key=True)
    # route_id = models.BigIntegerField()
    route_id = models.ForeignKey(RailRoute, related_name='railRouteId2', on_delete=models.CASCADE, blank=True, null=True, db_column='route_id')
    # schedule_id = models.ForeignKey(RailSchedule, related_name='scheduleId', on_delete=models.CASCADE, blank=True, null=True, db_column='schedule_id')
    schedule_id = models.BigIntegerField()
    station_id = models.ForeignKey(RailStation, related_name='stationId', on_delete=models.CASCADE, blank=True, null=True, db_column='station_id')
    # station_id = models.BigIntegerField()
    seq = models.IntegerField(blank=True, null=True)
    start_time = models.TimeField()
    status = models.CharField(max_length=50,choices=(('Active','Active'),('Inactive','Inactive')))
    service = models.CharField(max_length=50, blank=True, null=True, default='local', choices=(('local','local'),('airport','airport')))
    platform_no = models.CharField(max_length=10, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'rail_schedule_point'
        verbose_name_plural = 'Rail Schedule Point'
        unique_together = ['schedule_id', 'seq'] 
        
        # permissions = (
        #     ('can_add_rail_schedule_point', 'Can add rail schedule point'),
        #     ('can_change_rail_schedule_point', 'Can change rail schedule point'),
        #     ('can_delete_rail_schedule_point', 'Can delete rail schedule point'),
        #     ('can_view_rail_schedule_point', 'Can view rail schedule point'),
        # )

    # def save(self, *args, **kwargs):
    #     if not self.id:
    #         last_instance = RailSchedulePoint.objects.last()
    #         self.id = last_instance.id + 1 if last_instance else 1
    #     # Check how many times the value of metro_route appears in other instances
    #     if self.route_id:
    #         count = RailSchedulePoint.objects.filter(route_id=self.route_id).exclude(pk=self.pk).count()
    #         count = count+1
    #         self.seq = count

    #     super().save(*args, **kwargs)
    
    def save(self, *args, **kwargs):
        with transaction.atomic():  # Ensure consistency
            if not self.id:
                last_instance = RailSchedulePoint.objects.last()
                self.id = last_instance.id + 1 if last_instance else 1

            if not self.seq and self.id:
                existing_seq = RailSchedulePoint.objects.filter(
                    id=self.id
                ).order_by('seq')

                if existing_seq.exists():
                    existing_seq.filter(seq__gte=self.seq).update(seq=F('seq') + 1)
                    self.seq = self.seq  
                else:
                    self.seq = 1 

            try:
                super(RailSchedulePoint, self).save(*args, **kwargs)
            except IntegrityError:
                print("Error: seq already exists for the Rail Schedule Id. Please choose a different seq  or update an existing record.")


class RailStationCross(models.Model):
    # id = models.AutoField(primary_key=True)
    line = models.CharField(max_length=100)
    # station_id = models.ManyToManyField(RailStation, related_name='station_Id1', blank=True, db_column='station_id', primary_key=True)
    station_id = models.BigIntegerField()
    is_cross = models.BooleanField(choices=(('t','t'),('f','f')), default='f')

    class Meta:
        managed = False
        db_table = 'rail_station_cross'
        verbose_name_plural = 'Rail Station Cross'

    def save(self, *args, **kwargs):
        if not self.id:
            last_instance = RailStationCross.objects.last()
            self.id = last_instance.id + 1 if last_instance else 1
        super().save(*args, **kwargs)


class RailLineMaster(models.Model):
    id = models.AutoField(primary_key=True)
    line = models.CharField(max_length=100)
    # station_id = models.ForeignKey(RailStation, related_name='station_Id', on_delete=models.CASCADE, blank=True, null=True, db_column='station_id')
    station_id = models.IntegerField()
    order = models.IntegerField()

    class Meta:
        managed = False
        db_table = 'rail_line_master'
        verbose_name_plural = 'Rail Line Master'

    def save(self, *args, **kwargs):
        if not self.id:
            last_instance = RailLineMaster.objects.last()
            self.id = last_instance.id + 1 if last_instance else 1
        super().save(*args, **kwargs)


class RailFare(models.Model):
    rail_fare_id = models.AutoField(primary_key=True)    
    from_distance = models.IntegerField()
    to_distance = models.IntegerField()
    adult_fare = models.IntegerField()
    child_fare = models.IntegerField()

    class Meta:
        managed = False
        db_table = 'rail_fare'
        verbose_name_plural = 'Rail Fare'

    def save(self, *args, **kwargs):
        if not self.id:
            last_instance = RailFare.objects.last()
            self.rail_fare_id = last_instance.rail_fare_id + 1 if last_instance else 1
        super().save(*args, **kwargs)

class RailFareDistancePoint(models.Model):
    rail_fare_distance_point_id = models.AutoField(primary_key=True)
    schedule_point_id = models.BigIntegerField()
    route_id = models.BigIntegerField()
    schedule_id = models.BigIntegerField()
    station_id = models.BigIntegerField()
    seq = models.IntegerField()
    start_time = models.TimeField()
    distance = models.IntegerField()
    status = models.CharField(max_length=50)
    service = models.CharField(max_length=100)

    class Meta:
        managed = False
        db_table = 'rail_fare_distance_point'
        verbose_name_plural = 'Rail Fare Distance Point'

    def save(self, *args, **kwargs):
        if not self.id:
            last_instance = RailFareDistancePoint.objects.last()
            self.rail_fare_distance_point_id = last_instance.rail_fare_distance_point_id + 1 if last_instance else 1
        super().save(*args, **kwargs)