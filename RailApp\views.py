from django.shortcuts import render,redirect
import folium
from django.urls import reverse_lazy
from django.utils.html import format_html
from geopy.geocoders import Nominatim
from folium import Map, FeatureGroup, Marker, LayerControl
from RailApp.models import *
from CityApp.models import *
from django.contrib import admin
from django import conf
from django.contrib.auth.decorators import login_required, permission_required

# Create your views here.


@login_required
def rail(request):
    name = request.session['city_name']
    city = Cities.objects.get(name=name)
    city1 = Cities.objects.exclude(id=8).filter(is_rail='t')
    routes = RailRoute.objects.all().filter(is_main_route=1,status='Active').order_by('is_main_route')
    # print(len(routes))
    stations = RailStation.objects.all()
    # print(stations)
    loc = (float(stations[0].latitude),float(stations[0].longitude))
    map = folium.Map(location=loc, zoom_start=12, height=500)
    Fullscreen = folium.plugins.Fullscreen(position='topright').add_to(map)
    markers=[]
    try:
        for route in routes:
            routeID=route.id
            route_name=route.name
            description=route.description
            schedule = RailSchedule.objects.filter(route_id=route.id,status='Active').first()
            # print(schedule.id)
            schedule_points = RailSchedulePoint.objects.filter(schedule_id=schedule.id,status='Active').order_by('seq')
            # print(schedule_points)
            # sch = RailSchedulePoint.objects.select_related('station_id').filter(route_id=route.id).order_by('seq','route_id').distinct("route_id")
            # sch2 = RailSchedulePoint.objects.order_by(seq=RailSchedulePoint.objectsvalues_list("id").select_related('station_id').filter(route_id=route.id).order_by('route_id').distinct("route_id"))
            # sta=RailStation.objects.filter(id__in=schedule_points)
            locations=[]
            for index,schedule_point in enumerate(schedule_points):
                stations = RailStation.objects.filter(id=schedule_point.station_id.id,status='Active')
                marker_group = folium.FeatureGroup(name=f"Rail_{index}", show=True)
                for station in stations:
                    name = station.name
                    id = station.id
                    coordinates = (station.latitude,station.longitude)
                    locations.append(coordinates)
                    if schedule_point.seq == 1:
                        folium.Marker(coordinates, tooltip=(id,name), icon=folium.Icon(color='green')).add_to(marker_group)
                    elif schedule_point.seq == len(schedule_points):
                        folium.Marker(coordinates, tooltip=(id,name), icon=folium.Icon(color='red')).add_to(marker_group)
                    else :
                        # folium.Marker(coordinates, popup=(id,name)).add_to(marker_group)
                        folium.CircleMarker(coordinates, tooltip=(id,name), radius=8,color='white', fill=True, fill_color='black', fill_opacity=0.7).add_to(marker_group)
                iframe = folium.IFrame('Route Id:' + str(routeID) + '<br>' + 'Route Name: ' + route_name + '<br>' + 'description: ' + description)
                popup = folium.Popup(iframe, min_width=250, max_width=280)
                folium.PolyLine(popup=popup,locations=locations,color='blue', weight=5).add_to(marker_group)
                map.add_child(marker_group)
            map.fit_bounds([locations])
        # # #                 #### select * from(select distinct on (rs.id) rs.id,rs.name,rs.latitude,rs.longitude,rs.status,rscp.route_id ,rscp.seq
        # from rail_schedule_point rscp
        # join rail_station rs on rscp.station_id = rs.id where rscp.route_id = 8 order by rs.id ) result order by seq
        # map.save("rail_map.html")
        context ={"site_title": admin.site.site_title, "site_version": admin.site.site_version,'map':map._repr_html_(),"routes":routes, "city1":city1, "city":city}
        return render(request,'admin/rail_map.html',context)
    except Exception as e:
        print(e)
        city1 = Cities.objects.exclude(id=8).filter(is_rail='t')
        stations = RailStation.objects.all()
        loc = (float(stations[0].latitude),float(stations[0].longitude))
        map = folium.Map(location=loc, zoom_start=12)
        Fullscreen = folium.plugins.Fullscreen(position='topright').add_to(map)
        # map.save("rail_map.html")
        context ={"site_title": admin.site.site_title, "site_version": admin.site.site_version,'map':map._repr_html_(),"routes":routes, "city1":city1, "city":city}
        return render(request,'admin/rail_map.html',context)


@login_required
def get_rail_route(request,rail_route_id):
    name = request.session['city_name']
    city = Cities.objects.get(name=name)
    city1 = Cities.objects.exclude(id=8).filter(is_rail='t')
    routes = RailRoute.objects.all().filter(is_main_route=1,status='Active').order_by('is_main_route')
    routeID = RailRoute.objects.get(id=rail_route_id)
    stations = RailStation.objects.all()
    loc = (float(stations[0].latitude),float(stations[0].longitude))
    map = folium.Map(location=loc, zoom_start=12, height=500)
    Fullscreen = folium.plugins.Fullscreen(position='topright').add_to(map)
    sch = RailSchedule.objects.filter(route_id=rail_route_id,status='Active').first()
    schedules=RailSchedulePoint.objects.filter(schedule_id=sch.id,status='Active').order_by('seq')

    try:
        locations=[]
        for index,schedule in enumerate(schedules):
            stations = RailStation.objects.filter(id=schedule.station_id.id,status='Active')
            rail_marker_group = folium.FeatureGroup(name=f"Rail_schedule_{index}", show=True)
            for station in stations:
                name = station.name
                id=station.id
                coordinates = (station.latitude,station.longitude)
                locations.append(coordinates)
                if schedule.seq == 1:
                    folium.Marker(coordinates, tooltip=(id,name), icon=folium.Icon(color='green')).add_to(rail_marker_group)
                elif schedule.seq == len(schedules):
                    folium.Marker(coordinates, tooltip=(id,name), icon=folium.Icon(color='red')).add_to(rail_marker_group)
                else :
                    # folium.Marker(coordinates, popup=(id,name)).add_to(rail_marker_group)
                    folium.CircleMarker(coordinates, tooltip=(id,name), radius=10,color='white', fill=True, fill_color='black', fill_opacity=0.7).add_to(rail_marker_group)
            folium.PolyLine(popup=f'{ routeID.id },{ routeID },{ routeID.description }',locations=locations,color='blue', weight=5).add_to(rail_marker_group)
            map.add_child(rail_marker_group)
            map.fit_bounds([locations])
        # map.save("rail_map.html")
        context ={"site_title": admin.site.site_title, "site_version": admin.site.site_version,'map':map._repr_html_(),"routes":routes,"city1":city1, "city":city}
        return render(request,'admin/rail_map.html',context)
    except Exception as e:
        city1 = Cities.objects.exclude(id=8).filter(is_rail='t')
        routes = RailRoute.objects.all().filter(is_main_route=1).order_by('is_main_route')
        stations = RailStation.objects.all()
        loc = (float(stations[0].latitude),float(stations[0].longitude))
        map = folium.Map(location=loc, zoom_start=12, height=500)
        Fullscreen = folium.plugins.Fullscreen(position='topright').add_to(map)
        map.save("rail_map.html")
        context ={"site_title": admin.site.site_title, "site_version": admin.site.site_version,'map':map._repr_html_(),"stations":stations,"routes":routes, "city1":city1, "city":city}
        return render(request,'admin/rail_map.html',context)


def selectCity(request, id: int):
        city = Cities.objects.filter(id=id).first()
        print(city.name)
        db_name = city.db_name
        city_name = city.name
        request.session["db_name"] = db_name
        request.session["city_name"] = city_name
        conf.settings.DATABASES['master']['NAME'] = db_name
        return redirect ('/rail/')
        # return redirect ('/admin/' % (city._meta.app_label, city._meta.model_name))

# def get_urls(self):
#     urls = super().get_urls()
#     my_urls = [
#         path('selectCity/<int:id>', self.admin_view(self.selectCity), name="selectCity"),
#     ]
#     return my_urls + urls

def select_city(self, obj):
    return format_html('<a href="{}" class="link">Select</a>',
        reverse_lazy("rail:selectCity", args=[obj.id])
        )



# class RailImport():

#     def rail_stations():
#         rail = RailStation.objects.all()
#         return count(rail)


#     def rail_route():
#         rail = RailRoute.objects.all()
#         return count(rail)