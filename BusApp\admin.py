from socket import fromfd
from urllib import request
from venv import create
from django.contrib import admin, messages
import BusApp.models
from django_admin_listfilter_dropdown.filters import DropdownFilter, ChoiceDropdownFilter, RelatedDropdownFilter
from django.urls import reverse
from django.utils.html import format_html
from django.http import HttpResponse, HttpResponseRedirect
from django import forms
from django.urls import path
from django.shortcuts import render, redirect
from django.db import connections
import csv
import logging
from import_export.admin import ImportExportModelAdmin
from django.db import OperationalError
from django.contrib import messages
from CityApp.models import Cities
from BusApp.models import *

from django import conf


logger = logging.getLogger(__name__)

class BusRouteAdmin(admin.ModelAdmin):
    list_display = (
        'route_id', 'route_number', 'get_start_point_name', 'get_end_point_name', 'route_type_id', 'route_name',
        'status',
        'via', 'description', 'deleted_status', 'route_direction', 'effective_from', 'effective_till', 'route_string',
        'get_bus_service_type', 'route_group')
    readonly_fields = ('route_id',)
    search_fields = ('route_id','route_number', 'route_name', 'via', 'description', 'route_direction')
    ordering = ('route_id',)
    list_per_page = 10
    list_max_show_all = 50
    list_filter = (
        ('route_id', DropdownFilter),
        ('route_number', DropdownFilter),
        # ('bus_service_type_id', DropdownFilter),
        # ('get_bus_service_type', ChoiceDropdownFilter),
        ('route_direction', DropdownFilter),
    )

    def get_start_point_name(self, obj):
        obj_vars = vars(obj)
        # print(obj_vars)
        # print("error:", obj)
        if obj != None:
            name = obj.start_point_id.bus_stop_name
            idObj = obj.start_point_id.bus_stop_id
            url = reverse('admin:%s_%s_change' % (obj._meta.app_label, obj.start_point_id._meta.model_name), args=[idObj])
            return format_html(u'<a href="%s">%s</a>' % (url, name))
            # return obj.start_point_id.bus_stop_name

    def get_end_point_name(self, obj):
        if obj != None:
            name = obj.end_point_id.bus_stop_name
            idObj = obj.end_point_id.bus_stop_id
            url = reverse('admin:%s_%s_change' % (obj._meta.app_label, obj.end_point_id._meta.model_name), args=[idObj])
            return format_html(u'<a href="%s">%s</a>' % (url, name))
        
        # return obj.end_point_id.bus_stop_name    

    def get_bus_service_type(self, obj):
        return obj.bus_service_type_id.service_type_name

    get_start_point_name.short_description = 'Start Point Name'
    get_end_point_name.short_description = 'End Point Name'
    get_bus_service_type.short_description = 'Bus Service Type'

    def add_view(self, request, extra_context=None):
        extra_context = {'title': 'Bus Route'}
        try:
            return super(BusRouteAdmin, self).add_view(request, extra_context=extra_context)
        except Exception as e:
            print(e)
            messages.warning(request, 'Invalid Data')
            previous_url = request.META.get('HTTP_REFERER', reverse('admin:index'))
            return HttpResponseRedirect(previous_url)

    def changelist_view(self, request, extra_context=None):
        extra_context = {'title': 'Bus Route'}
        try:
            return super(BusRouteAdmin, self).changelist_view(request, extra_context=extra_context)
        except Exception as e:
            print(e)
            messages.warning(request,'Bus Route  is Not Exist' )
            previous_url = request.META.get('HTTP_REFERER', reverse('admin:index'))
            return HttpResponseRedirect(previous_url)

    def selectCity(self, request, id: int):
        city = Cities.objects.filter(id=id).first()                    
        db_name = city.db_name
        city_name = city.name
        request.session["db_name"] = db_name
        request.session["city_name"] = city_name
        conf.settings.DATABASES['master']['NAME'] = db_name
        return redirect ('/admin/')


    def select_city(self, obj):
        return format_html('<option value="{{ city.id }}">',
            reverse_lazy("admin:selectCity", args=[obj.id])
            )
    
    def get_urls(self):
        my_urls = [
            path('selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),
            path('add/selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),
            path('change/selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),


        ]
        return my_urls + super().get_urls()


admin.site.register(BusApp.models.BusRoute, BusRouteAdmin)





class BusRoutePointAdmin(admin.ModelAdmin):

    list_display = (
        'route_points_id','route_id', 'get_route_number', 'get_bus_stop_name', 'route_order', 'point_status', 'fare_stage',
        'sub_stage', 'deleted_status', 'travel_distance', 'travel_time')
    # readonly_fields = ('route_points_id', 'route_id', 'bus_stop_id', 'route_order'),
    readonly_fields = ('route_points_id',)
    search_fields = ('route_points_id','route_id__route_number', 'bus_stop_id__bus_stop_name', 'point_status')
    ordering = ('route_points_id',)
    list_per_page = 10
    list_filter = (
        ('route_id__route_number', DropdownFilter),
        ('bus_stop_id', DropdownFilter),
    )


    

    def get_bus_stop_name(self, obj):
        name = obj.bus_stop_id.bus_stop_name
        idObj = obj.bus_stop_id.bus_stop_id
        url = reverse('admin:%s_%s_change' % (obj._meta.app_label, obj.bus_stop_id._meta.model_name), args=[idObj])
        return format_html(u'<a href="%s">%s</a>' % (url, name))

    # def get_bus_stop_action(self, obj):
    #     return format_html(u'<button type="button">View</button>') 'get_bus_stop_action',  
    # search_fields = ['foreign_key__related_fieldname'] (from the docs)

    def get_route_number(self, obj):
        name = obj.route_id.route_number
        idObj = obj.route_id.route_id
        url = reverse('admin:%s_%s_change' % (obj._meta.app_label, obj.route_id._meta.model_name), args=[idObj])
        return format_html(u'<a href="%s">%s</a>' % (url, name))

    get_bus_stop_name.short_description = "Bus stop"
    # get_bus_stop_action.short_description = 'Action'
    get_route_number.short_description = "Route Number"

    # def save_model(self, request, obj, form, change):
    # # Generate the route_order based on route_id
    #     route_id = obj.route_id
    #     last_order = BusApp.models.BusRoutePoint.objects.filter(route_id=route_id).order_by('-route_order').first()
        
    #     if last_order:
    #         last_route_order = last_order.route_order
    #         obj.route_order = last_route_order + 1
    #     else:
    #         # No previous route_order for this route_id, set route_order to 1 or any initial value
    #         obj.route_order = 1
        
    #     # Save the order
    #     super().save_model(request, obj, form, change)

    def selectCity(self, request, id: int):
        city = Cities.objects.filter(id=id).first()
        
        db_name = city.db_name
        city_name = city.name
        request.session["db_name"] = db_name
        request.session["city_name"] = city_name

        conf.settings.DATABASES['master']['NAME'] = db_name
        return redirect ('/admin/')


    def select_city(self, obj):
        return format_html('<option value="{{ city.id }}">',
            reverse_lazy("admin:selectCity", args=[obj.id])
            )
    
    def get_urls(self):
        my_urls = [
            path('selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),
            path('add/selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),
            path('change/selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),


        ]
        return my_urls + super().get_urls()

    def add_view(self, request, extra_context=None):
        try:
            routes = BusRoute.objects.all()
            stops = BusStop.objects.all()
            # points = BusRoutePoint.objects.filter(route_id=route_id).order_by('route_order')
            # print("stops",stops)
            extra_context = {'title': 'Bus Route Point', "routes":routes, "stops":stops}
            return super(BusRoutePointAdmin, self).add_view(request, extra_context=extra_context)
        except Exception as e:
            print(e)
            messages.warning(request, 'Invalid Data')
            previous_url = request.META.get('HTTP_REFERER', reverse('admin:index'))
            return HttpResponseRedirect(previous_url)

    def get_route_points(self, route_id):
        route = BusRoute.objects.get(route_id=route_id)
        route_points = BusRoutePoint.objects.filter(route_id=route.route_id)
        # name = obj.route_id.route_number
        # idObj = obj.route_id.route_id
        url = reverse('admin:%s_%s_change' % (route_id._meta.app_label, route.route_id._meta.model_name), args=[route_points])
        return format_html(u'<option value="%s">%s</a>' % (url, route_points))

    

    def changelist_view(self, request, extra_context=None):
        extra_context = {'title': 'Bus Route Point'}
        try:
            return super(BusRoutePointAdmin, self).changelist_view(request, extra_context=extra_context)
        except Exception as e:
            print(e)
            messages.warning(request, 'Bus Route Point  is Not Exist')
            previous_url = request.META.get('HTTP_REFERER', reverse('admin:index'))
            return HttpResponseRedirect(previous_url)   
    
    # def get_urls(self):
    #     urls = super().get_urls()
    #     new_uls = [path('bus-route-point-upload-csv/', self.bus_route_point_upload_csv),
    #                path('bus_route_point_downloaad-csv/', self.bus_route_point_download_csv)]
    #     return new_uls + urls
    


    # def get_urls(self):
    #     urls = super().get_urls()
    #     new_uls = [path('bus-route-point-upload-csv/', self.bus_route_point_upload_csv),
    #                path('bus_route_point_downloaad-csv/', self.bus_route_point_download_csv)]
    #     return new_uls + urls
    


admin.site.register(BusApp.models.BusRoutePoint, BusRoutePointAdmin)


class BusStopAdmin(admin.ModelAdmin):
    list_display = (
        'bus_stop_id', 'bus_stop_name', 'bus_stop_code', 'status', 'landmark', 'latitude_current', 'longitude_current',
        'fare_stage', 'sub_stage', 'description', 'bmtc_status', 'route_status', 'stop_type_id', 'stop_group_id',
        'toll_zone')
    # readonly_fields = ('bus_stop_id', 'stop_type_id', 'stop_group_id',)
    readonly_fields = ('bus_stop_id',)
    search_fields = ('bus_stop_id','bus_stop_name', 'bus_stop_code', 'latitude_current', 'longitude_current','landmark')
    ordering = ('bus_stop_id',)
    list_per_page = 10
    list_filter = (
        ('bus_stop_id', DropdownFilter),
        ('bus_stop_name', DropdownFilter),
        ('status', DropdownFilter),
    )

    def add_view(self, request, extra_context=None):
        extra_context = {'title': 'Bus Stop'}
        try:
            return super(BusStopAdmin, self).add_view(request, extra_context=extra_context)
        except Exception as e:
            print(e)
            messages.warning(request, 'Invalid Data')
            previous_url = request.META.get('HTTP_REFERER', reverse('admin:index'))
            return HttpResponseRedirect(previous_url)

    def changelist_view(self, request, extra_context=None):
        extra_context = {'title': 'Bus Stop'}
        try:
            return super(BusStopAdmin, self).changelist_view(request, extra_context=extra_context)
        except Exception as e:
            print(e)
            messages.warning(request, 'Bus Stop  is Not Exist')
            previous_url = request.META.get('HTTP_REFERER', reverse('admin:index'))
            return HttpResponseRedirect(previous_url)
        
    def selectCity(self, request, id: int):
        city = Cities.objects.filter(id=id).first()
        try:
            db_name = city.db_name
            city_name = city.name
            request.session["db_name"] = db_name
            request.session["city_name"] = city_name

            conf.settings.DATABASES['master']['NAME'] = db_name
            return redirect ('/admin/')
        except Exception as e:
            print(e)
            return redirect ('/admin/',"That Specific City Database not Exist")

    def select_city(self, obj):
        return format_html('<option value="{{ city.id }}">',
            reverse_lazy("admin:selectCity", args=[obj.id])
            )
    
    def get_urls(self):
        my_urls = [
            path('selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),
            path('add/selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),
            path('change/selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),


        ]
        return my_urls + super().get_urls()

admin.site.register(BusApp.models.BusStop, BusStopAdmin)


class RouteVehicleMapIdField(forms.ModelChoiceField):
    def label_from_instance(self, route_vehicle_map_id):
        label = f"{route_vehicle_map_id.route_number} - {route_vehicle_map_id.vehicle_number}"
        return label


class RouteNumberIdField(forms.ModelChoiceField):
    def label_from_instance(self, route_number_id):
        label = f"{route_number_id.route_id} - {route_number_id.route_number}"
        return label


class BusScheduleDetailsForm(forms.ModelForm):
    def __init__(self, *arg, **kwargs):
        super(BusScheduleDetailsForm, self).__init__(*arg, **kwargs)
        self.fields['running_time'] = forms.TimeField(widget=forms.TimeInput(format="%H:%M:%S"))

    class Meta:
        model = BusApp.models.BusScheduleDetails
        fields = ['running_time', 'route_number_id', 
                #   'route_vehicle_map_id'
                  ]
        field_classes = {
            'route_number_id': RouteNumberIdField,
            # 'route_vehicle_map_id': RouteVehicleMapIdField
        }

    def add_error(self, field, error):
        # print(field, error)
        if field:
            logger.info('Form error on field %s: %s', field, error)
        else:
            logger.info('Form error: %s', error)
        super().add_error(field, error)
    

class BusScheduleDetails(admin.ModelAdmin):
    form = BusScheduleDetailsForm
    list_display = (
        'schedule_details_id', 'schedule_number', 'find_route_number_id', 'trip_number', 'distance', 'start_time',
        'end_time', 'format_running_time', 'shift_type_name', 'day_type',
        # 'route_vehicle_map_id',
        #   'find_route_vehicle_map_id'
          )
    readonly_fields = ('schedule_details_id',)
    search_fields = ('schedule_details_id','schedule_number', 'find_route_number_id',)
    ordering = ('schedule_details_id',)
    fields = ('schedule_number', 'route_number_id', 'trip_number', 'distance', 'start_time', 'end_time', 'running_time',
              'shift_type_name', 'day_type',
            #   'route_vehicle_map_id',
              )
    list_per_page = 10
    list_filter = (
        ('shift_type_name', DropdownFilter),
        ('day_type', DropdownFilter),)

    def find_route_number_id(self, obj):
        name = obj.route_number_id.route_number
        idObj = obj.route_number_id.route_id
        url = reverse('admin:%s_%s_change' % (obj._meta.app_label, obj.route_number_id._meta.model_name), args=[idObj])
        return format_html(u'<a href="%s">%s</a>' % (url, name))

    find_route_number_id.short_description = "Route Number"

    def add_view(self, request, extra_context=None):
        extra_context = {'title': 'Bus Schedule Details'}
        try:
            return super(BusScheduleDetails, self).add_view(request, extra_context=extra_context)
        except Exception as e:
            print(e)
            messages.warning(request, 'Invalid Data')
            previous_url = request.META.get('HTTP_REFERER', reverse('admin:index'))
            return HttpResponseRedirect(previous_url)

    # def find_route_vehicle_map_id(self, obj):
        # url = reverse('admin:%s_%s_change' % (obj._meta.app_label, obj.route_vehicle_map_id._meta.model_name),
        #               args=[obj.route_vehicle_map_id.id])
        # return format_html(u'<a href="%s">%s - %s</a>' % (
        #     url, obj.route_vehicle_map_id.route_number, obj.route_vehicle_map_id.vehicle_number))
        # url = reverse('admin:%s_%s_change' % (obj._meta.app_label, obj._meta.model_name), args=[obj.id])
        # return format_html(u'<a href="%s">%s - %s</a>' % (url, obj.route_number, obj.vehicle_number))

    # find_route_vehicle_map_id.short_description = 'Route Vehicle Map'
    def selectCity(self, request, id: int):
        city = Cities.objects.filter(id=id).first()
        
        db_name = city.db_name
        city_name = city.name
        request.session["db_name"] = db_name
        request.session["city_name"] = city_name

        conf.settings.DATABASES['master']['NAME'] = db_name
        return redirect ('/admin/')


    def select_city(self, obj):
        return format_html('<option value="{{ city.id }}">',
            reverse_lazy("admin:selectCity", args=[obj.id])
            )
    
    def get_urls(self):
        my_urls = [
            path('selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),
            path('add/selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),
            path('change/selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),


        ]
        return my_urls + super().get_urls()
    

    def format_running_time(self, obj):
        return obj.running_time.strftime("%H:%M:%S")[0:12]

    format_running_time.short_description = 'Running Time'

    def changelist_view(self, request, extra_context=None):
        extra_context = {'title': 'Bus Schedule Detail'}
        try:
            return super(BusScheduleDetails, self).changelist_view(request, extra_context=extra_context)
        except Exception as e:
            print(e)
            messages.warning(request, 'Bus Schedule Details  is Not Exist')
            previous_url = request.META.get('HTTP_REFERER', reverse('admin:index'))
            return HttpResponseRedirect(previous_url)

admin.site.register(BusApp.models.BusScheduleDetails, BusScheduleDetails)


class BusServiceType(admin.ModelAdmin):
    list_display = (
        'service_type_id', 'service_type_name', 'abbreviation', 'status', 'deleted_status', 'updated_by',
        'updated_date',
        'service_type_code', 'sync_updated_date')
    readonly_fields = ('service_type_id',)
    # readonly_fields = ('service_type_id', 'service_type_code')
    search_fields = ('service_type_id', 'service_type_name', 'abbreviation', 'service_type_code',)
    ordering = ('service_type_id',)
    fields = (
        'service_type_id', 'service_type_name', 'abbreviation', 'status', 'deleted_status', 'updated_by',
        'updated_date',
        'service_type_code', 'sync_updated_date')

    list_per_page = 10

    def add_view(self, request, extra_context=None):
        extra_context = {'title': 'Bus Service Type'}
        try:
            return super(BusServiceType, self).add_view(request, extra_context=extra_context)
        except Exception as e:
            print(e)
            messages.warning(request, 'Invalid Data')
            previous_url = request.META.get('HTTP_REFERER', reverse('admin:index'))
            return HttpResponseRedirect(previous_url)


    def changelist_view(self, request, extra_context=None):
        extra_context = {'title': 'Bus Service Type'}
        try:
            return super(BusServiceType, self).changelist_view(request, extra_context=extra_context)
        except Exception as e:
            print(e)
            messages.warning(request, 'Bus Service Type  is Not Exist')
            previous_url = request.META.get('HTTP_REFERER', reverse('admin:index'))
            return HttpResponseRedirect(previous_url)

    def selectCity(self, request, id: int):
        city = Cities.objects.filter(id=id).first()
        
        db_name = city.db_name
        city_name = city.name
        request.session["db_name"] = db_name
        request.session["city_name"] = city_name

        conf.settings.DATABASES['master']['NAME'] = db_name
        return redirect ('/admin/')


    def select_city(self, obj):
        return format_html('<option value="{{ city.id }}">',
            reverse_lazy("admin:selectCity", args=[obj.id])
            )
    
    def get_urls(self):
        my_urls = [
            path('selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),
            path('add/selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),
            path('change/selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),


        ]
        return my_urls + super().get_urls()

admin.site.register(BusApp.models.BusServiceType, BusServiceType)   

class BusFareAdmin(admin.ModelAdmin):
    list_display = (
        'fare_chart_id', 'route_id', 'start_point_id', 'end_point_id', 'number_of_kms', 'fare_amount',
        'toll_fee',
        'status')
    readonly_fields = ('fare_chart_id',)
    # readonly_fields = ('service_type_id', 'service_type_code')
    search_fields = ('fare_chart_id', 'route_id','start_point_id', 'end_point_id')
    ordering = ('fare_chart_id',)
    fields = (
        'fare_chart_id', 'route_id', 'start_point_id', 'end_point_id', 'number_of_kms', 'fare_amount',
        'toll_fee',
        'status')

    list_per_page = 10

    def add_view(self, request, extra_context=None):
        extra_context = {'title': 'Bus Fare'}
        try:
            return super(BusFareAdmin, self).add_view(request, extra_context=extra_context)
        except Exception as e:
            print(e)
            messages.warning(request, 'Invalid Data')
            previous_url = request.META.get('HTTP_REFERER', reverse('admin:index'))
            return HttpResponseRedirect(previous_url)

    def selectCity(self, request, id: int):
        city = Cities.objects.filter(id=id).first()
        
        db_name = city.db_name
        city_name = city.name
        request.session["db_name"] = db_name
        request.session["city_name"] = city_name

        conf.settings.DATABASES['master']['NAME'] = db_name
        return redirect ('/admin/')


    def select_city(self, obj):
        return format_html('<option value="{{ city.id }}">',
            reverse_lazy("admin:selectCity", args=[obj.id])
            )
    
    def get_urls(self):
        my_urls = [
            path('selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),
            path('add/selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),
            path('change/selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),


        ]
        return my_urls + super().get_urls()
    

    def changelist_view(self, request, extra_context=None):
        extra_context = {'title': 'Fare Chart'}
        try:
            return super(BusFareAdmin, self).changelist_view(request, extra_context=extra_context)
        except Exception as e:
            print(e)
            messages.warning(request, 'Fare Chart  is Not Exist')
            previous_url = request.META.get('HTTP_REFERER', reverse('admin:index'))
            return HttpResponseRedirect(previous_url)

admin.site.register(BusApp.models.BusFare, BusFareAdmin)

class BusFare2Admin(admin.ModelAdmin):

    list_display = (
        'fare_chart_id', 'farechart_master_id', 'route_id', 'service_type_id', 'passenger_type_id', 'schedule_type_id', 'start_point_id', 'end_point_id', 'number_of_kms', 'fare_amount',
        'toll_fee',
        )
    readonly_fields = ('fare_chart_id',)
    # readonly_fields = ('service_type_id', 'service_type_code')
    search_fields = ('fare_chart_id','farechart_master_id', 'route_id', 'service_type_id', 'passenger_type_id', 'schedule_type_id', 'start_point_id', 'end_point_id')
    ordering = ('fare_chart_id',)
    fields = (
        'fare_chart_id','farechart_master_id', 'route_id', 'service_type_id', 'passenger_type_id', 'schedule_type_id', 'start_point_id', 'end_point_id', 'number_of_kms', 'fare_amount',
        'toll_fee',
        )

    list_per_page = 10

    def add_view(self, request, extra_context=None):
        extra_context = {'title': 'Fare Chart'}
        try:
            return super(BusFare2Admin, self).add_view(request, extra_context=extra_context)
        except Exception as e:
            print(e)
            messages.warning(request, 'Invalid Data')
            previous_url = request.META.get('HTTP_REFERER', reverse('admin:index'))
            return HttpResponseRedirect(previous_url)

    def selectCity(self, request, id: int):
        city = Cities.objects.filter(id=id).first()
        
        db_name = city.db_name
        city_name = city.name
        request.session["db_name"] = db_name
        request.session["city_name"] = city_name

        conf.settings.DATABASES['master']['NAME'] = db_name
        return redirect ('/admin/')


    def select_city(self, obj):
        return format_html('<option value="{{ city.id }}">',
            reverse_lazy("admin:selectCity", args=[obj.id])
            )
    
    def get_urls(self):
        my_urls = [
            path('selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),
            path('add/selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),
            path('change/selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),


        ]
        return my_urls + super().get_urls()

    def changelist_view(self, request, extra_context=None):
        extra_context = {'title': 'Fare Chart'}
        try:
            return super(BusFare2Admin, self).changelist_view(request, extra_context=extra_context)
        except Exception as e:
            print(e)
            messages.warning(request, 'Fare Chart  is Not Exist')
            previous_url = request.META.get('HTTP_REFERER', reverse('admin:index'))
            return HttpResponseRedirect(previous_url)
        
admin.site.register(BusApp.models.BusFare2, BusFare2Admin)




class RouteVehicleMapAdmin(admin.ModelAdmin):
    list_display = ('id', 'route_id', 'route_number', 'vehicle_number', 'device_imei', 'vstc_id')
    readonly_fields = ('route_id', 'route_number',)
    search_fields = ('route_id', 'route_number', 'vehicle_number', 'device_imei')
    ordering = ('route_id',)
    list_per_page = 10
    list_filter = (
        ('route_id', DropdownFilter),
        ('route_number', DropdownFilter),
        ('vehicle_number', DropdownFilter),
    )

    def add_view(self, request, extra_context=None):
        extra_context = {'title': 'RouteVehicleMap'}
        try:
            return super(RouteVehicleMapAdmin, self).add_view(request, extra_context=extra_context)
        except Exception as e:
            print(e)
            messages.warning(request, 'Invalid Data')
            previous_url = request.META.get('HTTP_REFERER', reverse('admin:index'))
            return HttpResponseRedirect(previous_url)

    def selectCity(self, request, id: int):
        city = Cities.objects.filter(id=id).first()
        
        db_name = city.db_name
        city_name = city.name
        request.session["db_name"] = db_name
        request.session["city_name"] = city_name

        conf.settings.DATABASES['master']['NAME'] = db_name
        return redirect ('/admin/')


    def select_city(self, obj):
        return format_html('<option value="{{ city.id }}">',
            reverse_lazy("admin:selectCity", args=[obj.id])
            )
    
    def get_urls(self):
        my_urls = [
            path('selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),
            path('add/selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),
            path('change/selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),


        ]
        return my_urls + super().get_urls()

    def changelist_view(self, request, extra_context=None):
        extra_context = {'title': 'Route Vehicle Map'}
        try:
            return super(RouteVehicleMapAdmin, self).changelist_view(request, extra_context=extra_context)
        except Exception as e:
            print(e)
            messages.warning(request, 'Route Vehicle Map  is Not Exist')
            previous_url = request.META.get('HTTP_REFERER', reverse('admin:index'))
            return HttpResponseRedirect(previous_url)


admin.site.register(BusApp.models.RouteVehicleMap, RouteVehicleMapAdmin)


class FareChartMasterAdmin(admin.ModelAdmin):
    list_display = ('farechart_master_id', 'route_id', 'route_name', 'service_type_id', 'passenger_type_id', 'rate_master_id', 'route_fare_map_id', 'farechart_name', 'schedule_service', 'percentage', 'deleted_status', 'ceiling_fare', 'nignt_service', 'flexi_fare')
    readonly_fields = ('route_id',)
    search_fields = ('route_id', 'farechart_master_id', 'route_name', 'passenger_type_id', 'rate_master_id', 'route_fare_map_id',)
    ordering = ('route_id',)
    list_per_page = 10
    list_filter = (
        ('route_id', DropdownFilter),
        
    )

    def selectCity(self, request, id: int):
        city = Cities.objects.filter(id=id).first()
        
        db_name = city.db_name
        city_name = city.name
        request.session["db_name"] = db_name
        request.session["city_name"] = city_name

        conf.settings.DATABASES['master']['NAME'] = db_name
        return redirect ('/admin/')


    def select_city(self, obj):
        return format_html('<option value="{{ city.id }}">',
            reverse_lazy("admin:selectCity", args=[obj.id])
            )
    
    def get_urls(self):
        my_urls = [
            path('selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),
            path('add/selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),
            path('change/selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),


        ]
        return my_urls + super().get_urls()

    def add_view(self, request, extra_context=None):
        extra_context = {'title': 'Fare Chart Master'}
        try:
            return super(FareChartMasterAdmin, self).add_view(request, extra_context=extra_context)
        except Exception as e:
            print(e)
            messages.warning(request, 'Invalid Data')
            previous_url = request.META.get('HTTP_REFERER', reverse('admin:index'))
            return HttpResponseRedirect(previous_url)

    def changelist_view(self, request, extra_context=None):
        extra_context = {'title': 'Fare Chart Master'}
        try:
            return super(FareChartMasterAdmin, self).changelist_view(request, extra_context=extra_context)
        except Exception as e:
            print(e)
            messages.warning(request, 'Fare Chart Master  is Not Exist')
            previous_url = request.META.get('HTTP_REFERER', reverse('admin:index'))
            return HttpResponseRedirect(previous_url)
        
admin.site.register(BusApp.models.FareChartMaster,FareChartMasterAdmin)



class RateMasterDetailsAdmin(admin.ModelAdmin):
    list_display = ('rate_master_details_id', 'rate_master_id', 'service_type_id', 'stage_no', 'adult', 'children', 'senior_citizen', 'luggage', 'happy_hour1', 'happy_hour2', 'deleted_status', 'created_by', 'created_date', 'updated_by', 'updated_date', 'sync_updated_date')
    readonly_fields = ('rate_master_details_id',)
    search_fields = ('rate_master_details_id', 'rate_master_id', 'service_type_id', 'stage_no', 'senior_citizen',)
    ordering = ('rate_master_details_id',)
    list_per_page = 10
    list_filter = (
        ('rate_master_details_id', DropdownFilter),
        
    )

    def selectCity(self, request, id: int):
        city = Cities.objects.filter(id=id).first()
        
        db_name = city.db_name
        city_name = city.name
        request.session["db_name"] = db_name
        request.session["city_name"] = city_name

        conf.settings.DATABASES['master']['NAME'] = db_name
        return redirect ('/admin/')


    def select_city(self, obj):
        return format_html('<option value="{{ city.id }}">',
            reverse_lazy("admin:selectCity", args=[obj.id])
            )
    
    def get_urls(self):
        my_urls = [
            path('selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),
            path('add/selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),
            path('change/selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),


        ]
        return my_urls + super().get_urls()

    def add_view(self, request, extra_context=None):
        extra_context = {'title': 'Rate Master Details'}
        try:
            return super(RateMasterDetailsAdmin, self).add_view(request, extra_context=extra_context)
        except Exception as e:
            print(e)
            messages.warning(request, 'Invalid Data')
            previous_url = request.META.get('HTTP_REFERER', reverse('admin:index'))
            return HttpResponseRedirect(previous_url)

    def changelist_view(self, request, extra_context=None):
        extra_context = {'title': 'Rate Master Details'}
        try:
            return super(RateMasterDetailsAdmin, self).changelist_view(request, extra_context=extra_context)
        except Exception as e:
            print(e)
            messages.warning(request, 'Rate Master Details  is Not Exist')
            previous_url = request.META.get('HTTP_REFERER', reverse('admin:index'))
            return HttpResponseRedirect(previous_url)
        
admin.site.register(BusApp.models.RateMasterDetails,RateMasterDetailsAdmin)



class RateMasterTableAdmin(admin.ModelAdmin):
    list_display = ('rate_master_id', 'parent_rate_master', 'version_number', 'version_number_service_stype', 'service_type_id', 'effective_start_date', 'effective_end_date', 'status', 'deleted_status', 'created_by', 'created_date', 'updated_by', 'updated_date', 'sync_updated_date')
    readonly_fields = ('rate_master_id',)
    search_fields = ('rate_master_id', 'parent_rate_master', 'version_number', 'version_number_service_stype', 'service_type_id',)
    ordering = ('rate_master_id',)
    list_per_page = 10
    list_filter = (
        ('rate_master_id', DropdownFilter),
        
    )

    def selectCity(self, request, id: int):
        city = Cities.objects.filter(id=id).first()         
        db_name = city.db_name
        city_name = city.name
        request.session["db_name"] = db_name
        request.session["city_name"] = city_name

        conf.settings.DATABASES['master']['NAME'] = db_name
        return redirect ('/admin/')


    def select_city(self, obj):
        return format_html('<option value="{{ city.id }}">',
            reverse_lazy("admin:selectCity", args=[obj.id])
            )
    
    def get_urls(self):
        my_urls = [
            path('selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),
            path('add/selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),
            path('change/selectCity/<int:id>', self.admin_site.admin_view(self.selectCity), name="selectCity"),


        ]
        return my_urls + super().get_urls()

    def add_view(self, request, extra_context=None):
        extra_context = {'title': 'RateMasterTable'}
        try:
            return super(RateMasterTableAdmin, self).add_view(request, extra_context=extra_context)
        except Exception as e:
            print(e)
            messages.warning(request, 'Invalid Data')
            previous_url = request.META.get('HTTP_REFERER', reverse('admin:index'))
            return HttpResponseRedirect(previous_url)

    def changelist_view(self, request, extra_context=None):
        extra_context = {'title': 'Rate Master Table'}
        try:
            return super(RateMasterTableAdmin, self).changelist_view(request, extra_context=extra_context)
        except Exception as e:
            print(e)
            messages.warning(request, 'Rate Master Table  is Not Exist')
            previous_url = request.META.get('HTTP_REFERER', reverse('admin:index'))
            return HttpResponseRedirect(previous_url)
        
admin.site.register(BusApp.models.RateMasterTable,RateMasterTableAdmin)
