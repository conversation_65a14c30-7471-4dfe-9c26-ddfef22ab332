# from django.db import models
# from django.contrib.admin.models import LogEntry, ADDITION, DELETION
# from django.contrib.contenttypes.models import ContentType
# from django.contrib.auth.models import User
# import logging
# import coloredlogs

# # Configure coloredlogs
# coloredlogs.install(level='DEBUG')
# #
# logger = logging.getLogger(__name__)

# class Imports(models.Model):
#     module = models.CharField(max_length=250)
#     module_type = models.CharField(max_length=250)
#     service = models.Char<PERSON>ield(max_length=250, blank=True, null=True)
#     file_path = models.FileField(max_length=500, upload_to="imports")
#     city_name = models.Char<PERSON>ield(max_length=250)
#     user_agent = models.CharField(max_length=250)
#     user_id = models.IntegerField(null=True, blank=True)
#     created_at = models.DateTimeField(auto_now_add=True)  

    

#     def create_custom_log_entry(self, user, action_flag, change_message):
#         content_type = ContentType.objects.get_for_model(self)
#         LogEntry.objects.create(
#             user_id=user.id,
#             content_type_id=content_type.id,
#             object_id=self.id,
#             object_repr=str(self),
#             action_flag=action_flag,
#             change_message=change_message
#         )

#     def save(self, *args, **kwargs):
#         is_new = not self.pk
#         logger.info('Model instance saved: %s', self)
#         super(Imports, self).save(*args, **kwargs)

#         if is_new:
#             change_message = f"Custom log entry for addition: {self.module}, {self.module_type}, {self.city_name}"
#             user = User.objects.get(pk=self.user_id)
#             self.create_custom_log_entry(user, ADDITION, change_message)
    
#     def delete(self, *args, **kwargs):
#         change_message = f"Custom log entry for deletion: {self.module}, {self.module_type}, {self.city_name}"
#         user = User.objects.get(pk=self.user_id)
#         self.create_custom_log_entry(user, DELETION, change_message)
#         logger.info('Model instance deleted: %s', self)
#         super(Imports, self).delete(*args, **kwargs)


#     class Meta:
#         managed = False
#         db_table = 'imports'
#         verbose_name_plural = 'Imports'
