{% extends "admin/base_site.html" %}
{% load i18n %}
{% comment %} {% block userlinks %}
  {% url 'django-admindocs-docroot' as docsroot %}{% if docsroot %}<a href="{{ docsroot }}">{% translate 'Documentation' %}</a> / {% endif %}{% translate 'Change password' %} /
  <form id="logout-form" method="post" action="{% url 'admin:logout' %}">
    {% csrf_token %}
    <button type="submit">{% translate 'Log out' %}</button>
  </form>
  {% include "admin/color_theme_toggle.html" %}
{% endblock %} {% endcomment %}
<div class="topnav" id="grad1">
  
  <a href="/" ><img src="{% static 'tummoc-logo.png' %}" height="50px" style="vertical-align: middle;"/><span style="font-size:30px;">{{ site_title }}</span><br><span class="version">Version - {{ site_version }}</span></a>

 
  <div class="topnav-right">
    <div>
{% if user.get_username %}
{% block userlinks %}
  <a href="javascript:void()" style="text-align: unset;">
    <span>Welcome, {{  user.get_username }}</span> &nbsp;

    <span>{% if request.session.city_name %}City - {{ request.session.city_name }}{% endif %}</span>
  </a>
  {% comment %} {{block.super}} {% endcomment %}
  <a href="{% url 'password_change'%}">Change Password</a>
  <a href="{% url 'logout'%}">Logout</a> 
  {% endblock %}  
{% endif %}
</div>
</div>
</div>
{% block breadcrumbs %}
<div class="breadcrumbs">
<a href="{% url 'admin:index' %}">{% translate 'Home' %}</a>
&rsaquo; {% translate 'Password change' %}
</div>
{% endblock %}

{% block content %}
<p>{% translate 'Your password was changed.' %}</p>
{% endblock %}
