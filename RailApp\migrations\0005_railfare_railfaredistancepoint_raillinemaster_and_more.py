# Generated by Django 4.2.3 on 2023-11-06 12:09

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('RailApp', '0004_alter_railroute_options_alter_railschedule_options_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='RailFare',
            fields=[
                ('rail_fare_id', models.AutoField(primary_key=True, serialize=False)),
                ('from_distance', models.IntegerField()),
                ('to_distance', models.IntegerField()),
                ('adult_fare', models.IntegerField()),
                ('child_fare', models.IntegerField()),
            ],
            options={
                'verbose_name_plural': 'Rail Fare',
                'db_table': 'rail_fare',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='RailFareDistancePoint',
            fields=[
                ('rail_fare_distance_point_id', models.AutoField(primary_key=True, serialize=False)),
                ('schedule_point_id', models.BigIntegerField()),
                ('route_id', models.BigIntegerField()),
                ('schedule_id', models.BigIntegerField()),
                ('station_id', models.BigIntegerField()),
                ('seq', models.IntegerField()),
                ('start_time', models.TimeField()),
                ('distance', models.IntegerField()),
                ('status', models.CharField(max_length=50)),
                ('service', models.CharField(max_length=100)),
            ],
            options={
                'verbose_name_plural': 'Rail Fare Distance Point',
                'db_table': 'rail_fare_distance_point',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='RailLineMaster',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('line', models.CharField(max_length=100)),
                ('station_id', models.BigIntegerField()),
                ('order', models.IntegerField()),
            ],
            options={
                'verbose_name_plural': 'Rail Line Master',
                'db_table': 'rail_line_master',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='RailStationCross',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('line', models.CharField(max_length=100)),
                ('station_id', models.BigIntegerField()),
                ('is_cross', models.TextField(choices=[('t', 't'), ('f', 'f')], default='f')),
            ],
            options={
                'verbose_name_plural': 'Rail Station Cross',
                'db_table': 'rail_station_cross',
                'managed': False,
            },
        ),
    ]
