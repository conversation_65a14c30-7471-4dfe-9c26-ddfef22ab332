from django.db import transaction
from MetroApp.models import *


def times_process_chunk(chunk):
    t_instance=[]
    with transaction.atomic():
        for i, row in time_station.iterrows():
            route_name = row['route_name']
            # print(route_name)
            time = row['time']
            print(time)
            slot = row['slot']
            direction = row['direction']
            Day_type = row['Day_type']
            station_name = row["station_name"]
            route_id_instance = MetroRoute.objects.get(route_name=route_name)

            station = Station.objects.get(station_name=station_name,
                                            metro_route_id=route_id_instance.metro_route_id)
            time_instance = Times(diparture_time=time, slot_identity=slot, direction=direction,
                                    day_type=Day_type, station_id=station.station_id)
            t_instance.append(time_instance)

        Times.objects.bulk_create(t_instance,batch_size=10000, ignore_conflicts=True)


def metro_fare_process_chunk(chunk):
    instance = []
    with transaction.atomic():
        for i, row in stations_fare_format.iterrows():
            fare_amount = row['fare_amount']
            station_id_from = row['station_id_from']
            station_id_to = row['station_id_to']

            station_from_instance = Station.objects.get(station_id=station_id_from)
            station_to_instance = Station.objects.get(station_id=station_id_to)
            fare_instance = MetroFare(amount=fare_amount, station_id_from=station_from_instance,
                                    station_id_to=station_to_instance)
            instance.append(fare_instance)
        MetroFare.objects.bulk_create(instance, batch_size=10000, ignore_conflicts=True)
